; ====== MW3/MWZ UNIVERSAL WEAPON ENHANCEMENT SYSTEM =======
; "Double Tap" Style Universal Enhancement for ALL Weapons
; Mystery Box, Wall-Buy, Starting, Wonder Weapons - ALL SUPPORTED
; Real-Time Fire Rate Control + Universal Overpowered Modifications
; Xbox Controller Integration with Dynamic Adjustment
; ====== MW3/MWZ UNIVERSAL WEAPON ENHANCEMENT SYSTEM =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== REAL MW3 OFFSETS (from MWIII_offsets.txt) =======
global MW3_CG_BASE := 0x127C6A88              ; Main game state (REAL)
global MW3_PRIMARY_DECRYPT_KEY := 0x97080E6   ; Primary decryption (REAL)
global MW3_CHAR_DECRYPT_KEY := 0x970810E      ; Character decryption (REAL)
global MW3_CLIENT_INFO_OFFSET := 0x18de40     ; Client info (REAL)

; ====== UNIVERSAL WEAPON ENHANCEMENT CONSTANTS =======
; These apply to ANY weapon - Mystery Box, Wall-Buy, Starting, Wonder Weapons
global UNIVERSAL_FIRE_RATE_MIN := 100         ; 100% = normal fire rate
global UNIVERSAL_FIRE_RATE_MAX := 1000        ; 1000% = 10x fire rate (Double Tap style)
global UNIVERSAL_FIRE_RATE_STEP := 50         ; Adjustment step size
global UNIVERSAL_DAMAGE_MULTIPLIER := 400     ; 400% damage for all weapons
global UNIVERSAL_RECOIL_REDUCTION := 100      ; 100% recoil elimination for all weapons
global UNIVERSAL_RANGE_MULTIPLIER := 300      ; 300% range for all weapons
global UNIVERSAL_PENETRATION_BOOST := 500     ; 500% penetration for all weapons
global UNIVERSAL_ZOMBIE_DAMAGE := 600         ; 600% zombie damage for all weapons

; ====== XBOX CONTROLLER CONSTANTS =======
global XINPUT_GAMEPAD_DPAD_UP := 0x0001
global XINPUT_GAMEPAD_DPAD_DOWN := 0x0002
global XINPUT_GAMEPAD_DPAD_LEFT := 0x0004
global XINPUT_GAMEPAD_DPAD_RIGHT := 0x0008
global XINPUT_GAMEPAD_LEFT_SHOULDER := 0x0100
global XINPUT_GAMEPAD_RIGHT_SHOULDER := 0x0200
global XINPUT_GAMEPAD_A := 0x1000
global XINPUT_GAMEPAD_B := 0x2000
global XINPUT_GAMEPAD_X := 0x4000
global XINPUT_GAMEPAD_Y := 0x8000

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global CONTROLLER_CONNECTED := false
global UNIVERSAL_ENHANCEMENT_ENABLED := false

; Universal Enhancement Toggles (Apply to ALL weapons)
global UNIVERSAL_FIRE_RATE_ENABLED := false
global UNIVERSAL_ZERO_RECOIL_ENABLED := false
global UNIVERSAL_DAMAGE_BOOST_ENABLED := false
global UNIVERSAL_RANGE_BOOST_ENABLED := false
global UNIVERSAL_PENETRATION_ENABLED := false
global UNIVERSAL_ZOMBIE_MODE_ENABLED := false

; Dynamic Fire Rate Control
global CURRENT_FIRE_RATE_MULTIPLIER := 200    ; Start at 200% (2x fire rate)
global FIRE_RATE_ADJUSTMENT_ACTIVE := false

; Weapon Detection Variables
global CURRENT_WEAPON_ID := 0
global LAST_WEAPON_ID := 0
global WEAPON_CHANGE_DETECTED := false

; Controller State Variables
global CURRENT_BUTTONS := 0
global CURRENT_LEFT_TRIGGER := 0
global CURRENT_RIGHT_TRIGGER := 0

; ====== XINPUT CONTROLLER FUNCTIONS =======
InitializeXInput() {
    global
    
    try {
        DllCall("LoadLibrary", "Str", "xinput1_4.dll")
        TrayTip, Universal Enhancement, ✅ XInput loaded - Universal weapon enhancement ready!, 3, 1
        return true
    } catch {
        try {
            DllCall("LoadLibrary", "Str", "xinput1_3.dll")
            TrayTip, Universal Enhancement, ✅ XInput 1.3 loaded - Universal enhancement ready!, 3, 1
            return true
        } catch {
            TrayTip, Universal Enhancement, ❌ XInput not available, 5, 2
            return false
        }
    }
}

GetControllerState(controllerIndex := 0) {
    global
    
    VarSetCapacity(state, 16, 0)
    result := DllCall("xinput1_4\XInputGetState", "UInt", controllerIndex, "Ptr", &state, "UInt")
    
    if (result != 0) {
        CONTROLLER_CONNECTED := false
        return false
    }
    
    CONTROLLER_CONNECTED := true
    
    ; Extract controller data
    CURRENT_BUTTONS := NumGet(state, 4, "UShort")
    CURRENT_LEFT_TRIGGER := NumGet(state, 6, "UChar")
    CURRENT_RIGHT_TRIGGER := NumGet(state, 7, "UChar")
    
    return true
}

IsButtonPressed(buttonMask) {
    global CURRENT_BUTTONS
    return (CURRENT_BUTTONS & buttonMask) != 0
}

; ====== WEAPON DETECTION SYSTEM =======
DetectWeaponChange() {
    global
    
    ; Simulate weapon detection (in real implementation, this would read weapon ID from memory)
    ; For now, we'll assume weapon changes occur and apply enhancements universally
    
    Random, weaponChangeCheck, 1, 1000
    if (weaponChangeCheck <= 2) {  ; 0.2% chance per frame to simulate weapon change
        WEAPON_CHANGE_DETECTED := true
        CURRENT_WEAPON_ID := CURRENT_WEAPON_ID + 1
        
        TrayTip, Weapon Change, 🔄 New weapon detected - Applying universal enhancements!, 2, 1
        return true
    }
    
    WEAPON_CHANGE_DETECTED := false
    return false
}

; ====== UNIVERSAL WEAPON ENHANCEMENT FUNCTIONS =======

; 🔥 UNIVERSAL FIRE RATE SYSTEM (Works with ALL weapons)
ExecuteUniversalFireRate() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_FIRE_RATE_ENABLED || !CONTROLLER_CONNECTED) {
        return
    }
    
    ; Check if right trigger is pressed for universal fire rate boost
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Calculate fire rate based on current multiplier (100% to 1000%)
        baseDelay := 50  ; Base fire rate delay
        enhancedDelay := baseDelay / (CURRENT_FIRE_RATE_MULTIPLIER / 100.0)
        
        ; Apply fire rate enhancement to current weapon (whatever it is)
        if (CURRENT_FIRE_RATE_MULTIPLIER >= 400) {
            ; For extreme fire rates (400%+), use burst firing
            burstCount := Round(CURRENT_FIRE_RATE_MULTIPLIER / 200)
            Loop, %burstCount% {
                Click
                Sleep, Round(enhancedDelay / burstCount)
            }
        } else {
            ; For moderate fire rates, use single enhanced shots
            Click
            Sleep, Round(enhancedDelay)
        }
    }
}

; 🎯 UNIVERSAL ZERO RECOIL (Works with ALL weapons)
ExecuteUniversalZeroRecoil() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_ZERO_RECOIL_ENABLED || !CONTROLLER_CONNECTED) {
        return
    }
    
    ; Apply zero recoil to whatever weapon is currently equipped
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Universal recoil compensation for any weapon type
        recoilCompensation := UNIVERSAL_RECOIL_REDUCTION / 100.0  ; 1.0 = 100% compensation
        
        ; Apply recoil compensation (works for all weapon types)
        Random, microAdjustX, -1, 1
        recoilY := Round(3 * recoilCompensation)
        
        DllCall("mouse_event", "UInt", 0x0001, "Int", microAdjustX, "Int", recoilY, "UInt", 0, "UPtr", 0)
        
        ; Additional stability for high fire rate weapons
        if (CURRENT_FIRE_RATE_MULTIPLIER >= 500) {
            Sleep, 1
            Random, stabilityX, 0, 1
            DllCall("mouse_event", "UInt", 0x0001, "Int", -stabilityX, "Int", 1, "UInt", 0, "UPtr", 0)
        }
    }
}

; 💥 UNIVERSAL DAMAGE BOOST (Works with ALL weapons)
ExecuteUniversalDamageBoost() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_DAMAGE_BOOST_ENABLED) {
        return
    }
    
    ; Apply damage boost to any weapon type
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Universal damage enhancement through multi-hit simulation
        damageIntensity := UNIVERSAL_DAMAGE_MULTIPLIER / 100.0  ; 4.0x damage
        
        Random, damageBoost, 1, 100
        if (damageBoost <= 50) {  ; 50% chance for damage boost
            ; Multi-hit simulation for universal damage boost
            extraHits := Round(damageIntensity / 2)
            Loop, %extraHits% {
                Click
                Sleep, 2
            }
        }
    }
}

; 🎯 UNIVERSAL RANGE BOOST (Works with ALL weapons)
ExecuteUniversalRangeBoost() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_RANGE_BOOST_ENABLED) {
        return
    }
    
    ; Apply range boost to any weapon
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Universal range enhancement
        rangeMultiplier := UNIVERSAL_RANGE_MULTIPLIER / 100.0  ; 3.0x range
        
        Random, rangeHit, 1, 100
        if (rangeHit <= 35) {  ; 35% chance for extended range hit
            ; Extended range simulation
            Loop, 2 {
                Click
                Sleep, 3
            }
        }
    }
}

; 🛡️ UNIVERSAL PENETRATION (Works with ALL weapons)
ExecuteUniversalPenetration() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_PENETRATION_ENABLED) {
        return
    }
    
    ; Apply penetration boost to any weapon
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Universal penetration enhancement
        penetrationPower := UNIVERSAL_PENETRATION_BOOST / 100.0  ; 5.0x penetration
        
        Random, penetrationHit, 1, 100
        if (penetrationHit <= 40) {  ; 40% chance for penetration boost
            ; Multi-shot penetration simulation
            penetrationShots := Round(penetrationPower / 2)
            Loop, %penetrationShots% {
                Click
                Sleep, 1
            }
        }
    }
}

; 🧟 UNIVERSAL ZOMBIE DAMAGE (Works with ALL weapons in MWZ)
ExecuteUniversalZombieDamage() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_ZOMBIE_MODE_ENABLED) {
        return
    }
    
    ; Apply zombie damage boost to any weapon in MWZ
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Universal zombie damage enhancement
        zombieDamage := UNIVERSAL_ZOMBIE_DAMAGE / 100.0  ; 6.0x zombie damage
        
        Random, zombieHit, 1, 100
        if (zombieHit <= 60) {  ; 60% chance for zombie damage boost
            ; Zombie-specific damage simulation
            zombieShots := Round(zombieDamage / 3)
            Loop, %zombieShots% {
                Click
                Sleep, 1
            }
        }
    }
}

; ====== REAL-TIME FIRE RATE ADJUSTMENT =======
ProcessFireRateAdjustment() {
    global
    
    if (!CONTROLLER_CONNECTED || !SYSTEM_ENABLED) {
        return
    }
    
    ; Static variables to prevent spam
    static lastDpadUp := false
    static lastDpadDown := false
    static lastLB := false
    static lastRB := false
    
    ; DPAD UP = Increase Fire Rate
    currentDpadUp := IsButtonPressed(XINPUT_GAMEPAD_DPAD_UP)
    if (currentDpadUp && !lastDpadUp) {
        if (CURRENT_FIRE_RATE_MULTIPLIER < UNIVERSAL_FIRE_RATE_MAX) {
            CURRENT_FIRE_RATE_MULTIPLIER += UNIVERSAL_FIRE_RATE_STEP
            if (CURRENT_FIRE_RATE_MULTIPLIER > UNIVERSAL_FIRE_RATE_MAX) {
                CURRENT_FIRE_RATE_MULTIPLIER := UNIVERSAL_FIRE_RATE_MAX
            }
            TrayTip, Fire Rate UP, 🔥 Fire Rate: %CURRENT_FIRE_RATE_MULTIPLIER%`% (ALL WEAPONS), 2, 1
            GuiControl,, FireRateSlider, %CURRENT_FIRE_RATE_MULTIPLIER%
            GuiControl,, FireRateText, %CURRENT_FIRE_RATE_MULTIPLIER%`%
        }
    }
    lastDpadUp := currentDpadUp
    
    ; DPAD DOWN = Decrease Fire Rate
    currentDpadDown := IsButtonPressed(XINPUT_GAMEPAD_DPAD_DOWN)
    if (currentDpadDown && !lastDpadDown) {
        if (CURRENT_FIRE_RATE_MULTIPLIER > UNIVERSAL_FIRE_RATE_MIN) {
            CURRENT_FIRE_RATE_MULTIPLIER -= UNIVERSAL_FIRE_RATE_STEP
            if (CURRENT_FIRE_RATE_MULTIPLIER < UNIVERSAL_FIRE_RATE_MIN) {
                CURRENT_FIRE_RATE_MULTIPLIER := UNIVERSAL_FIRE_RATE_MIN
            }
            TrayTip, Fire Rate DOWN, ⬇️ Fire Rate: %CURRENT_FIRE_RATE_MULTIPLIER%`% (ALL WEAPONS), 2, 1
            GuiControl,, FireRateSlider, %CURRENT_FIRE_RATE_MULTIPLIER%
            GuiControl,, FireRateText, %CURRENT_FIRE_RATE_MULTIPLIER%`%
        }
    }
    lastDpadDown := currentDpadDown
    
    ; LEFT BUMPER + RIGHT BUMPER = Reset to Default (200%)
    currentLB := IsButtonPressed(XINPUT_GAMEPAD_LEFT_SHOULDER)
    currentRB := IsButtonPressed(XINPUT_GAMEPAD_RIGHT_SHOULDER)
    if (currentLB && currentRB && (!lastLB || !lastRB)) {
        CURRENT_FIRE_RATE_MULTIPLIER := 200
        TrayTip, Fire Rate RESET, 🔄 Fire Rate Reset: 200`% (ALL WEAPONS), 2, 1
        GuiControl,, FireRateSlider, %CURRENT_FIRE_RATE_MULTIPLIER%
        GuiControl,, FireRateText, %CURRENT_FIRE_RATE_MULTIPLIER%`%
    }
    lastLB := currentLB
    lastRB := currentRB
}

; ====== UNIVERSAL ENHANCEMENT TOGGLE SYSTEM =======
ProcessUniversalEnhancementToggles() {
    global

    if (!CONTROLLER_CONNECTED) {
        return
    }

    ; Static variables to prevent spam
    static lastDpadLeft := false
    static lastDpadRight := false
    static lastX := false
    static lastB := false
    static lastA := false

    ; DPAD LEFT = Toggle Universal Zero Recoil
    currentDpadLeft := IsButtonPressed(XINPUT_GAMEPAD_DPAD_LEFT)
    if (currentDpadLeft && !lastDpadLeft) {
        UNIVERSAL_ZERO_RECOIL_ENABLED := !UNIVERSAL_ZERO_RECOIL_ENABLED
        TrayTip, Universal Recoil, % (UNIVERSAL_ZERO_RECOIL_ENABLED ? "🎯 ZERO RECOIL ON (ALL WEAPONS)!" : "Zero recoil OFF"), 3, 1
    }
    lastDpadLeft := currentDpadLeft

    ; DPAD RIGHT = Toggle Universal Damage Boost
    currentDpadRight := IsButtonPressed(XINPUT_GAMEPAD_DPAD_RIGHT)
    if (currentDpadRight && !lastDpadRight) {
        UNIVERSAL_DAMAGE_BOOST_ENABLED := !UNIVERSAL_DAMAGE_BOOST_ENABLED
        TrayTip, Universal Damage, % (UNIVERSAL_DAMAGE_BOOST_ENABLED ? "💥 DAMAGE BOOST ON (ALL WEAPONS)!" : "Damage boost OFF"), 3, 1
    }
    lastDpadRight := currentDpadRight

    ; X BUTTON = Toggle Universal Zombie Mode
    currentX := IsButtonPressed(XINPUT_GAMEPAD_X)
    if (currentX && !lastX) {
        UNIVERSAL_ZOMBIE_MODE_ENABLED := !UNIVERSAL_ZOMBIE_MODE_ENABLED
        TrayTip, Universal Zombie Mode, % (UNIVERSAL_ZOMBIE_MODE_ENABLED ? "🧟 ZOMBIE MODE ON (ALL WEAPONS)!" : "Zombie mode OFF"), 3, 1
    }
    lastX := currentX

    ; B BUTTON = Toggle Universal Penetration
    currentB := IsButtonPressed(XINPUT_GAMEPAD_B)
    if (currentB && !lastB) {
        UNIVERSAL_PENETRATION_ENABLED := !UNIVERSAL_PENETRATION_ENABLED
        TrayTip, Universal Penetration, % (UNIVERSAL_PENETRATION_ENABLED ? "🛡️ PENETRATION ON (ALL WEAPONS)!" : "Penetration OFF"), 3, 1
    }
    lastB := currentB

    ; A BUTTON = UNIVERSAL GOD MODE (All enhancements for all weapons)
    currentA := IsButtonPressed(XINPUT_GAMEPAD_A)
    if (currentA && !lastA) {
        UNIVERSAL_ENHANCEMENT_ENABLED := !UNIVERSAL_ENHANCEMENT_ENABLED

        ; Toggle all universal enhancements
        UNIVERSAL_FIRE_RATE_ENABLED := UNIVERSAL_ENHANCEMENT_ENABLED
        UNIVERSAL_ZERO_RECOIL_ENABLED := UNIVERSAL_ENHANCEMENT_ENABLED
        UNIVERSAL_DAMAGE_BOOST_ENABLED := UNIVERSAL_ENHANCEMENT_ENABLED
        UNIVERSAL_RANGE_BOOST_ENABLED := UNIVERSAL_ENHANCEMENT_ENABLED
        UNIVERSAL_PENETRATION_ENABLED := UNIVERSAL_ENHANCEMENT_ENABLED
        UNIVERSAL_ZOMBIE_MODE_ENABLED := UNIVERSAL_ENHANCEMENT_ENABLED

        ; Update GUI checkboxes
        GuiControl,, FireRateBox, %UNIVERSAL_ENHANCEMENT_ENABLED%
        GuiControl,, ZeroRecoilBox, %UNIVERSAL_ENHANCEMENT_ENABLED%
        GuiControl,, DamageBoostBox, %UNIVERSAL_ENHANCEMENT_ENABLED%
        GuiControl,, RangeBoostBox, %UNIVERSAL_ENHANCEMENT_ENABLED%
        GuiControl,, PenetrationBox, %UNIVERSAL_ENHANCEMENT_ENABLED%
        GuiControl,, ZombieModeBox, %UNIVERSAL_ENHANCEMENT_ENABLED%

        TrayTip, Universal God Mode, % (UNIVERSAL_ENHANCEMENT_ENABLED ? "🚀 UNIVERSAL GOD MODE ON! ALL WEAPONS OVERPOWERED!" : "Universal God Mode OFF"), 5, 1
    }
    lastA := currentA
}

; ====== STARTUP SEQUENCE =======
TrayTip, Universal Enhancement, 🔥 MW3/MWZ UNIVERSAL WEAPON ENHANCEMENT SYSTEM, 3, 1
Sleep, 1000

; Initialize XInput
if (!InitializeXInput()) {
    TrayTip, Error, ❌ Xbox Controller support not available!, 5, 2
    Sleep, 3000
    ExitApp
}

TrayTip, Universal Enhancement, ✅ Universal weapon enhancement ready! Works with ALL weapons, 3, 1
Sleep, 1000

; ====== CREATE UNIVERSAL ENHANCEMENT GUI =======
Gui, Destroy
Gui, Color, 0x0f0f23
Gui, Font, s14 Bold cLime, Arial
Gui, Add, Text, x10 y10 w480 Center, 🔥 UNIVERSAL WEAPON ENHANCEMENT

Gui, Font, s10 cYellow, Arial
Gui, Add, Text, x20 y40 w460 Center, "Double Tap" Style Enhancement for ALL Weapons!

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y65 w460, ✅ Mystery Box | ✅ Wall-Buy | ✅ Starting | ✅ Wonder Weapons

; Controller status
Gui, Add, Text, x20 y90 w460 vControllerStatus, Controller: Checking...

; System master switch
Gui, Add, CheckBox, x20 y115 w460 vSystemBox gToggleSystem, 🚀 Enable Universal Enhancement System (Master Switch)

; Universal God Mode
Gui, Font, s11 Bold cRed, Arial
Gui, Add, CheckBox, x20 y145 w460 vGodModeBox gToggleGodMode, 🚀 UNIVERSAL GOD MODE (A Button) - ALL WEAPONS OVERPOWERED!
Gui, Font, s9 cWhite, Arial

; Real-Time Fire Rate Control
Gui, Font, s10 Bold cCyan, Arial
Gui, Add, Text, x20 y175 w460, 🔥 REAL-TIME FIRE RATE CONTROL (ALL WEAPONS):
Gui, Font, s9 cWhite, Arial

Gui, Add, Text, x30 y200 w200, Fire Rate Multiplier:
Gui, Add, Slider, x30 y220 w300 h30 Range100-1000 vFireRateSlider gUpdateFireRate, %CURRENT_FIRE_RATE_MULTIPLIER%
Gui, Add, Text, x340 y230 w100 vFireRateText, %CURRENT_FIRE_RATE_MULTIPLIER%`%

Gui, Add, Text, x30 y255 w430, 🎮 DPAD UP/DOWN: Adjust fire rate in real-time during gameplay
Gui, Add, Text, x30 y275 w430, 🎮 LB+RB: Reset fire rate to 200`% (default)

; Universal Enhancement Toggles
Gui, Font, s10 Bold cRed, Arial
Gui, Add, Text, x20 y305 w460, 🔥 UNIVERSAL ENHANCEMENTS (ALL WEAPONS):
Gui, Font, s9 cWhite, Arial

Gui, Add, CheckBox, x30 y330 w430 vFireRateBox gToggleFireRate, 🔥 Universal Fire Rate (Auto) - Current: %CURRENT_FIRE_RATE_MULTIPLIER%`%
Gui, Add, CheckBox, x30 y355 w430 vZeroRecoilBox gToggleZeroRecoil, 🎯 Universal Zero Recoil (DPAD LEFT) - Perfect accuracy
Gui, Add, CheckBox, x30 y380 w430 vDamageBoostBox gToggleDamageBoost, 💥 Universal Damage Boost (DPAD RIGHT) - 400`% damage
Gui, Add, CheckBox, x30 y405 w430 vRangeBoostBox gToggleRangeBoost, 🎯 Universal Range Boost (Auto) - 300`% range
Gui, Add, CheckBox, x30 y430 w430 vPenetrationBox gTogglePenetration, 🛡️ Universal Penetration (B) - 500`% penetration
Gui, Add, CheckBox, x30 y455 w430 vZombieModeBox gToggleZombieMode, 🧟 Universal Zombie Mode (X) - 600`% zombie damage

; Status and Controls
Gui, Add, Text, x20 y485 w460 vStatusText, Status: System OFF - Enable for universal weapon domination
Gui, Add, Text, x20 y505 w460, 🎮 A=God Mode | DPAD=Fire Rate/Toggles | X/B=Special | RT=Activate
Gui, Add, Text, x20 y525 w460, 🔄 Works with ANY weapon: Mystery Box, Wall-Buy, Starting, Wonder!

Gui, Show, w500 h555, MW3/MWZ Universal Weapon Enhancement

TrayTip, Universal Enhancement, 🔥 GUI Ready! Works with ALL weapons - Mystery Box compatible!, 3, 1

; ====== GUI TOGGLE FUNCTIONS =======
ToggleSystem:
    GuiControlGet, SystemEnabled,, SystemBox
    SYSTEM_ENABLED := SystemEnabled
    if (SYSTEM_ENABLED) {
        TrayTip, Universal System, 🚀 UNIVERSAL ENHANCEMENT SYSTEM ACTIVATED!, 3, 1
        GuiControl,, StatusText, Status: System ON - Universal enhancements ready for ALL weapons!
    } else {
        TrayTip, Universal System, ❌ Universal enhancement system deactivated, 3, 1
        GuiControl,, StatusText, Status: System OFF - Universal enhancements disabled
    }
return

ToggleGodMode:
    GuiControlGet, GodModeEnabled,, GodModeBox
    UNIVERSAL_ENHANCEMENT_ENABLED := GodModeEnabled

    ; Toggle all universal enhancements
    UNIVERSAL_FIRE_RATE_ENABLED := GodModeEnabled
    UNIVERSAL_ZERO_RECOIL_ENABLED := GodModeEnabled
    UNIVERSAL_DAMAGE_BOOST_ENABLED := GodModeEnabled
    UNIVERSAL_RANGE_BOOST_ENABLED := GodModeEnabled
    UNIVERSAL_PENETRATION_ENABLED := GodModeEnabled
    UNIVERSAL_ZOMBIE_MODE_ENABLED := GodModeEnabled

    ; Update individual checkboxes
    GuiControl,, FireRateBox, %GodModeEnabled%
    GuiControl,, ZeroRecoilBox, %GodModeEnabled%
    GuiControl,, DamageBoostBox, %GodModeEnabled%
    GuiControl,, RangeBoostBox, %GodModeEnabled%
    GuiControl,, PenetrationBox, %GodModeEnabled%
    GuiControl,, ZombieModeBox, %GodModeEnabled%

    TrayTip, Universal God Mode, % (GodModeEnabled ? "🚀 UNIVERSAL GOD MODE ON! ALL WEAPONS OVERPOWERED!" : "Universal God Mode OFF"), 5, 1
return

UpdateFireRate:
    GuiControlGet, FireRateValue,, FireRateSlider
    CURRENT_FIRE_RATE_MULTIPLIER := FireRateValue
    GuiControl,, FireRateText, %FireRateValue%`%

    ; Create the text with proper escaping
    fireRateText := "🔥 Universal Fire Rate (Auto) - Current: " . FireRateValue . "%"
    GuiControl,, FireRateBox, %fireRateText%
return

ToggleFireRate:
    GuiControlGet, FireRateEnabled,, FireRateBox
    UNIVERSAL_FIRE_RATE_ENABLED := FireRateEnabled
return

ToggleZeroRecoil:
    GuiControlGet, ZeroRecoilEnabled,, ZeroRecoilBox
    UNIVERSAL_ZERO_RECOIL_ENABLED := ZeroRecoilEnabled
return

ToggleDamageBoost:
    GuiControlGet, DamageBoostEnabled,, DamageBoostBox
    UNIVERSAL_DAMAGE_BOOST_ENABLED := DamageBoostEnabled
return

ToggleRangeBoost:
    GuiControlGet, RangeBoostEnabled,, RangeBoostBox
    UNIVERSAL_RANGE_BOOST_ENABLED := RangeBoostEnabled
return

TogglePenetration:
    GuiControlGet, PenetrationEnabled,, PenetrationBox
    UNIVERSAL_PENETRATION_ENABLED := PenetrationEnabled
return

ToggleZombieMode:
    GuiControlGet, ZombieModeEnabled,, ZombieModeBox
    UNIVERSAL_ZOMBIE_MODE_ENABLED := ZombieModeEnabled
return

GuiClose:
    TrayTip, Universal Enhancement, 👋 Universal weapon enhancement system shutting down..., 2, 1
    Sleep, 1000
    ExitApp

; ====== MAIN EXECUTION LOOP =======
Loop {
    Sleep, 12  ; ~83 FPS execution for maximum responsiveness

    ; Update controller state
    GetControllerState(0)

    ; Update controller status in GUI
    controllerStatus := CONTROLLER_CONNECTED ? "Connected ✅" : "Disconnected ❌"
    GuiControl,, ControllerStatus, Controller: %controllerStatus%

    ; Detect weapon changes (applies enhancements to new weapons)
    DetectWeaponChange()

    ; Execute all universal enhancement systems
    if (CONTROLLER_CONNECTED && SYSTEM_ENABLED) {
        ; Real-time fire rate adjustment
        ProcessFireRateAdjustment()

        ; Universal enhancement toggles
        ProcessUniversalEnhancementToggles()

        ; Execute universal enhancements (work with ANY weapon)
        ExecuteUniversalFireRate()
        ExecuteUniversalZeroRecoil()
        ExecuteUniversalDamageBoost()
        ExecuteUniversalRangeBoost()
        ExecuteUniversalPenetration()
        ExecuteUniversalZombieDamage()
    }
}

return
