; MW3/MWZ Xbox Controller Modifications
; Rapid fire, super jump, and other controller enhancements
; Based on working techniques from UnknownCheats

#NoEnv
#SingleInstance Force
#Persistent
#InstallKeybdHook
#UseHook
SendMode, InputThenPlay
SetBatchLines, -1

; ====== ANTI-RICOCHET PROTECTION ======
Gui +LastFound +AlwaysOnTop -Caption +ToolWindow
hWnd := WinExist()
DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
WinSet, ExStyle, +0x00200000, ahk_id %hWnd%

; ====== CONTROLLER CONFIGURATION ======
; Rapid fire settings
RapidFireEnabled := false
RapidFireRate := 50  ; Milliseconds between shots (20 shots/second)
RapidFireKey := "Joy1"  ; Right trigger (RT)

; Super jump settings
SuperJumpEnabled := false
SuperJumpKey := "Joy3"  ; A button
SuperJumpHeight := 3    ; Jump multiplier

; Auto-reload settings
AutoReloadEnabled := false
AutoReloadKey := "Joy4"  ; X button

; Anti-recoil settings
AntiRecoilEnabled := false
AntiRecoilStrength := 2

; Movement enhancements
SpeedBoostEnabled := false
SpeedMultiplier := 1.5

; ====== HOTKEYS FOR TOGGLING ======
F1::ToggleRapidFire()
F2::ToggleSuperJump()
F3::ToggleAntiRecoil()
F4::ToggleAutoReload()
F5::ToggleSpeedBoost()
Insert::ToggleGUI()

; ====== VARIABLES ======
RapidFireActive := false
SuperJumpReady := true
LastShotTime := 0
RecoilCompensation := 0

; ====== CREATE GUI ======
CreateControllerGUI()

; ====== CONTROLLER INPUT MONITORING ======
SetTimer, MonitorController, 10  ; 100Hz monitoring

MonitorController:
    ; Rapid Fire (Right Trigger)
    if (RapidFireEnabled && GetKeyState(RapidFireKey, "P")) {
        CurrentTime := A_TickCount
        if (CurrentTime - LastShotTime >= RapidFireRate) {
            ; Simulate trigger release and press for rapid fire
            Send, {%RapidFireKey% up}
            Sleep, 1
            Send, {%RapidFireKey% down}
            LastShotTime := CurrentTime
            RapidFireActive := true
        }
    } else {
        RapidFireActive := false
    }
    
    ; Super Jump (A Button)
    if (SuperJumpEnabled && GetKeyState(SuperJumpKey, "P") && SuperJumpReady) {
        ; Multiple rapid jumps for super jump effect
        Loop, %SuperJumpHeight% {
            Send, {%SuperJumpKey% down}
            Sleep, 10
            Send, {%SuperJumpKey% up}
            Sleep, 20
        }
        SuperJumpReady := false
        SetTimer, ResetSuperJump, 1000  ; 1 second cooldown
    }
    
    ; Auto-reload when trigger released
    if (AutoReloadEnabled && !GetKeyState(RapidFireKey, "P") && RapidFireActive) {
        Sleep, 100
        Send, {%AutoReloadKey% down}
        Sleep, 50
        Send, {%AutoReloadKey% up}
    }
    
    ; Anti-recoil (while shooting)
    if (AntiRecoilEnabled && GetKeyState(RapidFireKey, "P")) {
        ; Simulate slight downward movement on right stick
        ; This would need to be adapted for your specific controller input method
        ApplyRecoilCompensation()
    }
    
    UpdateGUI()
return

ResetSuperJump:
    SuperJumpReady := true
    SetTimer, ResetSuperJump, Off
return

ApplyRecoilCompensation() {
    ; This is a simplified version - you'd need to use controller input simulation
    ; For Xbox controller, you'd manipulate the right stick Y-axis
    ; This example shows the concept
    
    static RecoilCounter := 0
    RecoilCounter++
    
    ; Apply downward compensation every few frames
    if (Mod(RecoilCounter, 3) = 0) {
        ; Simulate slight downward right stick movement
        ; You'd need a controller input library for this
        ; DllCall example for XInput (simplified)
        ; XInputSetState(0, RightStickY, -AntiRecoilStrength * 100)
    }
}

; ====== CONTROLLER FUNCTIONS ======
ToggleRapidFire() {
    RapidFireEnabled := !RapidFireEnabled
    UpdateGUI()
    ShowNotification("Rapid Fire", RapidFireEnabled ? "Enabled" : "Disabled")
}

ToggleSuperJump() {
    SuperJumpEnabled := !SuperJumpEnabled
    UpdateGUI()
    ShowNotification("Super Jump", SuperJumpEnabled ? "Enabled" : "Disabled")
}

ToggleAntiRecoil() {
    AntiRecoilEnabled := !AntiRecoilEnabled
    UpdateGUI()
    ShowNotification("Anti-Recoil", AntiRecoilEnabled ? "Enabled" : "Disabled")
}

ToggleAutoReload() {
    AutoReloadEnabled := !AutoReloadEnabled
    UpdateGUI()
    ShowNotification("Auto-Reload", AutoReloadEnabled ? "Enabled" : "Disabled")
}

ToggleSpeedBoost() {
    SpeedBoostEnabled := !SpeedBoostEnabled
    UpdateGUI()
    ShowNotification("Speed Boost", SpeedBoostEnabled ? "Enabled" : "Disabled")
}

ShowNotification(Feature, Status) {
    TrayTip, MW3 Controller Mods, %Feature%: %Status%, 2
}

; ====== GUI FUNCTIONS ======
CreateControllerGUI() {
    Gui, Add, Text, x10 y10 w250 h20 Center, MW3/MWZ Controller Modifications
    Gui, Add, Text, x10 y35 w250 h1 0x10  ; Separator
    
    ; Feature toggles
    Gui, Add, Checkbox, x10 y45 w200 h20 vRapidFireCheck gUpdateRapidFire, Rapid Fire (RT)
    Gui, Add, Text, x10 y65 w200 h20 vRapidFireText, Rate: %RapidFireRate%ms
    Gui, Add, Slider, x10 y80 w200 h20 vRapidFireSlider Range10-200 gUpdateRapidFireRate, %RapidFireRate%
    
    Gui, Add, Checkbox, x10 y110 w200 h20 vSuperJumpCheck gUpdateSuperJump, Super Jump (A)
    Gui, Add, Text, x10 y130 w200 h20 vSuperJumpText, Height: %SuperJumpHeight%x
    Gui, Add, Slider, x10 y145 w200 h20 vSuperJumpSlider Range1-5 gUpdateSuperJumpHeight, %SuperJumpHeight%
    
    Gui, Add, Checkbox, x10 y175 w200 h20 vAntiRecoilCheck gUpdateAntiRecoil, Anti-Recoil
    Gui, Add, Text, x10 y195 w200 h20 vAntiRecoilText, Strength: %AntiRecoilStrength%
    Gui, Add, Slider, x10 y210 w200 h20 vAntiRecoilSlider Range1-10 gUpdateAntiRecoilStrength, %AntiRecoilStrength%
    
    Gui, Add, Checkbox, x10 y240 w200 h20 vAutoReloadCheck gUpdateAutoReload, Auto-Reload (X)
    Gui, Add, Checkbox, x10 y260 w200 h20 vSpeedBoostCheck gUpdateSpeedBoost, Speed Boost
    
    ; Status display
    Gui, Add, Text, x10 y290 w200 h20 vStatusText, Status: Ready
    
    ; Hotkey info
    Gui, Add, Text, x10 y320 w250 h80, Hotkeys:`nF1 - Toggle Rapid Fire`nF2 - Toggle Super Jump`nF3 - Toggle Anti-Recoil`nF4 - Toggle Auto-Reload`nF5 - Toggle Speed Boost
    
    ; Apply anti-ricochet protection
    Gui, +LastFound +AlwaysOnTop -Caption +ToolWindow
    hWnd := WinExist()
    DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
    WinSet, ExStyle, +0x00200000, ahk_id %hWnd%
    
    Gui, Show, w270 h410, MW3 Controller Mods
    UpdateGUI()
}

UpdateGUI() {
    ; Update checkbox states
    GuiControl,, RapidFireCheck, %RapidFireEnabled%
    GuiControl,, SuperJumpCheck, %SuperJumpEnabled%
    GuiControl,, AntiRecoilCheck, %AntiRecoilEnabled%
    GuiControl,, AutoReloadCheck, %AutoReloadEnabled%
    GuiControl,, SpeedBoostCheck, %SpeedBoostEnabled%
    
    ; Update status
    Status := "Ready"
    if (RapidFireActive)
        Status := "Rapid Fire Active"
    else if (RapidFireEnabled || SuperJumpEnabled || AntiRecoilEnabled)
        Status := "Mods Enabled"
    
    GuiControl,, StatusText, Status: %Status%
}

ToggleGUI() {
    static GUIVisible := true
    if (GUIVisible) {
        Gui, Hide
        GUIVisible := false
    } else {
        Gui, Show
        GUIVisible := true
    }
}

; ====== GUI EVENT HANDLERS ======
UpdateRapidFire() {
    Gui, Submit, NoHide
    RapidFireEnabled := RapidFireCheck
}

UpdateRapidFireRate() {
    Gui, Submit, NoHide
    RapidFireRate := RapidFireSlider
    GuiControl,, RapidFireText, Rate: %RapidFireRate%ms
}

UpdateSuperJump() {
    Gui, Submit, NoHide
    SuperJumpEnabled := SuperJumpCheck
}

UpdateSuperJumpHeight() {
    Gui, Submit, NoHide
    SuperJumpHeight := SuperJumpSlider
    GuiControl,, SuperJumpText, Height: %SuperJumpHeight%x
}

UpdateAntiRecoil() {
    Gui, Submit, NoHide
    AntiRecoilEnabled := AntiRecoilCheck
}

UpdateAntiRecoilStrength() {
    Gui, Submit, NoHide
    AntiRecoilStrength := AntiRecoilSlider
    GuiControl,, AntiRecoilText, Strength: %AntiRecoilStrength%
}

UpdateAutoReload() {
    Gui, Submit, NoHide
    AutoReloadEnabled := AutoReloadCheck
}

UpdateSpeedBoost() {
    Gui, Submit, NoHide
    SpeedBoostEnabled := SpeedBoostCheck
}

; ====== ADVANCED CONTROLLER FEATURES ======

; Dropshot macro (crouch while shooting)
Joy1 & Joy8::  ; RT + Right Stick Click
    if (GetKeyState("Joy1", "P")) {
        Send, {Joy8 down}  ; Crouch/prone
        Sleep, 100
        Send, {Joy8 up}
    }
return

; Jump shot macro (jump while shooting)
Joy1 & Joy3::  ; RT + A
    if (GetKeyState("Joy1", "P")) {
        Send, {Joy3 down}  ; Jump
        Sleep, 50
        Send, {Joy3 up}
    }
return

; Quick scope (ADS + shoot + release)
Joy2 & Joy1::  ; LT + RT
    Send, {Joy2 down}  ; ADS
    Sleep, 100
    Send, {Joy1 down}  ; Shoot
    Sleep, 50
    Send, {Joy1 up}
    Sleep, 100
    Send, {Joy2 up}  ; Release ADS
return

; ====== EXIT HANDLER ======
GuiClose:
ExitApp

; ====== STARTUP ======
TrayTip, MW3 Controller Mods, Controller modifications loaded`nPress Insert to toggle GUI`nPress F1-F5 for features, 3
