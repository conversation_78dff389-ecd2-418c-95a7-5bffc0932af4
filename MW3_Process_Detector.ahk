; ====== MW3 PROCESS DETECTOR =======
; Find the correct MW3 process name for memory validation
; Compatible with AutoHotkey v1.1.37.02
; ====== MW3 PROCESS DETECTOR =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== POSSIBLE MW3 PROCESS NAMES =======
possibleNames := ["cod.exe", "ModernWarfare3.exe", "MW3.exe", "CallOfDuty.exe", "mw3mp.exe", "iw9.exe", "modernwarfareiii.exe"]

; ====== STARTUP =======
TrayTip, Process Detector, 🔍 Scanning for MW3 processes..., 3, 1

; ====== CREATE GUI =======
Gui, Destroy
Gui, Color, 0x2d2d30
Gui, Font, s12 Bold cLime, <PERSON><PERSON>, Add, Text, x10 y10 w380 Center, 🔍 MW3 PROCESS DETECTOR

Gui, <PERSON>ont, s9 c<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Add, Text, x20 y40 w360, Scanning for MW3/MWZ processes...

; Process list area
Gui, Add, Text, x20 y70 w360 h200 vProcessList, Scanning...

; Scan button
Gui, Add, Button, x20 y280 w360 h30 gScanProcesses, 🔍 SCAN FOR MW3 PROCESSES

; Instructions
Gui, Add, Text, x20 y320 w360, Instructions:
Gui, Add, Text, x20 y340 w360, 1. Make sure MW3/MWZ is running
Gui, Add, Text, x20 y360 w360, 2. Click scan button
Gui, Add, Text, x20 y380 w360, 3. Note the correct process name

Gui, Show, w400 h410, MW3 Process Detector

; Auto-scan on startup
Gosub, ScanProcesses

; ====== SCAN FUNCTION =======
ScanProcesses:
    GuiControl,, ProcessList, 🔍 Scanning for MW3 processes...
    
    foundProcesses := ""
    foundCount := 0
    
    ; Check each possible process name
    Loop, % possibleNames.Length() {
        processName := possibleNames[A_Index]
        Process, Exist, %processName%
        if (ErrorLevel) {
            foundProcesses .= "✅ FOUND: " . processName . " (PID: " . ErrorLevel . ")`n"
            foundCount++
        } else {
            foundProcesses .= "❌ Not found: " . processName . "`n"
        }
    }
    
    ; Check for any Call of Duty related processes
    foundProcesses .= "`n🔍 Checking for any Call of Duty processes:`n"
    
    ; Get all running processes and check for COD-related names
    for process in ComObjGet("winmgmts:").ExecQuery("SELECT * FROM Win32_Process") {
        processName := process.Name
        if (InStr(processName, "cod") || InStr(processName, "call") || InStr(processName, "duty") || InStr(processName, "modern") || InStr(processName, "warfare") || InStr(processName, "mw") || InStr(processName, "iw")) {
            foundProcesses .= "🎯 Possible: " . processName . " (PID: " . process.ProcessId . ")`n"
            foundCount++
        }
    }
    
    if (foundCount > 0) {
        foundProcesses .= "`n✅ Found " . foundCount . " potential MW3 processes!"
        foundProcesses .= "`nUse the process name (with .exe) in the validation script."
        TrayTip, Process Found, ✅ Found MW3 processes! Check the list., 3, 1
    } else {
        foundProcesses .= "`n❌ No MW3 processes found."
        foundProcesses .= "`nMake sure MW3/MWZ is running and try again."
        TrayTip, No Process, ❌ No MW3 processes found - Start MW3 first!, 5, 2
    }
    
    GuiControl,, ProcessList, %foundProcesses%
return

GuiClose:
    ExitApp
