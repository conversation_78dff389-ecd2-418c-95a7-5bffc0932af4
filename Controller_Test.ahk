; Xbox Controller Button Tester
; Run this to see which buttons are which numbers

#NoEnv
#SingleInstance Force
#Persistent

; Create test GUI
Gui, Add, Text, x10 y10 w300 h20 Center, Xbox Controller Button Tester
Gui, Add, Text, x10 y35 w300 h2 0x10

Gui, Add, Text, x10 y45 w300 h20 vButtonDisplay, Press any controller button...

Gui, Add, Text, x10 y75 w300 h120, Button Mapping:`nJoy1 = Right Trigger (RT)`nJoy2 = Left Trigger (LT)`nJoy3 = A Button`nJoy4 = B Button`nJoy5 = X Button`nJoy6 = Y Button`nJoy7 = Left Bumper (LB)`nJoy8 = Right Bumper (RB)`nJoy9 = Back/Select`nJoy10 = Start`nJoy11 = Left Stick Click`nJoy12 = Right Stick Click

Gui, Show, w320 h210, Controller Test

SetTimer, CheckButtons, 50

CheckButtons:
    ButtonPressed := ""
    
    Loop, 32 {
        if (GetKeyState("Joy" . A_Index, "P")) {
            ButtonPressed := "Joy" . A_Index . " is pressed"
            break
        }
    }
    
    if (ButtonPressed = "")
        ButtonPressed := "No buttons pressed"
        
    GuiControl,, ButtonDisplay, %ButtonPressed%
return

GuiClose:
ExitApp
