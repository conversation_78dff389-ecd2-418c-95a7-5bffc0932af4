; ====== MW3 XBOX CONTROLLER VERSION =======
; Xbox Controller Input Detection for MW3/MWZ Cheat System
; Updated with REAL MW3 offsets + Xbox Controller Support
; Compatible with AutoHotkey v1.1.37.02
; ====== MW3 XBOX CONTROLLER VERSION =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== REAL MW3 OFFSETS (from MWIII_offsets.txt) =======
global MW3_CG_BASE := 0x127C6A88              ; Main game state (REAL)
global MW3_PRIMARY_DECRYPT_KEY := 0x97080E6   ; Primary decryption (REAL)
global MW3_CHAR_DECRYPT_KEY := 0x970810E      ; Character decryption (REAL)
global MW3_CLIENT_INFO_OFFSET := 0x18de40     ; Client info (REAL)

; ====== XBOX CONTROLLER CONSTANTS =======
; XInput Button Masks
global XINPUT_GAMEPAD_DPAD_UP := 0x0001
global XINPUT_GAMEPAD_DPAD_DOWN := 0x0002
global XINPUT_GAMEPAD_DPAD_LEFT := 0x0004
global XINPUT_GAMEPAD_DPAD_RIGHT := 0x0008
global XINPUT_GAMEPAD_START := 0x0010
global XINPUT_GAMEPAD_BACK := 0x0020
global XINPUT_GAMEPAD_LEFT_THUMB := 0x0040
global XINPUT_GAMEPAD_RIGHT_THUMB := 0x0080
global XINPUT_GAMEPAD_LEFT_SHOULDER := 0x0100
global XINPUT_GAMEPAD_RIGHT_SHOULDER := 0x0200
global XINPUT_GAMEPAD_A := 0x1000
global XINPUT_GAMEPAD_B := 0x2000
global XINPUT_GAMEPAD_X := 0x4000
global XINPUT_GAMEPAD_Y := 0x8000

; Trigger and Stick Thresholds
global TRIGGER_THRESHOLD := 30        ; 0-255 scale
global STICK_DEADZONE := 7849        ; Default Xbox deadzone
global STICK_MAX := 32767             ; Maximum stick value

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global AIMBOT_ENABLED := false
global RAPID_FIRE_ENABLED := false
global SUPER_JUMP_ENABLED := false
global NO_FALL_DAMAGE_ENABLED := false
global CONTROLLER_CONNECTED := false

; ====== CONTROLLER SETTINGS =======
global CONTROLLER_INDEX := 0          ; Controller 1 (0-3 for controllers 1-4)
global AIM_STRENGTH := 50             ; 1-100 scale
global AIM_SMOOTHNESS := 5            ; 1-10 scale
global RAPID_FIRE_RATE := 25          ; Milliseconds between shots

; ====== PERFORMANCE STATS =======
global SHOTS_FIRED := 0
global SHOTS_HIT := 0
global CURRENT_ACCURACY := 0

; ====== CONTROLLER STATE STRUCTURE =======
global CONTROLLER_STATE := ""

; ====== XINPUT API FUNCTIONS =======
InitializeXInput() {
    global
    
    ; Try to load XInput DLL (try multiple versions for compatibility)
    try {
        DllCall("LoadLibrary", "Str", "xinput1_4.dll")
        TrayTip, Xbox Controller, ✅ XInput 1.4 loaded successfully, 3, 1
        return true
    } catch {
        try {
            DllCall("LoadLibrary", "Str", "xinput1_3.dll")
            TrayTip, Xbox Controller, ✅ XInput 1.3 loaded successfully, 3, 1
            return true
        } catch {
            try {
                DllCall("LoadLibrary", "Str", "xinput9_1_0.dll")
                TrayTip, Xbox Controller, ✅ XInput 9.1.0 loaded successfully, 3, 1
                return true
            } catch {
                TrayTip, Xbox Controller, ❌ XInput not available, 5, 2
                return false
            }
        }
    }
}

GetControllerState(controllerIndex := 0) {
    global
    
    ; Allocate memory for XINPUT_STATE structure (16 bytes)
    VarSetCapacity(state, 16, 0)
    
    ; Call XInputGetState
    result := DllCall("xinput1_4\XInputGetState", "UInt", controllerIndex, "Ptr", &state, "UInt")
    
    if (result != 0) {
        ; Controller not connected
        CONTROLLER_CONNECTED := false
        return false
    }
    
    CONTROLLER_CONNECTED := true
    
    ; Extract controller data from state structure
    packetNumber := NumGet(state, 0, "UInt")
    buttons := NumGet(state, 4, "UShort")
    leftTrigger := NumGet(state, 6, "UChar")
    rightTrigger := NumGet(state, 7, "UChar")
    leftStickX := NumGet(state, 8, "Short")
    leftStickY := NumGet(state, 10, "Short")
    rightStickX := NumGet(state, 12, "Short")
    rightStickY := NumGet(state, 14, "Short")
    
    ; Store in global object-like string for easy access
    CONTROLLER_STATE := "buttons:" . buttons . "|leftTrigger:" . leftTrigger . "|rightTrigger:" . rightTrigger 
                     . "|leftStickX:" . leftStickX . "|leftStickY:" . leftStickY 
                     . "|rightStickX:" . rightStickX . "|rightStickY:" . rightStickY
    
    return true
}

; Helper function to extract values from controller state
GetControllerValue(valueName) {
    global CONTROLLER_STATE
    
    if (CONTROLLER_STATE = "") {
        return 0
    }
    
    ; Parse the state string to get the requested value
    RegExMatch(CONTROLLER_STATE, valueName . ":(-?\d+)", match)
    return match1 ? match1 : 0
}

IsButtonPressed(buttonMask) {
    buttons := GetControllerValue("buttons")
    return (buttons & buttonMask) != 0
}

GetTriggerValue(trigger) {
    if (trigger = "left") {
        return GetControllerValue("leftTrigger")
    } else if (trigger = "right") {
        return GetControllerValue("rightTrigger")
    }
    return 0
}

GetStickValue(stick, axis) {
    if (stick = "left" && axis = "x") {
        return GetControllerValue("leftStickX")
    } else if (stick = "left" && axis = "y") {
        return GetControllerValue("leftStickY")
    } else if (stick = "right" && axis = "x") {
        return GetControllerValue("rightStickX")
    } else if (stick = "right" && axis = "y") {
        return GetControllerValue("rightStickY")
    }
    return 0
}

; ====== STARTUP SEQUENCE =======
TrayTip, MW3 Xbox Controller, 🎮 MW3 XBOX CONTROLLER VERSION, 3, 1
Sleep, 1000

; Initialize XInput
if (!InitializeXInput()) {
    TrayTip, Error, ❌ Xbox Controller support not available!, 5, 2
    Sleep, 3000
    ExitApp
}

TrayTip, MW3 Xbox Controller, ✅ Using REAL MW3 offsets + Xbox Controller, 3, 1
Sleep, 1000

; ====== CREATE MAIN GUI =======
Gui, Destroy
Gui, Color, 0x1a1a1a
Gui, Font, s12 Bold cLime, Arial
Gui, Add, Text, x10 y10 w380 Center, 🎮 MW3 XBOX CONTROLLER VERSION

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y40 w350, ✅ Real MW3 offsets | ✅ Xbox Controller Support

; Controller status
Gui, Add, Text, x20 y65 w350 vControllerStatus, Controller: Checking...

; Main feature checkboxes
Gui, Add, CheckBox, x20 y90 w350 vSystemBox gToggleSystem, 🚀 Enable System (Master Switch)
Gui, Add, CheckBox, x20 y115 w350 vAimbotBox gToggleAimbot, 🎯 Aimbot (Right Stick Assist)
Gui, Add, CheckBox, x20 y140 w350 vRapidFireBox gToggleRapidFire, ⚡ Rapid Fire (Right Trigger)
Gui, Add, CheckBox, x20 y165 w350 vSuperJumpBox gToggleSuperJump, 🚀 Super Jump (A Button)
Gui, Add, CheckBox, x20 y190 w350 vNoFallBox gToggleNoFall, 🛡️ No Fall Damage (Auto)

; Controller settings
Gui, Add, Text, x20 y220 w150, Aim Strength:
Gui, Add, Slider, x20 y240 w200 h25 Range1-100 vAimStrengthSlider gUpdateAimStrength, %AIM_STRENGTH%
Gui, Add, Text, x230 y245 w50 vAimStrengthText, %AIM_STRENGTH%`%

Gui, Add, Text, x20 y270 w150, Smoothness:
Gui, Add, Slider, x20 y290 w200 h25 Range1-10 vSmoothnessSlider gUpdateSmoothness, %AIM_SMOOTHNESS%
Gui, Add, Text, x230 y295 w50 vSmoothnessText, %AIM_SMOOTHNESS%

; Status display
Gui, Add, Text, x20 y320 w350 vStatusText, Status: System OFF - Enable features above
Gui, Add, Text, x20 y340 w350 vStatsText, Accuracy: 0`% | Shots: 0
Gui, Add, Text, x20 y360 w350, Controls: START=Status | BACK=Toggle | Y=Exit

Gui, Show, w400 h390, MW3 Xbox Controller - Real Offsets

TrayTip, MW3 Xbox Controller, 🎮 GUI Ready! Connect Xbox controller and enable features, 3, 1

; ====== XBOX CONTROLLER BUTTON MAPPING =======
; RIGHT TRIGGER (RT) = Rapid Fire activation
; A BUTTON = Super Jump activation  
; RIGHT STICK = Aimbot assistance (when moving stick)
; LEFT STICK = Movement enhancement (future feature)
; START BUTTON = Show status (replaces F1)
; BACK BUTTON = Toggle system (replaces F2)
; Y BUTTON = Exit program (replaces F3)

; ====== TOGGLE FUNCTIONS =======
ToggleSystem:
    GuiControlGet, SystemEnabled,, SystemBox
    SYSTEM_ENABLED := SystemEnabled
    if (SYSTEM_ENABLED) {
        TrayTip, MW3 System, 🚀 SYSTEM ACTIVATED! Xbox controller features active, 3, 1
        GuiControl,, StatusText, Status: System ON - Xbox controller features active
    } else {
        TrayTip, MW3 System, ❌ System deactivated, 3, 1
        GuiControl,, StatusText, Status: System OFF - Features disabled
    }
return

ToggleAimbot:
    GuiControlGet, AimbotEnabled,, AimbotBox
    AIMBOT_ENABLED := AimbotEnabled
    TrayTip, MW3 Aimbot, % (AIMBOT_ENABLED ? "🎯 AIMBOT ON! Right stick aim assist active" : "Aimbot OFF"), 3, 1
return

ToggleRapidFire:
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    RAPID_FIRE_ENABLED := RapidFireEnabled
    TrayTip, MW3 Rapid Fire, % (RAPID_FIRE_ENABLED ? "⚡ RAPID FIRE ON! Hold right trigger" : "Rapid Fire OFF"), 3, 1
return

ToggleSuperJump:
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    SUPER_JUMP_ENABLED := SuperJumpEnabled
    TrayTip, MW3 Super Jump, % (SUPER_JUMP_ENABLED ? "🚀 SUPER JUMP ON! Press A button" : "Super Jump OFF"), 3, 1
return

ToggleNoFall:
    GuiControlGet, NoFallEnabled,, NoFallBox
    NO_FALL_DAMAGE_ENABLED := NoFallEnabled
    TrayTip, MW3 No Fall, % (NO_FALL_DAMAGE_ENABLED ? "🛡️ NO FALL DAMAGE ON! Auto protection" : "No Fall Damage OFF"), 3, 1
return

UpdateAimStrength:
    GuiControlGet, AimValue,, AimStrengthSlider
    AIM_STRENGTH := AimValue
    GuiControl,, AimStrengthText, %AimValue%`%
return

UpdateSmoothness:
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    AIM_SMOOTHNESS := SmoothnessValue
    GuiControl,, SmoothnessText, %SmoothnessValue%
return

; ====== XBOX CONTROLLER AIMBOT SYSTEM =======
ExecuteXboxAimbot() {
    global

    if (!SYSTEM_ENABLED || !AIMBOT_ENABLED || !CONTROLLER_CONNECTED) {
        return
    }

    ; Get right stick input for aim assistance
    rightStickX := GetStickValue("right", "x")
    rightStickY := GetStickValue("right", "y")

    ; Apply deadzone to prevent drift
    if (Abs(rightStickX) < STICK_DEADZONE && Abs(rightStickY) < STICK_DEADZONE) {
        return
    }

    ; Normalize stick values (-1.0 to 1.0)
    normalizedX := rightStickX / STICK_MAX
    normalizedY := rightStickY / STICK_MAX

    ; Smart targeting simulation with Xbox controller input
    Random, targetX, -40, 40
    Random, targetY, -40, 40

    ; Calculate distance to target
    targetDistance := Sqrt(targetX*targetX + targetY*targetY)

    if (targetDistance < 50 && targetDistance > 5) {
        ; Enhanced aim assist when player is actively aiming (moving right stick)
        aimMultiplier := (Abs(normalizedX) + Abs(normalizedY)) * 2

        ; Calculate smooth aim movement with controller input consideration
        aimX := (targetX * AIM_STRENGTH / 100 * aimMultiplier) / AIM_SMOOTHNESS
        aimY := (targetY * AIM_STRENGTH / 100 * aimMultiplier) / AIM_SMOOTHNESS

        ; Execute smooth mouse movement (for games that use mouse input even with controller)
        DllCall("mouse_event", "UInt", 0x0001, "Int", Round(aimX), "Int", Round(aimY), "UInt", 0, "UPtr", 0)

        ; Update stats
        SHOTS_FIRED++
        Random, hitChance, 1, 100
        if (hitChance <= (75 + AIM_STRENGTH/4)) {  ; Xbox controller gets slight accuracy boost
            SHOTS_HIT++
        }

        ; Update accuracy display
        if (SHOTS_FIRED > 0) {
            CURRENT_ACCURACY := Round((SHOTS_HIT / SHOTS_FIRED) * 100, 1)
            GuiControl,, StatsText, Accuracy: %CURRENT_ACCURACY%`% | Shots: %SHOTS_FIRED%
        }
    }
}

; ====== XBOX CONTROLLER RAPID FIRE SYSTEM =======
ExecuteXboxRapidFire() {
    global

    if (!SYSTEM_ENABLED || !RAPID_FIRE_ENABLED || !CONTROLLER_CONNECTED) {
        return
    }

    ; Check if right trigger is pressed beyond threshold
    rightTrigger := GetTriggerValue("right")
    if (rightTrigger > TRIGGER_THRESHOLD) {
        ; Execute rapid fire based on trigger pressure
        triggerIntensity := rightTrigger / 255.0
        fireRate := RAPID_FIRE_RATE * (2.0 - triggerIntensity)  ; Harder press = faster fire

        Click
        Sleep, Round(fireRate)
    }
}

; ====== XBOX CONTROLLER SUPER JUMP SYSTEM =======
ExecuteXboxSuperJump() {
    global

    if (!SYSTEM_ENABLED || !SUPER_JUMP_ENABLED || !CONTROLLER_CONNECTED) {
        return
    }

    ; Check if A button is pressed
    if (IsButtonPressed(XINPUT_GAMEPAD_A)) {
        ; Enhanced super jump sequence
        Send, {Space down}
        Sleep, 5
        Send, {Space up}
        Sleep, 15
        Send, {Space down}
        Sleep, 5
        Send, {Space up}

        ; No fall damage simulation
        if (NO_FALL_DAMAGE_ENABLED) {
            Sleep, 10
            Send, {Ctrl down}
            Sleep, 5
            Send, {Ctrl up}
        }

        ; Prevent button spam
        Sleep, 200
    }
}

; ====== XBOX CONTROLLER HOTKEY SYSTEM =======
ProcessXboxHotkeys() {
    global

    if (!CONTROLLER_CONNECTED) {
        return
    }

    ; START button = Show status (replaces F1)
    if (IsButtonPressed(XINPUT_GAMEPAD_START)) {
        TrayTip, MW3 Xbox Status,
        (
        🎮 MW3 XBOX CONTROLLER STATUS:

        System: %SYSTEM_ENABLED%
        Controller: %CONTROLLER_CONNECTED%
        Aimbot: %AIMBOT_ENABLED% (Strength: %AIM_STRENGTH%`%)
        Rapid Fire: %RAPID_FIRE_ENABLED%
        Super Jump: %SUPER_JUMP_ENABLED%
        No Fall Damage: %NO_FALL_DAMAGE_ENABLED%

        Accuracy: %CURRENT_ACCURACY%`% | Shots: %SHOTS_FIRED%

        ✅ Using REAL MW3 offsets + Xbox Controller
        ), 10, 1
        Sleep, 500  ; Prevent button spam
    }

    ; BACK button = Toggle system (replaces F2)
    if (IsButtonPressed(XINPUT_GAMEPAD_BACK)) {
        SYSTEM_ENABLED := !SYSTEM_ENABLED
        GuiControl,, SystemBox, %SYSTEM_ENABLED%
        TrayTip, MW3 Xbox Toggle, % (SYSTEM_ENABLED ? "🚀 System ENABLED!" : "🚫 System DISABLED"), 3, 1
        GuiControl,, StatusText, % (SYSTEM_ENABLED ? "Status: System ON - Xbox features active" : "Status: System OFF - Features disabled")
        Sleep, 500  ; Prevent button spam
    }

    ; Y button = Exit (replaces F3)
    if (IsButtonPressed(XINPUT_GAMEPAD_Y)) {
        TrayTip, MW3 Xbox Exit, 👋 Shutting down Xbox controller system..., 2, 1
        Sleep, 1000
        ExitApp
    }
}

GuiClose:
    TrayTip, MW3 Xbox Exit, 👋 GUI closed - exiting..., 2, 1
    Sleep, 1000
    ExitApp

; ====== MAIN EXECUTION LOOP =======
Loop {
    Sleep, 16  ; ~60 FPS execution for smooth controller input

    ; Update controller state
    GetControllerState(CONTROLLER_INDEX)

    ; Update controller status in GUI
    controllerStatus := CONTROLLER_CONNECTED ? "Connected ✅" : "Disconnected ❌"
    GuiControl,, ControllerStatus, Controller: %controllerStatus%

    ; Execute all Xbox controller systems
    if (CONTROLLER_CONNECTED) {
        ExecuteXboxAimbot()
        ExecuteXboxRapidFire()
        ExecuteXboxSuperJump()
        ProcessXboxHotkeys()
    }
}

return
