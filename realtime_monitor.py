#!/usr/bin/env python3
"""
MW3/MWZ Real-time Data Monitor
Displays live game data in a GUI dashboard

Based on BO6 offsets adapted for MW3/MWZ:
- Health monitoring
- Ammo tracking  
- Position coordinates
- Game state info
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import struct
import psutil
import ctypes
from typing import Optional, Dict, Any

class GameDataMonitor:
    def __init__(self):
        self.process_handle = None
        self.base_address = 0x0  # Will be found dynamically
        self.running = False
        
        # Estimated offsets based on BO6 patterns (will need adjustment)
        self.offsets = {
            'health': 0x1E8,           # Based on BO6: 993FC48 + 1E8
            'primary_ammo': 0x18F8,    # Based on BO6: B9742E8 + 18F8
            'secondary_ammo': 0x195C,  # Based on BO6: B9742E8 + 195C
            'primary_reserve': 0x1438, # Based on BO6: B9742E8 + 1438
            'secondary_reserve': 0x1488, # Based on BO6: B9742E8 + 1488
            'lethals': 0x1AEC,         # Based on BO6: B9742E8 + 1AEC
            'tacticals': 0x1B50,       # Based on BO6: B9742E8 + 1B50
            'field_upgrade': 0x1BB4,   # Based on BO6: B9742E8 + 1BB4
            'pos_x': 0x58,             # Based on BO6: B9742E8 + 58
            'pos_y': 0x60,             # Based on BO6: B9742E8 + 60
            'pos_z': 0x59,             # Based on BO6: B9742E8 + 59
        }
        
        self.current_data = {
            'health': 0,
            'primary_ammo': 0,
            'secondary_ammo': 0,
            'primary_reserve': 0,
            'secondary_reserve': 0,
            'lethals': 0,
            'tacticals': 0,
            'field_upgrade': 0,
            'pos_x': 0.0,
            'pos_y': 0.0,
            'pos_z': 0.0,
            'connected': False
        }
        
    def find_game_process(self) -> bool:
        """Find MW3/MWZ process and get handle"""
        process_names = ["cod.exe", "mw3.exe", "modernwarfare3.exe", "mwz.exe"]
        
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'].lower() in [name.lower() for name in process_names]:
                try:
                    self.process_handle = ctypes.windll.kernel32.OpenProcess(
                        0x0010, False, proc.info['pid']  # PROCESS_VM_READ
                    )
                    if self.process_handle:
                        print(f"Connected to {proc.info['name']} (PID: {proc.info['pid']})")
                        return True
                except Exception as e:
                    print(f"Failed to open process: {e}")
                    
        return False
    
    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """Read memory from game process"""
        if not self.process_handle:
            return None
            
        buffer = ctypes.create_string_buffer(size)
        bytes_read = ctypes.c_size_t()
        
        success = ctypes.windll.kernel32.ReadProcessMemory(
            self.process_handle, address, buffer, size, ctypes.byref(bytes_read)
        )
        
        return buffer.raw[:bytes_read.value] if success else None
    
    def read_int(self, base_addr: int, offset: int) -> int:
        """Read 4-byte integer from memory"""
        try:
            data = self.read_memory(base_addr + offset, 4)
            return struct.unpack('<I', data)[0] if data else 0
        except:
            return 0
    
    def read_float(self, base_addr: int, offset: int) -> float:
        """Read 4-byte float from memory"""
        try:
            data = self.read_memory(base_addr + offset, 4)
            return struct.unpack('<f', data)[0] if data else 0.0
        except:
            return 0.0
    
    def update_game_data(self):
        """Update all game data from memory"""
        if not self.process_handle:
            self.current_data['connected'] = False
            return
            
        # For demo purposes, using a placeholder base address
        # In practice, you'd scan for the actual base address
        base = 0x10000000  # Placeholder - needs to be found dynamically
        
        try:
            self.current_data.update({
                'health': self.read_int(base, self.offsets['health']),
                'primary_ammo': self.read_int(base, self.offsets['primary_ammo']),
                'secondary_ammo': self.read_int(base, self.offsets['secondary_ammo']),
                'primary_reserve': self.read_int(base, self.offsets['primary_reserve']),
                'secondary_reserve': self.read_int(base, self.offsets['secondary_reserve']),
                'lethals': self.read_int(base, self.offsets['lethals']),
                'tacticals': self.read_int(base, self.offsets['tacticals']),
                'field_upgrade': self.read_int(base, self.offsets['field_upgrade']),
                'pos_x': self.read_float(base, self.offsets['pos_x']),
                'pos_y': self.read_float(base, self.offsets['pos_y']),
                'pos_z': self.read_float(base, self.offsets['pos_z']),
                'connected': True
            })
        except Exception as e:
            print(f"Error updating data: {e}")
            self.current_data['connected'] = False

class MonitorGUI:
    def __init__(self):
        self.monitor = GameDataMonitor()
        self.root = tk.Tk()
        self.root.title("MW3/MWZ Real-time Monitor")
        self.root.geometry("600x500")
        
        self.setup_ui()
        self.update_thread = None
        
    def setup_ui(self):
        """Create the GUI interface"""
        # Connection status
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_label = ttk.Label(status_frame, text="Status: Disconnected", 
                                     foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(status_frame, text="Connect", 
                                     command=self.toggle_connection)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # Main data display
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Player stats section
        stats_frame = ttk.LabelFrame(main_frame, text="Player Stats")
        stats_frame.pack(fill=tk.X, pady=5)
        
        self.health_var = tk.StringVar(value="Health: 0")
        ttk.Label(stats_frame, textvariable=self.health_var, font=("Arial", 12)).pack(anchor=tk.W)
        
        # Ammo section
        ammo_frame = ttk.LabelFrame(main_frame, text="Ammunition")
        ammo_frame.pack(fill=tk.X, pady=5)
        
        self.primary_ammo_var = tk.StringVar(value="Primary: 0 / 0")
        self.secondary_ammo_var = tk.StringVar(value="Secondary: 0 / 0")
        ttk.Label(ammo_frame, textvariable=self.primary_ammo_var).pack(anchor=tk.W)
        ttk.Label(ammo_frame, textvariable=self.secondary_ammo_var).pack(anchor=tk.W)
        
        # Equipment section
        equipment_frame = ttk.LabelFrame(main_frame, text="Equipment")
        equipment_frame.pack(fill=tk.X, pady=5)
        
        self.lethals_var = tk.StringVar(value="Lethals: 0")
        self.tacticals_var = tk.StringVar(value="Tacticals: 0")
        self.field_upgrade_var = tk.StringVar(value="Field Upgrade: 0")
        
        ttk.Label(equipment_frame, textvariable=self.lethals_var).pack(anchor=tk.W)
        ttk.Label(equipment_frame, textvariable=self.tacticals_var).pack(anchor=tk.W)
        ttk.Label(equipment_frame, textvariable=self.field_upgrade_var).pack(anchor=tk.W)
        
        # Position section
        position_frame = ttk.LabelFrame(main_frame, text="Position")
        position_frame.pack(fill=tk.X, pady=5)
        
        self.position_var = tk.StringVar(value="X: 0.0, Y: 0.0, Z: 0.0")
        ttk.Label(position_frame, textvariable=self.position_var, font=("Courier", 10)).pack(anchor=tk.W)
        
        # Log section
        log_frame = ttk.LabelFrame(main_frame, text="Activity Log")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = tk.Text(log_frame, height=8, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def log_message(self, message: str):
        """Add message to log"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
    def toggle_connection(self):
        """Connect/disconnect from game"""
        if not self.monitor.running:
            if self.monitor.find_game_process():
                self.monitor.running = True
                self.status_label.config(text="Status: Connected", foreground="green")
                self.connect_btn.config(text="Disconnect")
                self.log_message("Connected to game process")
                
                # Start update thread
                self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
                self.update_thread.start()
            else:
                self.log_message("Game process not found!")
        else:
            self.monitor.running = False
            self.status_label.config(text="Status: Disconnected", foreground="red")
            self.connect_btn.config(text="Connect")
            self.log_message("Disconnected from game")
            
    def update_loop(self):
        """Main update loop running in separate thread"""
        last_data = {}
        
        while self.monitor.running:
            self.monitor.update_game_data()
            
            # Check for changes and log them
            for key, value in self.monitor.current_data.items():
                if key in last_data and last_data[key] != value and key != 'connected':
                    if key == 'health' and abs(value - last_data[key]) > 5:
                        self.log_message(f"Health changed: {last_data[key]} -> {value}")
                    elif 'ammo' in key and value != last_data[key]:
                        self.log_message(f"{key.replace('_', ' ').title()} changed: {last_data[key]} -> {value}")
                        
            last_data = self.monitor.current_data.copy()
            
            # Update GUI (must be done in main thread)
            self.root.after(0, self.update_gui)
            
            time.sleep(0.1)  # Update 10 times per second
            
    def update_gui(self):
        """Update GUI elements with current data"""
        data = self.monitor.current_data
        
        if data['connected']:
            self.health_var.set(f"Health: {data['health']}")
            self.primary_ammo_var.set(f"Primary: {data['primary_ammo']} / {data['primary_reserve']}")
            self.secondary_ammo_var.set(f"Secondary: {data['secondary_ammo']} / {data['secondary_reserve']}")
            self.lethals_var.set(f"Lethals: {data['lethals']}")
            self.tacticals_var.set(f"Tacticals: {data['tacticals']}")
            self.field_upgrade_var.set(f"Field Upgrade: {data['field_upgrade']}")
            self.position_var.set(f"X: {data['pos_x']:.2f}, Y: {data['pos_y']:.2f}, Z: {data['pos_z']:.2f}")
        else:
            # Show disconnected state
            for var in [self.health_var, self.primary_ammo_var, self.secondary_ammo_var,
                       self.lethals_var, self.tacticals_var, self.field_upgrade_var]:
                var.set(var.get().split(':')[0] + ": --")
            self.position_var.set("X: --, Y: --, Z: --")
            
    def run(self):
        """Start the GUI"""
        self.log_message("MW3/MWZ Monitor started")
        self.log_message("Click 'Connect' to attach to game process")
        self.root.mainloop()

def main():
    app = MonitorGUI()
    app.run()

if __name__ == "__main__":
    main()
