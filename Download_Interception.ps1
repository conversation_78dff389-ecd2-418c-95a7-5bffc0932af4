# MW3 AI Ultimate - Interception Binary Downloader
# This script downloads the correct Interception binaries

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MW3 AI ULTIMATE - INTERCEPTION SETUP" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  WARNING: Not running as Administrator" -ForegroundColor Yellow
    Write-Host "   Driver installation will require Administrator privileges later" -ForegroundColor Yellow
    Write-Host ""
}

Write-Host "[1/5] Downloading Interception binaries..." -ForegroundColor Green

# Interception download URL (latest release)
$interceptionUrl = "https://github.com/oblitum/Interception/releases/download/v1.0.1/Interception.zip"
$zipPath = "Interception_Binaries.zip"

try {
    Write-Host "   Downloading from: $interceptionUrl" -ForegroundColor Gray
    Invoke-WebRequest -Uri $interceptionUrl -OutFile $zipPath -UseBasicParsing
    Write-Host "   ✓ Download completed!" -ForegroundColor Green
} catch {
    Write-Host "   ✗ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "MANUAL DOWNLOAD INSTRUCTIONS:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://github.com/oblitum/Interception/releases" -ForegroundColor White
    Write-Host "2. Download 'Interception.zip' (NOT source code)" -ForegroundColor White
    Write-Host "3. Save it as 'Interception_Binaries.zip' in this folder" -ForegroundColor White
    Write-Host "4. Run this script again" -ForegroundColor White
    pause
    exit 1
}

Write-Host ""
Write-Host "[2/5] Extracting Interception binaries..." -ForegroundColor Green

# Extract the zip file
try {
    if (Test-Path "Interception_Binaries") {
        Remove-Item "Interception_Binaries" -Recurse -Force
    }
    
    Expand-Archive -Path $zipPath -DestinationPath "Interception_Binaries" -Force
    Write-Host "   ✓ Extraction completed!" -ForegroundColor Green
} catch {
    Write-Host "   ✗ Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    pause
    exit 1
}

Write-Host ""
Write-Host "[3/5] Locating required files..." -ForegroundColor Green

# Find the required files in the extracted folder
$interceptionFolder = Get-ChildItem -Path "Interception_Binaries" -Recurse -Directory | Where-Object { $_.Name -eq "Interception" } | Select-Object -First 1

if ($interceptionFolder) {
    $installExe = Get-ChildItem -Path $interceptionFolder.FullName -Recurse -Filter "install-interception.exe" | Select-Object -First 1
    $uninstallExe = Get-ChildItem -Path $interceptionFolder.FullName -Recurse -Filter "uninstall-interception.exe" | Select-Object -First 1
    $interceptionDll = Get-ChildItem -Path $interceptionFolder.FullName -Recurse -Filter "interception.dll" | Select-Object -First 1
    
    if ($installExe) {
        Write-Host "   ✓ Found install-interception.exe" -ForegroundColor Green
    } else {
        Write-Host "   ✗ install-interception.exe not found" -ForegroundColor Red
    }
    
    if ($uninstallExe) {
        Write-Host "   ✓ Found uninstall-interception.exe" -ForegroundColor Green
    } else {
        Write-Host "   ✗ uninstall-interception.exe not found" -ForegroundColor Red
    }
    
    if ($interceptionDll) {
        Write-Host "   ✓ Found interception.dll" -ForegroundColor Green
    } else {
        Write-Host "   ✗ interception.dll not found" -ForegroundColor Red
    }
} else {
    Write-Host "   ✗ Interception folder structure not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "[4/5] Copying files to current directory..." -ForegroundColor Green

# Copy the required files to the current directory
try {
    if ($installExe) {
        Copy-Item $installExe.FullName -Destination "install-interception.exe" -Force
        Write-Host "   ✓ Copied install-interception.exe" -ForegroundColor Green
    }
    
    if ($uninstallExe) {
        Copy-Item $uninstallExe.FullName -Destination "uninstall-interception.exe" -Force
        Write-Host "   ✓ Copied uninstall-interception.exe" -ForegroundColor Green
    }
    
    if ($interceptionDll) {
        Copy-Item $interceptionDll.FullName -Destination "interception.dll" -Force
        Write-Host "   ✓ Copied interception.dll" -ForegroundColor Green
    }
} catch {
    Write-Host "   ✗ File copy failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "[5/5] Cleanup..." -ForegroundColor Green

# Clean up temporary files
try {
    Remove-Item $zipPath -Force -ErrorAction SilentlyContinue
    Remove-Item "Interception_Binaries" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "   ✓ Temporary files cleaned up" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not clean up temporary files" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "INTERCEPTION SETUP COMPLETE!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check what we have now
Write-Host "FILES IN CURRENT DIRECTORY:" -ForegroundColor White
if (Test-Path "install-interception.exe") {
    Write-Host "✓ install-interception.exe" -ForegroundColor Green
} else {
    Write-Host "✗ install-interception.exe" -ForegroundColor Red
}

if (Test-Path "uninstall-interception.exe") {
    Write-Host "✓ uninstall-interception.exe" -ForegroundColor Green
} else {
    Write-Host "✗ uninstall-interception.exe" -ForegroundColor Red
}

if (Test-Path "interception.dll") {
    Write-Host "✓ interception.dll" -ForegroundColor Green
} else {
    Write-Host "✗ interception.dll" -ForegroundColor Red
}

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Install the driver by running: install-interception.exe" -ForegroundColor White
Write-Host "   (Right-click and 'Run as administrator')" -ForegroundColor Gray
Write-Host "2. Restart your computer (REQUIRED)" -ForegroundColor White
Write-Host "3. Run the MW3 AI Ultimate System" -ForegroundColor White
Write-Host ""

if ($isAdmin) {
    Write-Host "INSTALL DRIVER NOW? (y/n): " -ForegroundColor Yellow -NoNewline
    $response = Read-Host
    
    if ($response -eq "y" -or $response -eq "Y" -or $response -eq "yes") {
        Write-Host ""
        Write-Host "Installing Interception driver..." -ForegroundColor Green
        
        if (Test-Path "install-interception.exe") {
            try {
                Start-Process -FilePath "install-interception.exe" -Wait -Verb RunAs
                Write-Host "✓ Driver installation completed!" -ForegroundColor Green
                Write-Host ""
                Write-Host "⚠️  RESTART REQUIRED!" -ForegroundColor Red
                Write-Host "   Please restart your computer before using the system." -ForegroundColor Yellow
            } catch {
                Write-Host "✗ Driver installation failed: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "   Try running install-interception.exe manually as Administrator" -ForegroundColor Yellow
            }
        } else {
            Write-Host "✗ install-interception.exe not found" -ForegroundColor Red
        }
    }
} else {
    Write-Host "⚠️  Run PowerShell as Administrator to install the driver automatically" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
