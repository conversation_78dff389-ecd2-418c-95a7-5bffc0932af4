; MW3/MWZ ULTIMATE COLOR AIMBOT SYSTEM
; Advanced external aimbot with multiple targeting modes
; Based on working BO6 techniques - RICOCHET PROOF

#NoEnv
#SingleInstance Force
#Persistent
CoordMode, Pixel, Screen, RGB
CoordMode, Mouse, Screen
#InstallKeybdHook
#UseHook
SendMode, InputThenPlay
SetBatchLines, -1
ListLines, Off

; ====== ULTIMATE ANTI-DETECTION ======
; Multiple layers of screenshot protection
Gui +LastFound +AlwaysOnTop -Caption +ToolWindow +E0x08000000
hWnd := WinExist()
; Layer 1: SetWindowDisplayAffinity
DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
; Layer 2: Extended window style
WinSet, ExStyle, +0x00200000, ahk_id %hWnd%
; Layer 3: Process protection
DllCall("ntdll.dll\\RtlSetProcessIsCritical", "UInt", 1, "UInt", 0, "UInt", 0)

; ====== ULTIMATE AIMBOT CONFIGURATION ======
; Target Colors (MW3/MWZ specific)
EnemyColor := 0xFF0000      ; Red enemy nameplates
AllyColor := 0x00FF00       ; Green ally nameplates (avoid)
ZombieColor := 0xFFFF00     ; Yellow zombie nameplates
BossColor := 0xFF8000       ; Orange boss nameplates
ColorTolerance := 25        ; Color matching tolerance

; Aimbot Settings
AimbotEnabled := false
AimMode := "Smart"          ; "Smart", "Rage", "Legit", "Silent"
TargetBone := "Head"        ; "Head", "Chest", "Auto"
FOVRadius := 200            ; Field of view radius
AimSpeed := 0.7             ; Aim smoothness (0.1-1.0)
MaxDistance := 500          ; Maximum target distance

; Advanced Features
PredictionEnabled := true   ; Target prediction
AntiShakeEnabled := true    ; Reduce aim shake
VisibilityCheck := true     ; Only target visible enemies
MultiTargetMode := false    ; Target multiple enemies
AutoShoot := false          ; Automatic shooting
TriggerBot := false         ; Shoot when crosshair on target

; Bone Offsets (pixels from detected color)
HeadOffset := -15           ; Pixels above nameplate
ChestOffset := 10           ; Pixels below nameplate
AutoOffset := -5            ; Balanced offset

; Prediction Settings
PredictionStrength := 2.0   ; Prediction multiplier
PredictionSmoothing := 0.8  ; Prediction smoothing

; ====== HOTKEY SYSTEM ======
F1::ToggleAimbot()
F2::CycleAimMode()
F3::CycleTargetBone()
F4::TogglePrediction()
F5::ToggleAutoShoot()
F6::ToggleTriggerBot()
F7::ToggleMultiTarget()
F8::CycleTargetType()
F9::EmergencyDisable()
Insert::ToggleGUI()
Home::ShowSettings()

; ====== VARIABLES ======
CurrentTarget := {x: 0, y: 0, found: false, type: "none"}
LastTarget := {x: 0, y: 0, time: 0}
TargetHistory := []
AimActive := false
ShotsFired := 0
TargetType := "Enemy"       ; "Enemy", "Zombie", "Boss", "All"

; ====== MAIN AIMBOT LOOP ======
SetTimer, UltimateAimbotLoop, 8  ; 125Hz for maximum precision

UltimateAimbotLoop:
    if (!AimbotEnabled)
        return
        
    ; Get current mouse position
    MouseGetPos, MouseX, MouseY
    
    ; Find targets based on current mode
    FindTargets(MouseX, MouseY)
    
    ; Apply aim assistance if target found
    if (CurrentTarget.found) {
        ApplyAimAssistance(MouseX, MouseY)
        
        ; Auto shoot if enabled
        if (AutoShoot && IsTargetValid()) {
            AutomaticShooting()
        }
        
        ; Trigger bot
        if (TriggerBot && IsOnTarget(MouseX, MouseY)) {
            TriggerShooting()
        }
    }
    
    ; Update target history for prediction
    UpdateTargetHistory()
    
    ; Update GUI
    UpdateAimbotGUI()
return

; ====== TARGET FINDING SYSTEM ======
FindTargets(centerX, centerY) {
    CurrentTarget.found := false
    BestTarget := {x: 0, y: 0, distance: 999999, type: "none"}
    
    ; Scan in optimized spiral pattern
    Loop, %FOVRadius% {
        radius := A_Index * 2
        if (radius > FOVRadius)
            break
            
        ; Check multiple points around circle for better coverage
        Loop, 16 {
            angle := (A_Index - 1) * 22.5 * 0.0174533  ; Convert to radians
            checkX := centerX + (radius * Cos(angle))
            checkY := centerY + (radius * Sin(angle))
            
            ; Boundary check
            if (checkX < 0 || checkX > A_ScreenWidth || checkY < 0 || checkY > A_ScreenHeight)
                continue
                
            ; Get pixel color
            PixelGetColor, foundColor, %checkX%, %checkY%, RGB
            
            ; Check against target colors
            targetInfo := CheckTargetColor(foundColor, checkX, checkY)
            if (targetInfo.valid) {
                distance := Sqrt((checkX - centerX)**2 + (checkY - centerY)**2)
                
                ; Select best target based on mode
                if (SelectBestTarget(targetInfo, distance, BestTarget)) {
                    BestTarget.x := checkX
                    BestTarget.y := checkY
                    BestTarget.distance := distance
                    BestTarget.type := targetInfo.type
                }
            }
        }
    }
    
    ; Set current target
    if (BestTarget.distance < 999999) {
        CurrentTarget.x := BestTarget.x
        CurrentTarget.y := BestTarget.y
        CurrentTarget.found := true
        CurrentTarget.type := BestTarget.type
        
        ; Apply bone offset
        ApplyBoneOffset()
    }
}

CheckTargetColor(color, x, y) {
    result := {valid: false, type: "none", priority: 0}
    
    ; Check enemy color
    if (TargetType = "Enemy" || TargetType = "All") {
        if (ColorMatch(color, EnemyColor, ColorTolerance)) {
            result.valid := true
            result.type := "Enemy"
            result.priority := 3
            return result
        }
    }
    
    ; Check zombie color
    if (TargetType = "Zombie" || TargetType = "All") {
        if (ColorMatch(color, ZombieColor, ColorTolerance)) {
            result.valid := true
            result.type := "Zombie"
            result.priority := 2
            return result
        }
    }
    
    ; Check boss color
    if (TargetType = "Boss" || TargetType = "All") {
        if (ColorMatch(color, BossColor, ColorTolerance)) {
            result.valid := true
            result.type := "Boss"
            result.priority := 5
            return result
        }
    }
    
    ; Avoid allies
    if (ColorMatch(color, AllyColor, ColorTolerance)) {
        result.valid := false
        return result
    }
    
    return result
}

SelectBestTarget(targetInfo, distance, currentBest) {
    ; Priority-based target selection
    if (AimMode = "Smart") {
        ; Prioritize by type and distance
        score := targetInfo.priority * 1000 - distance
        bestScore := currentBest.priority * 1000 - currentBest.distance
        return score > bestScore
    } else if (AimMode = "Rage") {
        ; Closest target
        return distance < currentBest.distance
    } else if (AimMode = "Legit") {
        ; Closest to crosshair with priority
        return (distance < currentBest.distance && targetInfo.priority >= 2)
    }
    
    return distance < currentBest.distance
}

ApplyBoneOffset() {
    if (TargetBone = "Head") {
        CurrentTarget.y += HeadOffset
    } else if (TargetBone = "Chest") {
        CurrentTarget.y += ChestOffset
    } else if (TargetBone = "Auto") {
        ; Auto-select based on distance
        if (CurrentTarget.distance < 150)
            CurrentTarget.y += HeadOffset
        else
            CurrentTarget.y += ChestOffset
    }
}

; ====== AIM ASSISTANCE SYSTEM ======
ApplyAimAssistance(mouseX, mouseY) {
    targetX := CurrentTarget.x
    targetY := CurrentTarget.y
    
    ; Apply prediction if enabled
    if (PredictionEnabled && TargetHistory.Length() > 1) {
        predicted := CalculatePrediction()
        targetX := predicted.x
        targetY := predicted.y
    }
    
    ; Calculate aim movement
    deltaX := targetX - mouseX
    deltaY := targetY - mouseY
    
    ; Apply smoothing based on mode
    smoothing := GetSmoothingFactor()
    moveX := deltaX * smoothing
    moveY := deltaY * smoothing
    
    ; Anti-shake system
    if (AntiShakeEnabled) {
        moveX := ApplyAntiShake(moveX)
        moveY := ApplyAntiShake(moveY)
    }
    
    ; Apply movement with randomization
    Random, jitterX, -1, 1
    Random, jitterY, -1, 1
    finalX := moveX + (jitterX * 0.5)
    finalY := moveY + (jitterY * 0.5)
    
    ; Move mouse
    DllCall("mouse_event", "UInt", 0x01, "Int", finalX, "Int", finalY, "UInt", 0, "Ptr", 0)
    AimActive := true
}

GetSmoothingFactor() {
    if (AimMode = "Rage")
        return 1.0  ; Instant aim
    else if (AimMode = "Legit")
        return AimSpeed * 0.3  ; Very smooth
    else if (AimMode = "Smart")
        return AimSpeed * 0.7  ; Balanced
    else
        return AimSpeed
}

CalculatePrediction() {
    if (TargetHistory.Length() < 2)
        return {x: CurrentTarget.x, y: CurrentTarget.y}
        
    ; Calculate velocity from history
    last := TargetHistory[TargetHistory.Length()]
    prev := TargetHistory[TargetHistory.Length() - 1]
    
    velocityX := (last.x - prev.x) * PredictionStrength
    velocityY := (last.y - prev.y) * PredictionStrength
    
    ; Apply smoothing
    velocityX *= PredictionSmoothing
    velocityY *= PredictionSmoothing
    
    return {x: CurrentTarget.x + velocityX, y: CurrentTarget.y + velocityY}
}

ApplyAntiShake(movement) {
    ; Reduce micro-movements that look unnatural
    if (Abs(movement) < 2)
        return 0
    return movement
}

; ====== SHOOTING SYSTEMS ======
AutomaticShooting() {
    static lastShot := 0
    currentTime := A_TickCount
    
    ; Rate limiting
    if (currentTime - lastShot < 100)  ; 10 shots per second max
        return
        
    ; Simulate mouse click
    Click
    ShotsFired++
    lastShot := currentTime
}

TriggerShooting() {
    static triggerReady := true
    
    if (triggerReady) {
        Click
        ShotsFired++
        triggerReady := false
        SetTimer, ResetTrigger, 150
    }
}

ResetTrigger:
    triggerReady := true
    SetTimer, ResetTrigger, Off
return

IsOnTarget(mouseX, mouseY) {
    ; Check if mouse is close enough to target for trigger bot
    distance := Sqrt((mouseX - CurrentTarget.x)**2 + (mouseY - CurrentTarget.y)**2)
    return distance < 20  ; 20 pixel tolerance
}

IsTargetValid() {
    ; Visibility check and other validations
    if (!VisibilityCheck)
        return true
        
    ; Simple visibility check (could be enhanced)
    return CurrentTarget.found && CurrentTarget.distance < MaxDistance
}

; ====== UTILITY FUNCTIONS ======
ColorMatch(color1, color2, tolerance) {
    r1 := (color1 >> 16) & 0xFF
    g1 := (color1 >> 8) & 0xFF
    b1 := color1 & 0xFF
    
    r2 := (color2 >> 16) & 0xFF
    g2 := (color2 >> 8) & 0xFF
    b2 := color2 & 0xFF
    
    return (Abs(r1 - r2) <= tolerance && Abs(g1 - g2) <= tolerance && Abs(b1 - b2) <= tolerance)
}

UpdateTargetHistory() {
    if (CurrentTarget.found) {
        TargetHistory.Push({x: CurrentTarget.x, y: CurrentTarget.y, time: A_TickCount})
        
        ; Keep only last 5 positions for prediction
        if (TargetHistory.Length() > 5)
            TargetHistory.RemoveAt(1)
    }
}

; ====== TOGGLE FUNCTIONS ======
ToggleAimbot() {
    AimbotEnabled := !AimbotEnabled
    ShowNotification("Aimbot", AimbotEnabled ? "ENABLED" : "DISABLED")
    UpdateAimbotGUI()
}

CycleAimMode() {
    if (AimMode = "Smart")
        AimMode := "Rage"
    else if (AimMode = "Rage")
        AimMode := "Legit"
    else if (AimMode = "Legit")
        AimMode := "Silent"
    else
        AimMode := "Smart"
        
    ShowNotification("Aim Mode", AimMode)
    UpdateAimbotGUI()
}

CycleTargetBone() {
    if (TargetBone = "Head")
        TargetBone := "Chest"
    else if (TargetBone = "Chest")
        TargetBone := "Auto"
    else
        TargetBone := "Head"
        
    ShowNotification("Target Bone", TargetBone)
    UpdateAimbotGUI()
}

CycleTargetType() {
    if (TargetType = "Enemy")
        TargetType := "Zombie"
    else if (TargetType = "Zombie")
        TargetType := "Boss"
    else if (TargetType = "Boss")
        TargetType := "All"
    else
        TargetType := "Enemy"
        
    ShowNotification("Target Type", TargetType)
    UpdateAimbotGUI()
}

TogglePrediction() {
    PredictionEnabled := !PredictionEnabled
    ShowNotification("Prediction", PredictionEnabled ? "ON" : "OFF")
    UpdateAimbotGUI()
}

ToggleAutoShoot() {
    AutoShoot := !AutoShoot
    ShowNotification("Auto Shoot", AutoShoot ? "ON" : "OFF")
    UpdateAimbotGUI()
}

ToggleTriggerBot() {
    TriggerBot := !TriggerBot
    ShowNotification("Trigger Bot", TriggerBot ? "ON" : "OFF")
    UpdateAimbotGUI()
}

ToggleMultiTarget() {
    MultiTargetMode := !MultiTargetMode
    ShowNotification("Multi Target", MultiTargetMode ? "ON" : "OFF")
    UpdateAimbotGUI()
}

EmergencyDisable() {
    AimbotEnabled := false
    AutoShoot := false
    TriggerBot := false
    ShowNotification("EMERGENCY", "ALL DISABLED")
    UpdateAimbotGUI()
}

ShowNotification(feature, status) {
    TrayTip, MW3 Ultimate Aimbot, %feature%: %status%, 2, 1
}

; ====== GUI SYSTEM ======
CreateAimbotGUI() {
    Gui, Add, Text, x10 y10 w300 h25 Center, MW3/MWZ ULTIMATE AIMBOT
    Gui, Add, Text, x10 y40 w300 h2 0x10
    
    ; Status display
    Gui, Add, Text, x10 y50 w150 h20 vAimbotStatus, Aimbot: OFF
    Gui, Add, Text, x160 y50 w150 h20 vTargetStatus, Target: None
    Gui, Add, Text, x10 y70 w150 h20 vModeStatus, Mode: Smart
    Gui, Add, Text, x160 y70 w150 h20 vBoneStatus, Bone: Head
    
    ; Feature status
    Gui, Add, Text, x10 y100 w150 h20 vPredictionStatus, Prediction: ON
    Gui, Add, Text, x160 y100 w150 h20 vAutoShootStatus, Auto Shoot: OFF
    Gui, Add, Text, x10 y120 w150 h20 vTriggerStatus, Trigger Bot: OFF
    Gui, Add, Text, x160 y120 w150 h20 vTypeStatus, Type: Enemy
    
    ; Statistics
    Gui, Add, Text, x10 y150 w300 h20 vStatsText, Shots Fired: 0
    
    ; Controls
    Gui, Add, Text, x10 y180 w300 h100, Hotkeys:`nF1=Toggle Aimbot  F2=Cycle Mode  F3=Cycle Bone  F4=Prediction`nF5=Auto Shoot  F6=Trigger Bot  F7=Multi Target  F8=Target Type`nF9=EMERGENCY DISABLE  Insert=GUI  Home=Settings
    
    ; Apply ultimate protection
    Gui, +LastFound +AlwaysOnTop -Caption +ToolWindow +E0x08000000
    hWnd := WinExist()
    DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
    WinSet, ExStyle, +0x00200000, ahk_id %hWnd%
    
    Gui, Show, w320 h290, MW3 Ultimate Aimbot
}

UpdateAimbotGUI() {
    GuiControl,, AimbotStatus, Aimbot: %AimbotEnabled% ? "ON" : "OFF"
    GuiControl,, TargetStatus, Target: %CurrentTarget.found% ? CurrentTarget.type : "None"
    GuiControl,, ModeStatus, Mode: %AimMode%
    GuiControl,, BoneStatus, Bone: %TargetBone%
    GuiControl,, PredictionStatus, Prediction: %PredictionEnabled% ? "ON" : "OFF"
    GuiControl,, AutoShootStatus, Auto Shoot: %AutoShoot% ? "ON" : "OFF"
    GuiControl,, TriggerStatus, Trigger Bot: %TriggerBot% ? "ON" : "OFF"
    GuiControl,, TypeStatus, Type: %TargetType%
    GuiControl,, StatsText, Shots Fired: %ShotsFired%
}

ToggleGUI() {
    static GUIVisible := true
    if (GUIVisible) {
        Gui, Hide
        GUIVisible := false
    } else {
        Gui, Show
        GUIVisible := true
    }
}

ShowSettings() {
    MsgBox, 0, Ultimate Aimbot Settings,
    (
    MW3/MWZ ULTIMATE AIMBOT SYSTEM
    
    FEATURES:
    • Smart targeting with prediction
    • Multiple aim modes (Smart/Rage/Legit/Silent)
    • Bone targeting (Head/Chest/Auto)
    • Target types (Enemy/Zombie/Boss/All)
    • Auto shoot and trigger bot
    • Anti-shake and smoothing
    • Screenshot protection
    
    MODES:
    • Smart: Balanced targeting with priority
    • Rage: Instant aim, closest target
    • Legit: Smooth aim, looks human
    • Silent: Hidden aim assistance
    
    SAFETY:
    • Multiple anti-detection layers
    • Randomized movements
    • Rate limiting
    • Emergency disable (F9)
    )
}

; ====== STARTUP ======
CreateAimbotGUI()
ShowNotification("ULTIMATE AIMBOT", "LOADED - Press Home for help")

; ====== EXIT ======
GuiClose:
ExitApp
