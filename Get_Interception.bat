@echo off
echo ========================================
echo MW3 AI ULTIMATE - GET INTERCEPTION
echo ========================================
echo.

echo This will help you get the Interception binaries.
echo.

echo METHOD 1: Automatic Download (Recommended)
echo -------------------------------------------
echo 1. Right-click "Download_Interception.ps1"
echo 2. Select "Run with PowerShell"
echo 3. If prompted, allow the script to run
echo.

echo METHOD 2: Manual Download (If automatic fails)
echo -----------------------------------------------
echo 1. Go to: https://github.com/oblitum/Interception/releases
echo 2. Download "Interception.zip" (NOT source code)
echo 3. Extract it to this folder
echo 4. Copy these files to this directory:
echo    - install-interception.exe
echo    - uninstall-interception.exe  
echo    - interception.dll
echo.

echo METHOD 3: Direct Download Links
echo --------------------------------
echo If the above methods don't work, try these direct links:
echo.
echo Latest Release:
echo https://github.com/oblitum/Interception/releases/download/v1.0.1/Interception.zip
echo.

echo WHAT YOU NEED:
echo ✓ install-interception.exe (driver installer)
echo ✓ uninstall-interception.exe (driver uninstaller)
echo ✓ interception.dll (library for our system)
echo.

echo ========================================
echo CURRENT STATUS CHECK
echo ========================================
echo.

if exist "install-interception.exe" (
    echo ✓ install-interception.exe - FOUND
) else (
    echo ✗ install-interception.exe - MISSING
)

if exist "uninstall-interception.exe" (
    echo ✓ uninstall-interception.exe - FOUND
) else (
    echo ✗ uninstall-interception.exe - MISSING
)

if exist "interception.dll" (
    echo ✓ interception.dll - FOUND
) else (
    echo ✗ interception.dll - MISSING
)

echo.

if exist "install-interception.exe" (
    if exist "interception.dll" (
        echo ========================================
        echo ✓ INTERCEPTION BINARIES READY!
        echo ========================================
        echo.
        echo You have the required files!
        echo.
        echo NEXT STEPS:
        echo 1. Right-click "install-interception.exe"
        echo 2. Select "Run as administrator"
        echo 3. Restart your computer (REQUIRED)
        echo 4. Run the MW3 AI Ultimate System
        echo.
        echo Would you like to install the driver now? (y/n)
        set /p choice=
        if /i "%choice%"=="y" (
            echo.
            echo Installing Interception driver...
            echo Right-click install-interception.exe and select "Run as administrator"
            echo.
            start "" "install-interception.exe"
        )
    ) else (
        echo ⚠️  You have the installer but missing interception.dll
        echo    Please download the complete Interception package
    )
) else (
    echo ⚠️  INTERCEPTION BINARIES MISSING
    echo.
    echo Please use one of the methods above to get the files.
    echo.
    echo TIP: Try running "Download_Interception.ps1" first!
)

echo.
echo ========================================
pause
