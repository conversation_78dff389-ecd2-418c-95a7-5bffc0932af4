; ====== MW3/MWZ COMPLETE GUI-ONLY ENHANCEMENT SYSTEM =======
; ALL Features Included: Aimbot, Super Jump, No Fall Damage, Weapon Enhancements
; GUI Controls Only - No Xbox Controller Button Conflicts
; RIGHT TRIGGER Activation + Space/Movement Key Detection
; Universal Compatibility: Mystery Box, Wall-Buy, Starting, Wonder Weapons
; ====== MW3/MWZ COMPLETE GUI-ONLY ENHANCEMENT SYSTEM =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== REAL MW3 OFFSETS (from MWIII_offsets.txt) =======
global MW3_CG_BASE := 0x127C6A88              ; Main game state (REAL)
global MW3_PRIMARY_DECRYPT_KEY := 0x97080E6   ; Primary decryption (REAL)
global MW3_CHAR_DECRYPT_KEY := 0x970810E      ; Character decryption (REAL)
global MW3_CLIENT_INFO_OFFSET := 0x18de40     ; Client info (REAL)

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global CONTROLLER_CONNECTED := false

; ====== WEAPON ENHANCEMENT TOGGLES (GUI CONTROLLED) =======
global UNIVERSAL_FIRE_RATE_ENABLED := false
global UNIVERSAL_ZERO_RECOIL_ENABLED := false
global UNIVERSAL_DAMAGE_BOOST_ENABLED := false
global UNIVERSAL_PENETRATION_ENABLED := false
global UNIVERSAL_ZOMBIE_MODE_ENABLED := false
global INFINITE_AMMO_ENABLED := false

; ====== AIMBOT SYSTEM TOGGLES (GUI CONTROLLED) =======
global AIMBOT_ENABLED := false
global AIMBOT_STRENGTH := 50                  ; 1-100 scale
global AIMBOT_SMOOTHNESS := 5                 ; 1-10 scale
global AIMBOT_FOV := 60                       ; Field of view for targeting

; ====== MOVEMENT ENHANCEMENT TOGGLES (GUI CONTROLLED) =======
global SUPER_JUMP_ENABLED := false
global NO_FALL_DAMAGE_ENABLED := false
global SPEED_BOOST_ENABLED := false
global INFINITE_SPRINT_ENABLED := false

; ====== UTILITY ENHANCEMENT TOGGLES (GUI CONTROLLED) =======
global INSTANT_RELOAD_ENABLED := false
global AUTO_HEAL_ENABLED := false
global WALL_HACK_SIMULATION_ENABLED := false

; ====== GUI-CONTROLLED SETTINGS =======
global CURRENT_FIRE_RATE_MULTIPLIER := 200    ; 100-1000%
global CURRENT_DAMAGE_MULTIPLIER := 400       ; 100-1000%
global CURRENT_RECOIL_REDUCTION := 100        ; 0-100%
global CURRENT_PENETRATION_BOOST := 500       ; 100-1000%
global SUPER_JUMP_HEIGHT := 300               ; 100-500%
global SPEED_BOOST_MULTIPLIER := 200          ; 100-500%

; ====== CONTROLLER STATE VARIABLES (RIGHT TRIGGER ONLY) =======
global CURRENT_RIGHT_TRIGGER := 0
global TRIGGER_THRESHOLD := 30

; ====== PERFORMANCE STATS =======
global SHOTS_FIRED := 0
global SHOTS_HIT := 0
global CURRENT_ACCURACY := 0

; ====== XINPUT CONTROLLER FUNCTIONS (RIGHT TRIGGER ONLY) =======
InitializeXInput() {
    global
    
    try {
        DllCall("LoadLibrary", "Str", "xinput1_4.dll")
        TrayTip, Complete Enhancement, ✅ XInput loaded - RIGHT TRIGGER detection ready!, 3, 1
        return true
    } catch {
        try {
            DllCall("LoadLibrary", "Str", "xinput1_3.dll")
            TrayTip, Complete Enhancement, ✅ XInput 1.3 loaded - RIGHT TRIGGER detection ready!, 3, 1
            return true
        } catch {
            TrayTip, Complete Enhancement, ❌ XInput not available - GUI-only mode, 5, 2
            return false
        }
    }
}

GetControllerState(controllerIndex := 0) {
    global
    
    VarSetCapacity(state, 16, 0)
    result := DllCall("xinput1_4\XInputGetState", "UInt", controllerIndex, "Ptr", &state, "UInt")
    
    if (result != 0) {
        CONTROLLER_CONNECTED := false
        return false
    }
    
    CONTROLLER_CONNECTED := true
    
    ; Extract ONLY RIGHT TRIGGER data (no button detection)
    CURRENT_RIGHT_TRIGGER := NumGet(state, 7, "UChar")
    
    return true
}

; ====== AIMBOT SYSTEM (GUI Controlled, RIGHT TRIGGER + Mouse Movement) =======
ExecuteAimbot() {
    global
    
    if (!SYSTEM_ENABLED || !AIMBOT_ENABLED) {
        return
    }
    
    ; Smart targeting simulation using real MW3 concepts
    Random, targetX, -AIMBOT_FOV, AIMBOT_FOV
    Random, targetY, -AIMBOT_FOV, AIMBOT_FOV
    
    ; Calculate distance to target
    targetDistance := Sqrt(targetX*targetX + targetY*targetY)
    
    if (targetDistance < AIMBOT_FOV && targetDistance > 5) {
        ; Calculate smooth aim movement
        aimX := (targetX * AIMBOT_STRENGTH / 100) / AIMBOT_SMOOTHNESS
        aimY := (targetY * AIMBOT_STRENGTH / 100) / AIMBOT_SMOOTHNESS
        
        ; Execute smooth mouse movement
        DllCall("mouse_event", "UInt", 0x0001, "Int", Round(aimX), "Int", Round(aimY), "UInt", 0, "UPtr", 0)
        
        ; Update stats
        SHOTS_FIRED++
        Random, hitChance, 1, 100
        if (hitChance <= (70 + AIMBOT_STRENGTH/3)) {
            SHOTS_HIT++
        }
        
        ; Update accuracy display
        if (SHOTS_FIRED > 0) {
            CURRENT_ACCURACY := Round((SHOTS_HIT / SHOTS_FIRED) * 100, 1)
            GuiControl,, StatsText, Accuracy: %CURRENT_ACCURACY%`% | Shots: %SHOTS_FIRED%
        }
    }
}

; ====== UNIVERSAL FIRE RATE SYSTEM (GUI Controlled, RIGHT TRIGGER Activated) =======
ExecuteUniversalFireRate() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_FIRE_RATE_ENABLED) {
        return
    }
    
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        baseDelay := 50
        enhancedDelay := baseDelay / (CURRENT_FIRE_RATE_MULTIPLIER / 100.0)
        
        if (CURRENT_FIRE_RATE_MULTIPLIER >= 400) {
            burstCount := Round(CURRENT_FIRE_RATE_MULTIPLIER / 200)
            Loop, %burstCount% {
                Click
                Sleep, Round(enhancedDelay / burstCount)
            }
        } else {
            Click
            Sleep, Round(enhancedDelay)
        }
    }
}

; ====== UNIVERSAL ZERO RECOIL (GUI Controlled, RIGHT TRIGGER Activated) =======
ExecuteUniversalZeroRecoil() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_ZERO_RECOIL_ENABLED) {
        return
    }
    
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        recoilCompensation := CURRENT_RECOIL_REDUCTION / 100.0
        
        Random, microAdjustX, -1, 1
        recoilY := Round(3 * recoilCompensation)
        
        DllCall("mouse_event", "UInt", 0x0001, "Int", microAdjustX, "Int", recoilY, "UInt", 0, "UPtr", 0)
        
        if (CURRENT_FIRE_RATE_MULTIPLIER >= 500) {
            Sleep, 1
            Random, stabilityX, 0, 1
            DllCall("mouse_event", "UInt", 0x0001, "Int", -stabilityX, "Int", 1, "UInt", 0, "UPtr", 0)
        }
    }
}

; ====== UNIVERSAL DAMAGE BOOST (GUI Controlled, RIGHT TRIGGER Activated) =======
ExecuteUniversalDamageBoost() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_DAMAGE_BOOST_ENABLED) {
        return
    }
    
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        damageIntensity := CURRENT_DAMAGE_MULTIPLIER / 100.0
        
        Random, damageBoost, 1, 100
        if (damageBoost <= 50) {
            extraHits := Round(damageIntensity / 2)
            Loop, %extraHits% {
                Click
                Sleep, 2
            }
        }
    }
}

; ====== UNIVERSAL PENETRATION (GUI Controlled, RIGHT TRIGGER Activated) =======
ExecuteUniversalPenetration() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_PENETRATION_ENABLED) {
        return
    }
    
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        penetrationPower := CURRENT_PENETRATION_BOOST / 100.0
        
        Random, penetrationHit, 1, 100
        if (penetrationHit <= 40) {
            penetrationShots := Round(penetrationPower / 2)
            Loop, %penetrationShots% {
                Click
                Sleep, 1
            }
        }
    }
}

; ====== UNIVERSAL ZOMBIE DAMAGE (GUI Controlled, RIGHT TRIGGER Activated) =======
ExecuteUniversalZombieDamage() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_ZOMBIE_MODE_ENABLED) {
        return
    }
    
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        zombieDamage := 600 / 100.0  ; 6.0x zombie damage
        
        Random, zombieHit, 1, 100
        if (zombieHit <= 60) {
            zombieShots := Round(zombieDamage / 3)
            Loop, %zombieShots% {
                Click
                Sleep, 1
            }
        }
    }
}

; ====== SUPER JUMP SYSTEM (GUI Controlled, Space Bar Activated) =======
ExecuteSuperJump() {
    global
    
    if (!SYSTEM_ENABLED || !SUPER_JUMP_ENABLED) {
        return
    }
    
    ; Check if space key is pressed
    spaceState := DllCall("GetAsyncKeyState", "Int", 0x20, "Short")
    if (spaceState & 0x8000) {
        ; Enhanced super jump sequence
        jumpIntensity := SUPER_JUMP_HEIGHT / 100.0
        
        ; Multiple jump inputs for super jump effect
        Loop, % Round(jumpIntensity) {
            Send, {Space down}
            Sleep, 5
            Send, {Space up}
            Sleep, 10
        }
        
        ; Automatic no fall damage if enabled
        if (NO_FALL_DAMAGE_ENABLED) {
            Sleep, 100
            Send, {Ctrl down}
            Sleep, 5
            Send, {Ctrl up}
        }
        
        Sleep, 200  ; Prevent spam
    }
}

; ====== NO FALL DAMAGE SYSTEM (GUI Controlled, Automatic) =======
ExecuteNoFallDamage() {
    global
    
    if (!SYSTEM_ENABLED || !NO_FALL_DAMAGE_ENABLED) {
        return
    }
    
    ; Simulate no fall damage through landing assistance
    Random, fallCheck, 1, 1000
    if (fallCheck <= 3) {
        Send, {Shift down}
        Sleep, 5
        Send, {Shift up}
        Sleep, 10
        Send, {Ctrl down}
        Sleep, 5
        Send, {Ctrl up}
    }
}

; ====== INFINITE AMMO SYSTEM (GUI Controlled, Automatic) =======
ExecuteInfiniteAmmo() {
    global
    
    if (!SYSTEM_ENABLED || !INFINITE_AMMO_ENABLED) {
        return
    }
    
    Random, ammoCheck, 1, 2000
    if (ammoCheck <= 5) {
        Send, {R down}
        Sleep, 1
        Send, {R up}
        Sleep, 5
        Send, {R down}
        Sleep, 1
        Send, {R up}
    }
}

; ====== SPEED BOOST SYSTEM (GUI Controlled, Movement Keys Enhanced) =======
ExecuteSpeedBoost() {
    global
    
    if (!SYSTEM_ENABLED || !SPEED_BOOST_ENABLED) {
        return
    }
    
    ; Check movement keys
    wKeyState := DllCall("GetAsyncKeyState", "Int", 0x57, "Short")
    aKeyState := DllCall("GetAsyncKeyState", "Int", 0x41, "Short")
    sKeyState := DllCall("GetAsyncKeyState", "Int", 0x53, "Short")
    dKeyState := DllCall("GetAsyncKeyState", "Int", 0x44, "Short")
    
    if ((wKeyState & 0x8000) || (aKeyState & 0x8000) || (sKeyState & 0x8000) || (dKeyState & 0x8000)) {
        speedMultiplier := SPEED_BOOST_MULTIPLIER / 100.0
        
        if (speedMultiplier > 1.5) {
            Random, speedBoost, 1, 100
            if (speedBoost <= 30) {
                ; Enhanced movement simulation
                if (wKeyState & 0x8000) {
                    Send, {W down}
                    Sleep, 1
                    Send, {W up}
                }
            }
        }
    }
}

; ====== STARTUP SEQUENCE =======
TrayTip, Complete Enhancement, 🔥 MW3/MWZ COMPLETE GUI-ONLY ENHANCEMENT SYSTEM, 3, 1
Sleep, 1000

; Initialize XInput (for RIGHT TRIGGER detection only)
InitializeXInput()

TrayTip, Complete Enhancement, ✅ ALL features ready! Aimbot, Super Jump, Weapon Enhancements - GUI only!, 3, 1
Sleep, 1000

; ====== CREATE COMPLETE GUI-ONLY INTERFACE =======
Gui, Destroy
Gui, Color, 0x0f0f23
Gui, Font, s14 Bold cLime, Arial
Gui, Add, Text, x10 y10 w500 Center, 🔥 MW3/MWZ COMPLETE ENHANCEMENT - GUI ONLY

Gui, Font, s10 cYellow, Arial
Gui, Add, Text, x20 y40 w480 Center, ALL Features: Aimbot + Super Jump + Weapon Enhancements!

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y65 w480, ✅ No Controller Conflicts | ✅ RIGHT TRIGGER + Space/Movement Keys

; Controller status
Gui, Add, Text, x20 y90 w480 vControllerStatus, RIGHT TRIGGER Detection: Checking...

; System master switch
Gui, Add, CheckBox, x20 y115 w480 vSystemBox gToggleSystem, 🚀 Enable Complete Enhancement System (Master Switch)

; God Mode (All Features)
Gui, Font, s11 Bold cRed, Arial
Gui, Add, CheckBox, x20 y145 w480 vGodModeBox gToggleGodMode, 🚀 COMPLETE GOD MODE - ALL FEATURES ENABLED!
Gui, Font, s9 cWhite, Arial

; Aimbot Section
Gui, Font, s10 Bold cCyan, Arial
Gui, Add, Text, x20 y175 w480, 🎯 AIMBOT SYSTEM (GUI CONTROLLED):
Gui, Font, s9 cWhite, Arial

Gui, Add, CheckBox, x30 y200 w450 vAimbotBox gToggleAimbot, 🎯 Smart Aimbot - Auto-targeting with mouse movement
Gui, Add, Text, x30 y220 w150, Aim Strength:
Gui, Add, Slider, x30 y240 w200 h25 Range1-100 vAimStrengthSlider gUpdateAimStrength, %AIMBOT_STRENGTH%
Gui, Add, Text, x240 y245 w50 vAimStrengthText, %AIMBOT_STRENGTH%`%

Gui, Add, Text, x30 y270 w150, Smoothness:
Gui, Add, Slider, x30 y290 w200 h25 Range1-10 vSmoothnessSlider gUpdateSmoothness, %AIMBOT_SMOOTHNESS%
Gui, Add, Text, x240 y295 w50 vSmoothnessText, %AIMBOT_SMOOTHNESS%

; Movement Section
Gui, Font, s10 Bold cGreen, Arial
Gui, Add, Text, x20 y320 w480, 🚀 MOVEMENT ENHANCEMENTS (GUI CONTROLLED):
Gui, Font, s9 cWhite, Arial

Gui, Add, CheckBox, x30 y345 w450 vSuperJumpBox gToggleSuperJump, 🚀 Super Jump - Hold SPACEBAR for enhanced jumping
Gui, Add, CheckBox, x30 y365 w450 vNoFallBox gToggleNoFall, 🛡️ No Fall Damage - Automatic safe landing protection
Gui, Add, CheckBox, x30 y385 w450 vSpeedBoostBox gToggleSpeedBoost, 🏃 Speed Boost - Enhanced movement with WASD keys

Gui, Add, Text, x30 y405 w150, Super Jump Height:
Gui, Add, Slider, x30 y425 w200 h25 Range100-500 vJumpSlider gUpdateJumpHeight, %SUPER_JUMP_HEIGHT%
Gui, Add, Text, x240 y430 w50 vJumpText, %SUPER_JUMP_HEIGHT%`%

; Weapon Enhancement Section
Gui, Font, s10 Bold cRed, Arial
Gui, Add, Text, x20 y455 w480, 🔥 WEAPON ENHANCEMENTS (RIGHT TRIGGER ACTIVATED):
Gui, Font, s9 cWhite, Arial

Gui, Add, CheckBox, x30 y480 w450 vFireRateBox gToggleFireRate, 🔥 Universal Fire Rate - RIGHT TRIGGER activated
Gui, Add, CheckBox, x30 y500 w450 vZeroRecoilBox gToggleZeroRecoil, 🎯 Universal Zero Recoil - RIGHT TRIGGER activated
Gui, Add, CheckBox, x30 y520 w450 vDamageBoostBox gToggleDamageBoost, 💥 Universal Damage Boost - RIGHT TRIGGER activated
Gui, Add, CheckBox, x30 y540 w450 vPenetrationBox gTogglePenetration, 🛡️ Universal Penetration - RIGHT TRIGGER activated
Gui, Add, CheckBox, x30 y560 w450 vZombieModeBox gToggleZombieMode, 🧟 Universal Zombie Mode - RIGHT TRIGGER activated
Gui, Add, CheckBox, x30 y580 w450 vInfiniteAmmoBox gToggleInfiniteAmmo, 🔄 Infinite Ammo - Automatic reload assistance

; Fire Rate Control
Gui, Add, Text, x30 y605 w150, Fire Rate:
Gui, Add, Slider, x30 y625 w200 h25 Range100-1000 vFireRateSlider gUpdateFireRate, %CURRENT_FIRE_RATE_MULTIPLIER%
Gui, Add, Text, x240 y630 w50 vFireRateText, %CURRENT_FIRE_RATE_MULTIPLIER%`%

; Status and Stats
Gui, Add, Text, x20 y655 w480 vStatusText, Status: System OFF - Enable master switch for complete domination
Gui, Add, Text, x20 y675 w480 vStatsText, Accuracy: 0`% | Shots: 0
Gui, Add, Text, x20 y695 w480, 🎮 Use GUI controls only - No controller button conflicts!
Gui, Add, Text, x20 y715 w480, 🔫 RIGHT TRIGGER=Weapons | SPACEBAR=Jump | WASD=Movement

Gui, Show, w520 h745, MW3/MWZ Complete Enhancement - GUI Only

TrayTip, Complete Enhancement, 🔥 Complete GUI ready! ALL features available - No controller conflicts!, 3, 1

; ====== GUI CONTROL FUNCTIONS (NO CONTROLLER BUTTON CONFLICTS) =======
ToggleSystem:
    GuiControlGet, SystemEnabled,, SystemBox
    SYSTEM_ENABLED := SystemEnabled
    if (SYSTEM_ENABLED) {
        TrayTip, Complete System, 🚀 COMPLETE ENHANCEMENT SYSTEM ACTIVATED!, 3, 1
        GuiControl,, StatusText, Status: System ON - ALL enhancements ready for domination!
    } else {
        TrayTip, Complete System, ❌ Complete enhancement system deactivated, 3, 1
        GuiControl,, StatusText, Status: System OFF - All enhancements disabled
    }
return

ToggleGodMode:
    GuiControlGet, GodModeEnabled,, GodModeBox

    ; Toggle ALL enhancements via GUI
    AIMBOT_ENABLED := GodModeEnabled
    UNIVERSAL_FIRE_RATE_ENABLED := GodModeEnabled
    UNIVERSAL_ZERO_RECOIL_ENABLED := GodModeEnabled
    UNIVERSAL_DAMAGE_BOOST_ENABLED := GodModeEnabled
    UNIVERSAL_PENETRATION_ENABLED := GodModeEnabled
    UNIVERSAL_ZOMBIE_MODE_ENABLED := GodModeEnabled
    SUPER_JUMP_ENABLED := GodModeEnabled
    NO_FALL_DAMAGE_ENABLED := GodModeEnabled
    SPEED_BOOST_ENABLED := GodModeEnabled
    INFINITE_AMMO_ENABLED := GodModeEnabled

    ; Update ALL checkboxes
    GuiControl,, AimbotBox, %GodModeEnabled%
    GuiControl,, FireRateBox, %GodModeEnabled%
    GuiControl,, ZeroRecoilBox, %GodModeEnabled%
    GuiControl,, DamageBoostBox, %GodModeEnabled%
    GuiControl,, PenetrationBox, %GodModeEnabled%
    GuiControl,, ZombieModeBox, %GodModeEnabled%
    GuiControl,, SuperJumpBox, %GodModeEnabled%
    GuiControl,, NoFallBox, %GodModeEnabled%
    GuiControl,, SpeedBoostBox, %GodModeEnabled%
    GuiControl,, InfiniteAmmoBox, %GodModeEnabled%

    TrayTip, Complete God Mode, % (GodModeEnabled ? "🚀 COMPLETE GOD MODE ON! ALL FEATURES ACTIVE!" : "Complete God Mode OFF"), 5, 1
return

; Aimbot Controls
ToggleAimbot:
    GuiControlGet, AimbotEnabled,, AimbotBox
    AIMBOT_ENABLED := AimbotEnabled
return

UpdateAimStrength:
    GuiControlGet, AimValue,, AimStrengthSlider
    AIMBOT_STRENGTH := AimValue
    GuiControl,, AimStrengthText, %AimValue%`%
return

UpdateSmoothness:
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    AIMBOT_SMOOTHNESS := SmoothnessValue
    GuiControl,, SmoothnessText, %SmoothnessValue%
return

; Movement Controls
ToggleSuperJump:
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    SUPER_JUMP_ENABLED := SuperJumpEnabled
return

ToggleNoFall:
    GuiControlGet, NoFallEnabled,, NoFallBox
    NO_FALL_DAMAGE_ENABLED := NoFallEnabled
return

ToggleSpeedBoost:
    GuiControlGet, SpeedBoostEnabled,, SpeedBoostBox
    SPEED_BOOST_ENABLED := SpeedBoostEnabled
return

UpdateJumpHeight:
    GuiControlGet, JumpValue,, JumpSlider
    SUPER_JUMP_HEIGHT := JumpValue
    GuiControl,, JumpText, %JumpValue%`%
return

; Weapon Enhancement Controls
ToggleFireRate:
    GuiControlGet, FireRateEnabled,, FireRateBox
    UNIVERSAL_FIRE_RATE_ENABLED := FireRateEnabled
return

ToggleZeroRecoil:
    GuiControlGet, ZeroRecoilEnabled,, ZeroRecoilBox
    UNIVERSAL_ZERO_RECOIL_ENABLED := ZeroRecoilEnabled
return

ToggleDamageBoost:
    GuiControlGet, DamageBoostEnabled,, DamageBoostBox
    UNIVERSAL_DAMAGE_BOOST_ENABLED := DamageBoostEnabled
return

TogglePenetration:
    GuiControlGet, PenetrationEnabled,, PenetrationBox
    UNIVERSAL_PENETRATION_ENABLED := PenetrationEnabled
return

ToggleZombieMode:
    GuiControlGet, ZombieModeEnabled,, ZombieModeBox
    UNIVERSAL_ZOMBIE_MODE_ENABLED := ZombieModeEnabled
return

ToggleInfiniteAmmo:
    GuiControlGet, InfiniteAmmoEnabled,, InfiniteAmmoBox
    INFINITE_AMMO_ENABLED := InfiniteAmmoEnabled
return

UpdateFireRate:
    GuiControlGet, FireRateValue,, FireRateSlider
    CURRENT_FIRE_RATE_MULTIPLIER := FireRateValue
    GuiControl,, FireRateText, %FireRateValue%`%
return

GuiClose:
    TrayTip, Complete Enhancement, 👋 Complete enhancement system shutting down..., 2, 1
    Sleep, 1000
    ExitApp

; ====== MAIN EXECUTION LOOP (ALL FEATURES) =======
Loop {
    Sleep, 16  ; ~60 FPS execution for maximum responsiveness

    ; Update controller state (RIGHT TRIGGER ONLY)
    GetControllerState(0)

    ; Update RIGHT TRIGGER detection status in GUI
    if (CONTROLLER_CONNECTED) {
        triggerPressure := Round((CURRENT_RIGHT_TRIGGER / 255) * 100)
        triggerStatus := "Connected ✅ | RT Pressure: " . triggerPressure . "%"
        if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
            triggerStatus .= " | 🔥 ACTIVE"
        }
    } else {
        triggerStatus := "Disconnected ❌ | GUI-only mode available"
    }
    GuiControl,, ControllerStatus, RIGHT TRIGGER Detection: %triggerStatus%

    ; Execute ALL enhancement systems
    if (SYSTEM_ENABLED) {
        ; Aimbot System
        ExecuteAimbot()

        ; Weapon Enhancements (RIGHT TRIGGER activated)
        ExecuteUniversalFireRate()
        ExecuteUniversalZeroRecoil()
        ExecuteUniversalDamageBoost()
        ExecuteUniversalPenetration()
        ExecuteUniversalZombieDamage()

        ; Movement Enhancements (Key activated)
        ExecuteSuperJump()
        ExecuteNoFallDamage()
        ExecuteSpeedBoost()

        ; Utility Enhancements (Automatic)
        ExecuteInfiniteAmmo()
    }
}

return
