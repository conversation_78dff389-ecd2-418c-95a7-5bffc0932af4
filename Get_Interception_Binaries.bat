@echo off
echo ====== INTERCEPTION BINARIES DOWNLOAD HELPER =======
echo This will help you get the correct Interception files
echo ====== INTERCEPTION BINARIES DOWNLOAD HELPER =======
echo.

echo CURRENT STATUS:
echo - You have: SOURCE CODE (needs compilation)
echo - You need: COMPILED BINARIES (ready to use)
echo.

echo STEP 1: OPENING GITHUB RELEASES PAGE...
echo Browser will open to the correct download page
pause
start https://github.com/oblitum/Interception/releases

echo.
echo STEP 2: DOWNLOAD INSTRUCTIONS
echo 1. Look for "Interception.zip" (NOT source code)
echo 2. Download the ZIP file with compiled binaries
echo 3. Extract it to: %~dp0Interception-Binaries\
echo 4. You should see: install-interception.exe and interception.dll
echo.

echo STEP 3: AFTER DOWNLOAD
echo 1. Copy interception.dll to: %~dp0
echo 2. Run install-interception.exe as Administrator
echo 3. Restart your computer
echo 4. Run Test_Interception_Driver.ahk to verify
echo.

echo Creating download folder...
mkdir "%~dp0Interception-Binaries" 2>nul

echo.
echo DOWNLOAD FOLDER CREATED: %~dp0Interception-Binaries\
echo.
echo Press any key when you've downloaded and extracted the files...
pause

echo.
echo STEP 4: CHECKING FOR FILES...
if exist "%~dp0Interception-Binaries\install-interception.exe" (
    echo ✅ Found: install-interception.exe
) else (
    echo ❌ Missing: install-interception.exe
)

if exist "%~dp0Interception-Binaries\interception.dll" (
    echo ✅ Found: interception.dll
    echo Copying interception.dll to script folder...
    copy "%~dp0Interception-Binaries\interception.dll" "%~dp0" >nul
    echo ✅ interception.dll copied successfully
) else (
    echo ❌ Missing: interception.dll
)

echo.
echo NEXT STEPS:
echo 1. Open Command Prompt as Administrator
echo 2. Navigate to: %~dp0Interception-Binaries\
echo 3. Run: install-interception.exe /install
echo 4. Restart your computer
echo 5. Run: Test_Interception_Driver.ahk
echo.

pause
