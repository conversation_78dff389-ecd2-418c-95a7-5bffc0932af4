# MW3/MWZ Game Modification Tools

Based on working techniques from UnknownCheats Black Ops 6 reversal research.

## ⚠️ IMPORTANT DISCLAIMER

**These tools are for educational purposes only. Using them in online games will result in permanent bans. Use at your own risk.**

## What's Actually Working (Based on Forum Research)

### 1. **External Color-Based Aimbots** ✅
- **Status**: Working and undetected
- **Method**: Read screen pixels, no memory injection
- **Anti-Cheat Bypass**: Uses Windows API to hide from screenshots
- **File**: `mw3_color_aimbot.ahk`

### 2. **External Memory Reading** ✅
- **Status**: Working with proper decryption
- **Method**: Read game memory from external process
- **Anti-Cheat Bypass**: No injection, complex decryption functions
- **File**: `mw3_external_memory.cpp`

### 3. **DMA Hardware** ✅
- **Status**: Most reliable method
- **Method**: PCIe card reads memory directly
- **Anti-Cheat Bypass**: Completely external to game process
- **Requirement**: Special hardware ($200-500)

## How They're Bypassing Ricochet

### **Method 1: External Tools (Most Common)**
```cpp
// Anti-screenshot protection
DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
WinSet, ExStyle, +0x00200000, ahk_id %hWnd%
```

### **Method 2: No Memory Injection**
- Read memory from separate process
- Use complex decryption functions
- Never inject code into game

### **Method 3: Hardware-Based (DMA)**
- PCIe cards that bypass Windows entirely
- Custom firmware required
- Most expensive but most reliable

## Available Modifications

### **Color Aimbot Features:**
- ✅ Rapid fire (mouse automation)
- ✅ Aim assistance 
- ✅ Prediction system
- ✅ Anti-recoil compensation
- ✅ Screenshot protection

### **Memory Tool Features:**
- ✅ Infinite health
- ✅ Infinite ammo
- ✅ Super jump
- ✅ Teleportation
- ✅ FOV modification
- ✅ Moon gravity
- ✅ Bypass patches (NOP instructions)

## Usage Instructions

### **Color Aimbot (AutoHotkey)**
1. Install AutoHotkey v1.1.37.02 (NOT v2!)
2. Set enemy nameplates to RED in game settings
3. Run `mw3_color_aimbot.ahk`
4. Press F1 to toggle aimbot
5. Adjust FOV and strength in GUI

### **Memory Tool (C++)**
1. Compile with Visual Studio or MinGW
2. Run as Administrator
3. Start MW3/MWZ
4. Run the tool
5. Use number keys for modifications

## Technical Details

### **Offsets Used (Adapted from BO6)**
```cpp
// Health system
HEALTH_BASE = 0x993FC48
HEALTH_OFFSET = 0x1E8

// Ammo system  
AMMO_BASE = 0xB9742E8
PRIMARY_AMMO = 0x18F8
SECONDARY_AMMO = 0x195C

// Position system
POSITION_BASE = 0xB9742E8
POS_X = 0x58
POS_Y = 0x60
POS_Z = 0x59
```

### **Bypass Patches (NOP Instructions)**
```cpp
// Stop health reverting after self-damage
0x1FD78E1: NOP out original instruction

// Stop equipment reverting after field upgrade
0x1BD5715: NOP out original instruction
```

## Why These Methods Work

### **1. No Code Injection**
- Tools run as separate processes
- Never inject DLLs into game
- Anti-cheat can't detect external reading

### **2. Screenshot Protection**
- GUI hidden from screen capture
- Ricochet can't see the tools
- Stream-safe and recording-safe

### **3. Legitimate Windows APIs**
- Use standard memory reading functions
- Look like normal applications
- No suspicious behavior patterns

## Detection Risks

### **Low Risk:**
- Color aimbot (external pixel reading)
- DMA hardware tools
- External memory reading (with proper decryption)

### **Medium Risk:**
- Memory writing (infinite health, ammo)
- Position modifications (teleport, super jump)
- Game state changes (FOV, gravity)

### **High Risk:**
- Rapid fire in multiplayer
- Obvious cheating behavior
- Statistical analysis detection

## Recommendations

### **For Learning/Testing:**
1. Use in single-player campaigns
2. Test on private servers
3. Offline mode only

### **For Online Use (High Risk):**
1. Use minimal settings
2. Avoid obvious cheating
3. Don't use rapid fire
4. Expect eventual detection

### **Safest Options:**
1. DMA hardware ($200-500)
2. Color aimbot with low settings
3. External memory reading only

## Forum Sources

Based on research from:
- UnknownCheats BO6 Reversal Thread (129 pages)
- Working AHK scripts with anti-Ricochet protection
- DMA guides and hardware recommendations
- External memory reading techniques

## Legal Notice

This is for educational purposes only. The authors are not responsible for any bans, legal issues, or other consequences from using these tools. Use at your own risk and only in appropriate environments.
