@echo off
title MW3 AI Ultimate - Manual File Copy
color 0A

echo.
echo ========================================
echo   MANUAL INTERCEPTION FILE COPY
echo ========================================
echo.

echo Let's find and copy the files manually...
echo.

echo [1] Looking for install-interception.exe...
if exist "Interception\command line installer\install-interception.exe" (
    copy "Interception\command line installer\install-interception.exe" . >nul 2>&1
    echo [✓] Found and copied install-interception.exe
) else (
    echo [!] Not found in expected location, searching...
    for /r "Interception" %%f in (install-interception.exe) do (
        if exist "%%f" (
            copy "%%f" . >nul 2>&1
            echo [✓] Found and copied install-interception.exe from %%f
            goto :found_installer
        )
    )
    echo [✗] install-interception.exe not found anywhere
    :found_installer
)

echo.
echo [2] Looking for interception.dll...
if exist "Interception\library\x64\interception.dll" (
    copy "Interception\library\x64\interception.dll" . >nul 2>&1
    echo [✓] Found and copied interception.dll (x64)
) else (
    echo [!] Not found in expected location, searching...
    for /r "Interception" %%f in (interception.dll) do (
        if exist "%%f" (
            copy "%%f" . >nul 2>&1
            echo [✓] Found and copied interception.dll from %%f
            goto :found_dll
        )
    )
    echo [✗] interception.dll not found anywhere
    :found_dll
)

echo.
echo [3] Looking for uninstall-interception.exe (optional)...
if exist "Interception\command line installer\uninstall-interception.exe" (
    copy "Interception\command line installer\uninstall-interception.exe" . >nul 2>&1
    echo [✓] Found and copied uninstall-interception.exe
) else (
    for /r "Interception" %%f in (uninstall-interception.exe) do (
        if exist "%%f" (
            copy "%%f" . >nul 2>&1
            echo [✓] Found and copied uninstall-interception.exe from %%f
            goto :found_uninstaller
        )
    )
    echo [!] uninstall-interception.exe not found (this is normal)
    :found_uninstaller
)

echo.
echo ========================================
echo   FINAL CHECK
echo ========================================
echo.

if exist "install-interception.exe" (
    echo [✓] install-interception.exe - READY
    set "ready=1"
) else (
    echo [✗] install-interception.exe - MISSING
    set "ready=0"
)

if exist "interception.dll" (
    echo [✓] interception.dll - READY
) else (
    echo [✗] interception.dll - MISSING
    set "ready=0"
)

if exist "uninstall-interception.exe" (
    echo [✓] uninstall-interception.exe - READY
) else (
    echo [!] uninstall-interception.exe - Not found (optional)
)

echo.

if "%ready%"=="1" (
    echo ========================================
    echo   SUCCESS! FILES COPIED
    echo ========================================
    echo.
    echo Great! The required files are now ready.
    echo.
    echo NEXT STEP: Install the Interception driver
    echo.
    choice /c YN /m "Install driver now (Y/N)"
    
    if errorlevel 2 goto :manual_instructions
    if errorlevel 1 goto :install_now
    
    :install_now
    echo.
    echo Installing Interception driver...
    echo [!] Click YES when Windows asks for permission
    echo.
    
    start "" /wait "install-interception.exe"
    
    echo.
    echo Driver installation completed!
    echo.
    echo ========================================
    echo   RESTART REQUIRED!
    echo ========================================
    echo.
    echo You MUST restart your computer now for the driver to work.
    echo.
    choice /c YN /m "Restart now (Y/N)"
    
    if errorlevel 1 (
        echo Restarting in 5 seconds... (Press Ctrl+C to cancel)
        timeout /t 5
        shutdown /r /t 0
    )
    
    goto :end
    
    :manual_instructions
    echo.
    echo Manual installation:
    echo 1. Right-click install-interception.exe
    echo 2. Select "Run as administrator"
    echo 3. Restart your computer
    echo 4. Run MW3 AI Ultimate System
    echo.
    
) else (
    echo ========================================
    echo   FILES STILL MISSING
    echo ========================================
    echo.
    echo Let's try a different approach...
    echo.
    echo MANUAL METHOD:
    echo 1. Open the Interception folder in Windows Explorer
    echo 2. Look for install-interception.exe (might be in a subfolder)
    echo 3. Copy it to this folder (where this .bat file is)
    echo 4. Look for interception.dll (might be in library/x64/ or similar)
    echo 5. Copy it to this folder too
    echo.
    echo Then run this script again to verify.
    echo.
)

:end
echo ========================================
pause
