@echo off
title MW3/MWZ Ultimate Mod Suite Installer
color 0A

echo ========================================
echo   MW3/MWZ ULTIMATE MOD SUITE INSTALLER
echo   Automated Setup System v2.0
echo ========================================
echo.

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [+] Administrator privileges confirmed
) else (
    echo [-] This installer requires administrator privileges
    echo [-] Please right-click and "Run as administrator"
    pause
    exit /b 1
)

:: Create installation directory
set INSTALL_DIR=%USERPROFILE%\Documents\MW3_Ultimate_Mods
echo [+] Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
cd /d "%INSTALL_DIR%"

:: Download and install AutoHotkey v1.1.37.02
echo.
echo [+] Installing AutoHotkey v1.1.37.02...
if not exist "AutoHotkey_1.1.37.02_setup.exe" (
    echo [+] Downloading AutoHotkey...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/AutoHotkey/AutoHotkey/releases/download/v1.1.37.02/AutoHotkey_1.1.37.02_setup.exe' -OutFile 'AutoHotkey_1.1.37.02_setup.exe'}"
    if exist "AutoHotkey_1.1.37.02_setup.exe" (
        echo [+] AutoHotkey downloaded successfully
        echo [+] Installing AutoHotkey...
        AutoHotkey_1.1.37.02_setup.exe /S
        timeout /t 10 /nobreak >nul
        echo [+] AutoHotkey installation complete
    ) else (
        echo [-] Failed to download AutoHotkey
        echo [-] Please download manually from: https://www.autohotkey.com/
        pause
    )
) else (
    echo [+] AutoHotkey installer already exists
)

:: Install Visual C++ Redistributables (for C++ tools)
echo.
echo [+] Installing Visual C++ Redistributables...
if not exist "vc_redist.x64.exe" (
    echo [+] Downloading VC++ Redistributables...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile 'vc_redist.x64.exe'}"
    if exist "vc_redist.x64.exe" (
        echo [+] Installing VC++ Redistributables...
        vc_redist.x64.exe /quiet /norestart
        echo [+] VC++ Redistributables installed
    ) else (
        echo [-] Failed to download VC++ Redistributables
    )
)

:: Create mod files directory structure
echo.
echo [+] Creating mod files structure...
mkdir "Controller_Mods" 2>nul
mkdir "Memory_Tools" 2>nul
mkdir "Aimbot_System" 2>nul
mkdir "Configs" 2>nul
mkdir "Logs" 2>nul

:: Copy mod files (assuming they're in the same directory as installer)
echo [+] Installing mod files...

:: Controller Mods
if exist "..\MW3_Ultimate_Controller.ahk" (
    copy "..\MW3_Ultimate_Controller.ahk" "Controller_Mods\"
    echo [+] Ultimate Controller mod installed
)

if exist "..\xbox_controller_mods.ahk" (
    copy "..\xbox_controller_mods.ahk" "Controller_Mods\"
    echo [+] Xbox Controller mod installed
)

:: Memory Tools
if exist "..\MW3_Ultimate_Memory.cpp" (
    copy "..\MW3_Ultimate_Memory.cpp" "Memory_Tools\"
    echo [+] Ultimate Memory tool source installed
)

if exist "..\mw3_external_memory.cpp" (
    copy "..\mw3_external_memory.cpp" "Memory_Tools\"
    echo [+] External Memory tool source installed
)

:: Aimbot System
if exist "..\MW3_Ultimate_Aimbot.ahk" (
    copy "..\MW3_Ultimate_Aimbot.ahk" "Aimbot_System\"
    echo [+] Ultimate Aimbot installed
)

if exist "..\mw3_color_aimbot.ahk" (
    copy "..\mw3_color_aimbot.ahk" "Aimbot_System\"
    echo [+] Color Aimbot installed
)

:: Create launcher script
echo [+] Creating launcher script...
(
echo @echo off
echo title MW3/MWZ Ultimate Mod Suite Launcher
echo color 0B
echo.
echo ========================================
echo   MW3/MWZ ULTIMATE MOD SUITE LAUNCHER
echo ========================================
echo.
echo Select which tool to launch:
echo.
echo 1. Ultimate Controller Mods
echo 2. Xbox Controller Mods  
echo 3. Ultimate Aimbot System
echo 4. Color Aimbot
echo 5. Memory Tool ^(Compile First^)
echo 6. Setup Instructions
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "
echo.

if "%%choice%%"=="1" (
    echo [+] Launching Ultimate Controller Mods...
    start "" "Controller_Mods\MW3_Ultimate_Controller.ahk"
) else if "%%choice%%"=="2" (
    echo [+] Launching Xbox Controller Mods...
    start "" "Controller_Mods\xbox_controller_mods.ahk"
) else if "%%choice%%"=="3" (
    echo [+] Launching Ultimate Aimbot System...
    start "" "Aimbot_System\MW3_Ultimate_Aimbot.ahk"
) else if "%%choice%%"=="4" (
    echo [+] Launching Color Aimbot...
    start "" "Aimbot_System\mw3_color_aimbot.ahk"
) else if "%%choice%%"=="5" (
    echo [+] Opening Memory Tools folder...
    echo [!] You need to compile the .cpp files first
    echo [!] Use Visual Studio or MinGW to compile
    start "" "Memory_Tools\"
) else if "%%choice%%"=="6" (
    echo [+] Opening setup instructions...
    start "" "README.txt"
) else if "%%choice%%"=="7" (
    exit
) else (
    echo [-] Invalid choice
    pause
    goto :start
)

echo.
echo [+] Tool launched! Check for any error messages.
pause
) > "MW3_Ultimate_Launcher.bat"

:: Create configuration files
echo [+] Creating default configurations...

:: Controller config
(
echo ; MW3 Ultimate Controller Configuration
echo ; Edit these values to customize your experience
echo.
echo [RapidFire]
echo Enabled=false
echo Rate=60
echo BurstMode=3
echo.
echo [SuperJump]
echo Enabled=false
echo Height=4
echo Type=multi
echo.
echo [AntiRecoil]
echo Enabled=false
echo Strength=3
echo Pattern=adaptive
echo.
echo [Profile_Balanced]
echo RapidFireRate=80
echo SuperJumpHeight=3
echo AntiRecoilStrength=2
echo.
echo [Profile_Aggressive]
echo RapidFireRate=50
echo SuperJumpHeight=5
echo AntiRecoilStrength=4
echo.
echo [Profile_Stealth]
echo RapidFireRate=120
echo SuperJumpHeight=2
echo AntiRecoilStrength=1
) > "Configs\controller_config.ini"

:: Aimbot config
(
echo ; MW3 Ultimate Aimbot Configuration
echo.
echo [Targeting]
echo EnemyColor=0xFF0000
echo ZombieColor=0xFFFF00
echo BossColor=0xFF8000
echo ColorTolerance=25
echo.
echo [Aimbot]
echo FOVRadius=200
echo AimSpeed=0.7
echo MaxDistance=500
echo.
echo [Prediction]
echo Enabled=true
echo Strength=2.0
echo Smoothing=0.8
echo.
echo [BoneOffsets]
echo HeadOffset=-15
echo ChestOffset=10
echo AutoOffset=-5
) > "Configs\aimbot_config.ini"

:: Create comprehensive README
echo [+] Creating setup instructions...
(
echo MW3/MWZ ULTIMATE MOD SUITE - SETUP INSTRUCTIONS
echo ================================================
echo.
echo INSTALLATION COMPLETE!
echo.
echo WHAT WAS INSTALLED:
echo - AutoHotkey v1.1.37.02 ^(Required for .ahk scripts^)
echo - Visual C++ Redistributables ^(Required for .exe tools^)
echo - All mod files organized in folders
echo - Configuration files
echo - Launcher script
echo.
echo HOW TO USE:
echo.
echo 1. CONTROLLER MODS:
echo    - Run "MW3_Ultimate_Launcher.bat"
echo    - Choose option 1 or 2
echo    - Press F1-F8 for different features
echo    - F9 for emergency disable
echo.
echo 2. AIMBOT SYSTEM:
echo    - Run launcher, choose option 3 or 4
echo    - Set enemy nameplates to RED in game
echo    - Press F1 to toggle aimbot
echo    - Adjust settings in GUI
echo.
echo 3. MEMORY TOOLS:
echo    - Need to compile .cpp files first
echo    - Use Visual Studio or MinGW
echo    - Run as Administrator
echo    - Press number keys for features
echo.
echo SAFETY TIPS:
echo - Start with conservative settings
echo - Test in single-player first
echo - Use F9 emergency disable
echo - Don't use all features at once
echo.
echo HOTKEYS SUMMARY:
echo Controller: F1=RapidFire F2=SuperJump F3=AntiRecoil F4=Auto F5=Movement
echo Aimbot: F1=Toggle F2=Mode F3=Bone F4=Prediction F5=AutoShoot
echo Memory: 1=God 2=Ammo 3=Jump 4=Speed 5=Weapons 6=NoFall 7=NoExplosion
echo.
echo TROUBLESHOOTING:
echo - If scripts won't run: Install AutoHotkey v1.1.37.02
echo - If controller not detected: Check Windows Device Manager
echo - If memory tool crashes: Run as Administrator
echo - If aimbot not working: Set enemy nameplates to RED
echo.
echo DETECTION RISKS:
echo - Controller mods: LOW risk ^(external input^)
echo - Aimbot: LOW-MEDIUM risk ^(external reading^)
echo - Memory mods: MEDIUM-HIGH risk ^(memory writing^)
echo.
echo Use at your own risk! The authors are not responsible for bans.
echo.
echo For support, check the source forum threads or documentation.
) > "README.txt"

:: Create uninstaller
echo [+] Creating uninstaller...
(
echo @echo off
echo title MW3/MWZ Ultimate Mod Suite Uninstaller
echo.
echo This will remove all MW3 Ultimate Mod files.
echo.
set /p confirm="Are you sure? (Y/N): "
if /i "%%confirm%%"=="Y" (
    echo [+] Removing mod files...
    cd /d "%%USERPROFILE%%\Documents"
    rmdir /s /q "MW3_Ultimate_Mods"
    echo [+] Uninstallation complete
) else (
    echo [-] Uninstallation cancelled
)
pause
) > "Uninstall.bat"

:: Set up Windows Defender exclusions (optional)
echo.
echo [+] Setting up Windows Defender exclusions...
echo [!] This helps prevent false positive detections
set /p defender="Add Windows Defender exclusions? (Y/N): "
if /i "%defender%"=="Y" (
    powershell -Command "Add-MpPreference -ExclusionPath '%INSTALL_DIR%'"
    powershell -Command "Add-MpPreference -ExclusionExtension '.ahk'"
    echo [+] Windows Defender exclusions added
) else (
    echo [-] Skipping Windows Defender exclusions
)

:: Create desktop shortcuts
echo.
echo [+] Creating desktop shortcuts...
set /p shortcuts="Create desktop shortcuts? (Y/N): "
if /i "%shortcuts%"=="Y" (
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\MW3 Ultimate Mods.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\MW3_Ultimate_Launcher.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"
    echo [+] Desktop shortcut created
)

:: Final setup
echo.
echo ========================================
echo   INSTALLATION COMPLETE!
echo ========================================
echo.
echo Installation Directory: %INSTALL_DIR%
echo.
echo NEXT STEPS:
echo 1. Read the README.txt file for detailed instructions
echo 2. Run MW3_Ultimate_Launcher.bat to start using mods
echo 3. Test in single-player mode first
echo 4. Configure settings in the Configs folder
echo.
echo IMPORTANT:
echo - Always run as Administrator for memory tools
echo - Use F9 for emergency disable in all tools
echo - Start with conservative settings
echo - Test offline before going online
echo.
echo The launcher will open automatically in 10 seconds...
echo Press any key to open it now, or wait.
echo.

timeout /t 10 /nobreak >nul 2>&1
if not errorlevel 1 goto launch

:launch
start "" "MW3_Ultimate_Launcher.bat"
start "" "README.txt"

echo.
echo [+] Setup complete! Enjoy your MW3/MWZ mods!
pause
