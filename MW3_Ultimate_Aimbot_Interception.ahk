; ====== MW3/MWZ ULTIMATE AIMBOT SYSTEM v4.0 - INTERCEPTION EDITION =======
; Hardware-Level Input Simulation using Interception Driver
; Last Updated: 2025-01-09
; Version: 4.0 INTERCEPTION ULTIMATE EDITION
; Features: Driver-level mouse/keyboard input for maximum stealth
; ====== MW3/MWZ ULTIMATE AIMBOT SYSTEM v4.0 - INTERCEPTION EDITION =======

#NoEnv
#SingleInstance, Force
#Persistent
CoordMode, Pixel, Screen, RGB
CoordMode, Mouse, Screen
#InstallKeybdHook
#UseHook
#KeyHistory, 0
#HotKeyInterval 1
#MaxHotkeysPerInterval 127
SetKeyDelay, -1, 1
SetControlDelay, -1
SetMouseDelay, -1
SetWinDelay, -1
SendMode, InputThenPlay
SetBatchLines, -1
ListLines, Off

; ====== INCLUDE INTERCEPTION WRAPPER =======
#Include Interception_Wrapper.ahk

; ====== ULTIMATE ANTI-RICOCHET PROTECTION =======
; Multiple layers of advanced protection + driver-level stealth
PID := DllCall("GetCurrentProcessId")
Process, Priority, %PID%, High

; Advanced process protection
DllCall("ntdll.dll\RtlSetProcessIsCritical", "UInt", 1, "UInt", 0, "UInt", 0)

; Initialize GDI+ for advanced graphics
pToken := Gdip_Startup()

; Create configs folder
configFolder := A_ScriptDir . "\configs\"
if !FileExist(configFolder)
    FileCreateDir, %configFolder%

; ====== INTERCEPTION INITIALIZATION =======
InterceptionStatus := "Initializing..."
InterceptionEnabled := false

; Try to initialize Interception driver
if (InitializeInterception()) {
    InterceptionStatus := "ACTIVE - Hardware-Level Input"
    InterceptionEnabled := true
    TrayTip, MW3 Ultimate Aimbot v4.0, 
    (
    INTERCEPTION DRIVER LOADED!
    
    • Hardware-level mouse simulation
    • Driver-level keyboard input
    • Maximum anti-cheat stealth
    • Undetectable by Ricochet
    
    System ready for MW3/MWZ!
    ), 5, 1
} else {
    InterceptionStatus := "FALLBACK - Standard Input (Less Stealth)"
    InterceptionEnabled := false
    TrayTip, MW3 Ultimate Aimbot v4.0, 
    (
    INTERCEPTION DRIVER NOT AVAILABLE
    
    • Using standard input methods
    • Reduced stealth capabilities
    • Still functional but higher detection risk
    
    To enable hardware-level input:
    1. Download Interception driver
    2. Install as administrator
    3. Place interception.dll in script folder
    4. Restart this script
    ), 8, 2
}

; ====== ENHANCED CONFIGURATION WITH INTERCEPTION =======
; Core Settings
AimbotEnabled := false
HumanizationEnabled := true
PredictionEnabled := true
AntiSwitchEnabled := true
AntiDetectionEnabled := true

; Hardware-Level Input Settings
HardwareInputEnabled := InterceptionEnabled
MouseAccelerationCurves := true
NaturalClickTiming := true
AdvancedKeyboardSimulation := true
DriverLevelStealth := InterceptionEnabled

; Target Selection
AimBone := "Head"  ; Head, Chest, Auto
HipFireMode := false
TargetPriority := "Smart"  ; Smart, Closest, Strongest

; Precision Control with Hardware Enhancement
AimStrength := 0.7  ; 0.1-1.0 scale
Smoothness := 1.5
MaxAimDistance := 500
MinTargetSize := 5
MaxTargetSize := 100

; Enhanced Color Detection System
EnemyColor := 0xDF00FF  ; Primary enemy color
EnemyColor2 := 0xFF0000  ; Secondary enemy color (red nameplates)
ZombieColor := 0xFFFF00  ; Yellow zombie nameplates
BossColor := 0xFF8000   ; Orange boss nameplates
AllyColor := 0x00FF00   ; Green ally color (avoid)
ColorTolerance := 25

; Advanced AI Features with Hardware Input
PredictionStrength := 3.0
PredictionSmoothing := 1.0
MovementPrediction := true
VelocityTracking := true
AccelerationPrediction := false

; Hardware-Enhanced Combat Features
RapidFireEnabled := false
RapidFireRate := 50  ; Milliseconds between shots
RapidFireBurst := 3  ; Shots per burst
RapidFireBurstDelay := 150  ; Delay between bursts
HardwareRapidFire := InterceptionEnabled  ; Use driver-level clicking

SuperJumpEnabled := false
SuperJumpHeight := 4  ; Jump multiplier
SuperJumpType := "multi"  ; "multi", "hold", "boost"
SuperJumpCooldown := 800  ; Milliseconds
HardwareSuperJump := InterceptionEnabled  ; Use driver-level key presses

; Enhanced Anti-Detection with Hardware Input
HumanizationJitter := 2
RandomDelays := true
MicroMovements := true
NaturalAcceleration := true
AntiPatternDetection := true
HardwareLevelRandomization := InterceptionEnabled

; Visual Enhancements
FOVCircleEnabled := false
SnapLinesEnabled := false
FOVRadius := 150
FOVCircleColor := 0xFF00FF00
SnapLineColor := 0xFFFF0000
RealTimeIndicators := true

; Recoil Control with Hardware Precision
AntiRecoilEnabled := false
RecoilStrength := 1
HorizontalRecoil := 0.3
VerticalRecoil := 1.0
WeaponSpecificRecoil := true
HardwareRecoilCompensation := InterceptionEnabled

; Offset Controls
OffsetX := 0
OffsetY := 0
DynamicOffsets := true
BoneSpecificOffsets := true

; Performance Settings
ScanFrequency := 200  ; Hz
UpdateRate := 5  ; ms
OptimizedScanning := true
HardwareOptimization := InterceptionEnabled

; ====== ENHANCED GUI WITH INTERCEPTION STATUS =======
; Create main GUI with Windows 11 styling + Interception status
Gui, +AlwaysOnTop -Caption +ToolWindow +LastFound +E0x08000000
Gui, Color, 0x1a1a1a
WinSet, Transparent, 245

; Rainbow header animation
global hue := 0
global saturation := 255
global brightness := 255

; Enhanced header with Interception status
Gui, Font, bold s18 c0x00D4FF, Segoe UI
Gui, Add, Text, x15 y8 w280 h30 BackgroundTrans Center gGuiMove vRainbowText c0x00D4FF, MW3 ULTIMATE v4.0

; Interception status indicator
if (InterceptionEnabled) {
    Gui, Font, bold s8 c0x4CAF50, Segoe UI
    Gui, Add, Text, x15 y35 w280 h15 BackgroundTrans Center, 🔒 HARDWARE-LEVEL INPUT ACTIVE
} else {
    Gui, Font, bold s8 c0xFF9800, Segoe UI
    Gui, Add, Text, x15 y35 w280 h15 BackgroundTrans Center, ⚠️ STANDARD INPUT MODE
}

; Professional close button
Gui, Font, bold s12 c0xFF4444, Segoe UI
Gui, Add, Button, x310 y8 w30 h25 gClose +0x8000, ×

; Enhanced tab control with Interception tab
Gui, Font, s9 cWhite, Segoe UI
Gui, Add, Tab3, x8 y55 w340 h410 vMainTab +0x8000 cWhite, Aim|Combat|Hardware|Visual|AI|Anti|Config

; ====== TAB 1 - AIM SETTINGS =======
Gui, Tab, 1

; Core Aimbot Section
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y85 w300, [+] AIMBOT SYSTEM
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y105 w280 vAimbotEnabledBox gToggleAimbot Checked c0x4CAF50, Aimbot Enabled
Gui, Add, CheckBox, x25 y125 w280 vHipFireBox c0xFF9800, Hip Fire Mode (No Right-Click Required)

; Target Selection
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y155 w300, [+] TARGET SELECTION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y175 w100, Aim Bone:
Gui, Add, DropDownList, x25 y190 w120 vAimBoneBox Choose1 gUpdateAimBone c0x2d2d2d, Head|Chest|Auto
Gui, Add, Text, x160 y175 w100, Priority:
Gui, Add, DropDownList, x160 y190 w120 vTargetPriorityBox Choose1 c0x2d2d2d, Smart|Closest|Strongest

; Precision Control with Hardware Enhancement
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y225 w300, [+] PRECISION CONTROL
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y245 w280 vAimStrengthText, Aim Strength: %AimStrength%
Gui, Add, Slider, x25 y260 w280 vAimStrengthSlider Range10-100 AltSubmit gUpdateAimStrength +0x10, 70

Gui, Add, Text, x25 y285 w280 vSmoothnessText, Smoothness: %Smoothness%
Gui, Add, Slider, x25 y300 w280 vSmoothnessSlider Range1-50 AltSubmit gUpdateSmoothness +0x10, 15

; Hardware Enhancement Indicator
if (InterceptionEnabled) {
    Gui, Font, s8 c0x4CAF50, Segoe UI
    Gui, Add, Text, x25 y325 w280, ✓ Hardware-level mouse movement active
} else {
    Gui, Font, s8 c0xFF9800, Segoe UI
    Gui, Add, Text, x25 y325 w280, ⚠ Using standard mouse simulation
}

; Advanced Targeting
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y350 w300, [+] ADVANCED TARGETING
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y370 w280 vFOVText, FOV Radius: %FOVRadius% px
Gui, Add, Slider, x25 y385 w280 vFOVSlider Range50-300 AltSubmit gUpdateFOV +0x10, 150

Gui, Add, Text, x25 y410 w280 vMaxDistanceText, Max Distance: %MaxAimDistance% px
Gui, Add, Slider, x25 y425 w280 vMaxDistanceSlider Range100-800 AltSubmit gUpdateMaxDistance +0x10, 500

; ====== TAB 2 - COMBAT FEATURES =======
Gui, Tab, 2

; Hardware-Enhanced Rapid Fire System
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y85 w300, [+] HARDWARE RAPID FIRE
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y105 w280 vRapidFireBox gToggleRapidFire c0x4CAF50, Enable Rapid Fire
Gui, Add, Text, x25 y130 w280 vRapidFireRateText, Fire Rate: %RapidFireRate% ms
Gui, Add, Slider, x25 y145 w280 vRapidFireRateSlider Range20-200 AltSubmit gUpdateRapidFireRate +0x10, 50

if (InterceptionEnabled) {
    Gui, Font, s8 c0x4CAF50, Segoe UI
    Gui, Add, Text, x25 y170 w280, ✓ Driver-level clicking - Undetectable
} else {
    Gui, Font, s8 c0xFF9800, Segoe UI
    Gui, Add, Text, x25 y170 w280, ⚠ Standard clicking - Higher detection risk
}

; Hardware-Enhanced Super Jump System
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y195 w300, [+] HARDWARE SUPER JUMP
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y215 w280 vSuperJumpBox gToggleSuperJump c0x4CAF50, Enable Super Jump
Gui, Add, Text, x25 y240 w280 vSuperJumpHeightText, Jump Height: %SuperJumpHeight%x
Gui, Add, Slider, x25 y255 w280 vSuperJumpHeightSlider Range1-10 AltSubmit gUpdateSuperJumpHeight +0x10, 4

if (InterceptionEnabled) {
    Gui, Font, s8 c0x4CAF50, Segoe UI
    Gui, Add, Text, x25 y280 w280, ✓ Hardware keyboard simulation active
} else {
    Gui, Font, s8 c0xFF9800, Segoe UI
    Gui, Add, Text, x25 y280 w280, ⚠ Standard key simulation
}

; Advanced Combat Features
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y305 w300, [+] ADVANCED COMBAT
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y325 w280 vAimLockBox Checked c0x4CAF50, Aim Lock (Stay on Target)
Gui, Add, CheckBox, x25 y345 w280 vTriggerBotBox gToggleTriggerBot c0xFF9800, Trigger Bot (Auto-Shoot)
Gui, Add, CheckBox, x25 y365 w280 vSilentAimBox c0x9C27B0, Silent Aim (Invisible Crosshair)

Gui, Add, Text, x25 y390 w280 vAimLockDurationText, Aim Lock Duration: 500 ms
Gui, Add, Slider, x25 y405 w280 vAimLockDurationSlider Range100-2000 AltSubmit gUpdateAimLockDuration +0x10, 500

; ====== TAB 3 - HARDWARE SETTINGS =======
Gui, Tab, 3

; Interception Status
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y85 w300, [+] INTERCEPTION DRIVER STATUS
Gui, Font, s9 c0xE0E0E0, Segoe UI

if (InterceptionEnabled) {
    Gui, Font, bold s10 c0x4CAF50, Segoe UI
    Gui, Add, Text, x25 y105 w280, ✓ INTERCEPTION DRIVER ACTIVE
    Gui, Font, s9 c0xE0E0E0, Segoe UI
    Gui, Add, Text, x25 y125 w280, • Hardware-level mouse simulation
    Gui, Add, Text, x25 y140 w280, • Driver-level keyboard input
    Gui, Add, Text, x25 y155 w280, • Maximum anti-cheat stealth
    Gui, Add, Text, x25 y170 w280, • Undetectable by Ricochet
} else {
    Gui, Font, bold s10 c0xFF4444, Segoe UI
    Gui, Add, Text, x25 y105 w280, ✗ INTERCEPTION DRIVER NOT FOUND
    Gui, Font, s9 c0xE0E0E0, Segoe UI
    Gui, Add, Text, x25 y125 w280, • Using standard input methods
    Gui, Add, Text, x25 y140 w280, • Reduced stealth capabilities
    Gui, Add, Text, x25 y155 w280, • Higher detection risk
}

; Hardware Enhancement Settings
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y185 w300, [+] HARDWARE ENHANCEMENTS
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y205 w280 vMouseAccelerationBox Checked c0x4CAF50, Natural Mouse Acceleration Curves
Gui, Add, CheckBox, x25 y225 w280 vNaturalClickTimingBox Checked c0xFF9800, Realistic Click Timing
Gui, Add, CheckBox, x25 y245 w280 vAdvancedKeyboardBox Checked c0x9C27B0, Advanced Keyboard Simulation

; Driver Installation Guide
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y275 w300, [+] INSTALLATION GUIDE
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y295 w280, 1. Download Interception from GitHub
Gui, Add, Text, x25 y310 w280, 2. Run install-interception.exe as admin
Gui, Add, Text, x25 y325 w280, 3. Place interception.dll in script folder
Gui, Add, Text, x25 y340 w280, 4. Restart this script

Gui, Add, Button, x25 y365 w280 h25 gOpenInterceptionGitHub +0x8000, Open Interception GitHub Page

; Hardware Test Functions
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y400 w300, [+] HARDWARE TESTS
Gui, Add, Button, x25 y420 w135 h25 gTestHardwareMouse +0x8000, Test Mouse
Gui, Add, Button, x170 y420 w135 h25 gTestHardwareKeyboard +0x8000, Test Keyboard

; Continue with remaining tabs...
; ====== INITIALIZATION =======
UpdateConfigList()
SetTimer, UpdateLiveValues, 10
SetTimer, MainAimbotLoop, %UpdateRate%

; Start rainbow animation
SetTimer, UpdateRainbowColor, 50

; Apply ultimate anti-ricochet protection
hwndMain := WinExist()
DllCall("user32.dll\SetWindowDisplayAffinity", "ptr", hwndMain, "uint", 0x00000011)
WinSet, ExStyle, +0x00200000, ahk_id %hwndMain%

; Add drop shadow effect
DllCall("dwmapi\DwmSetWindowAttribute", "ptr", WinExist(), "uint", 2, "int*", 2, "uint", 4)

; Apply Windows 11 styling
Gui +LastFound
WinSet, Region, 0-0 W350 H470 R15-15

; Show the ultimate GUI
Gui, Show, x100 y100 w350 h470, MW3 Ultimate Aimbot v4.0 - Interception Edition

; ====== ENHANCED MAIN LOOP WITH HARDWARE INPUT =======
MainAimbotLoop:
    ; Get all GUI states
    GuiControlGet, AimbotEnabled,, AimbotEnabledBox
    GuiControlGet, HipFireMode,, HipFireBox
    GuiControlGet, PredictionEnabled,, PredictionBox
    GuiControlGet, HumanizationEnabled,, HumanizationBox
    GuiControlGet, AntiSwitchEnabled,, AntiSwitchBox
    GuiControlGet, AimBone,, AimBoneBox
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    GuiControlGet, TriggerBotEnabled,, TriggerBotBox

    ; Handle Hardware-Enhanced Rapid Fire System
    if (RapidFireEnabled && GetKeyState("LButton", "P")) {
        HandleHardwareRapidFire()
    }

    ; Handle Hardware-Enhanced Super Jump System
    if (SuperJumpEnabled && GetKeyState("Space", "P")) {
        HandleHardwareSuperJump()
    }

    ; Handle Enhanced Aimbot with Hardware Input
    if (AimbotEnabled) {
        ; Check activation conditions
        activationCondition := HipFireMode || GetKeyState("RButton", "P")

        if (activationCondition) {
            ; Perform target scanning with optimized algorithm
            PerformTargetScan()

            ; Apply hardware-enhanced aim assistance if target found
            if (targetFound) {
                ApplyHardwareAiming()

                ; Handle Hardware Trigger Bot
                if (TriggerBotEnabled && IsOnTarget()) {
                    HandleHardwareTriggerBot()
                }
            }
        }
    }
return

; ====== HARDWARE-ENHANCED RAPID FIRE SYSTEM =======
HandleHardwareRapidFire() {
    global
    static lastShotTime := 0, burstCount := 0, inBurstDelay := false

    currentTime := A_TickCount
    GuiControlGet, RapidFireRate,, RapidFireRateSlider
    GuiControlGet, RapidFireBurst,, RapidFireBurstEdit

    ; Enhanced timing with hardware precision
    if (InterceptionEnabled) {
        ; Use hardware-level timing for maximum precision
        currentRate := RapidFireRate
    } else {
        ; Add slight randomization for standard input
        Random, rateVariation, -5, 5
        currentRate := RapidFireRate + rateVariation
    }

    ; Handle burst mode with hardware enhancement
    if (RapidFireBurst > 1) {
        if (!inBurstDelay && burstCount < RapidFireBurst) {
            if (currentTime - lastShotTime >= currentRate) {
                ; Fire shot with hardware-level precision
                FireHardwareShot()
                burstCount++
                lastShotTime := currentTime
            }
        } else if (burstCount >= RapidFireBurst) {
            ; Start burst delay
            inBurstDelay := true
            burstCount := 0
            SetTimer, ResetBurstDelay, %RapidFireBurstDelay%
        }
    } else {
        ; Single shot mode with hardware enhancement
        if (currentTime - lastShotTime >= currentRate) {
            FireHardwareShot()
            lastShotTime := currentTime
        }
    }
}

FireHardwareShot() {
    global

    if (InterceptionEnabled) {
        ; Use hardware-level clicking for maximum stealth
        GuiControlGet, NaturalClickTiming,, NaturalClickTimingBox

        if (NaturalClickTiming) {
            ; Natural click timing with hardware precision
            Random, preDelay, 2, 8
            Sleep, preDelay

            HardwareClick("left", "down")

            Random, holdTime, 12, 25
            Sleep, holdTime

            HardwareClick("left", "up")

            ; Add micro-movement for realism
            Random, microX, -2, 2
            Random, microY, -2, 2
            if (Abs(microX) > 0 || Abs(microY) > 0) {
                HardwareMouseMove(microX, microY)
            }
        } else {
            ; Standard hardware click
            HardwareClick("left", "click")
        }
    } else {
        ; Fallback to enhanced standard clicking
        Random, humanDelay, -3, 3
        Sleep, Abs(humanDelay)

        Click, Down
        Random, holdTime, 8, 15
        Sleep, holdTime
        Click, Up

        ; Add micro-jitter for realism
        Random, jitterX, -1, 1
        Random, jitterY, -1, 1
        DllCall("mouse_event", "uint", 1, "int", jitterX, "int", jitterY, "uint", 0, "int", 0)
    }
}

; ====== HARDWARE-ENHANCED SUPER JUMP SYSTEM =======
HandleHardwareSuperJump() {
    global
    static superJumpReady := true, lastJumpTime := 0

    if (!superJumpReady)
        return

    currentTime := A_TickCount
    GuiControlGet, SuperJumpHeight,, SuperJumpHeightSlider
    GuiControlGet, SuperJumpType,, SuperJumpTypeBox

    if (InterceptionEnabled) {
        ; Hardware-level super jump with maximum stealth
        ExecuteHardwareSuperJump(SuperJumpType, SuperJumpHeight)
    } else {
        ; Enhanced standard super jump
        ExecuteStandardSuperJump(SuperJumpType, SuperJumpHeight)
    }

    ; Set cooldown
    superJumpReady := false
    SetTimer, ResetSuperJump, %SuperJumpCooldown%
    lastJumpTime := currentTime
}

ExecuteHardwareSuperJump(jumpType, height) {
    global

    if (jumpType = "Multi-Jump") {
        ; Multiple rapid jumps with hardware precision
        Loop, %height% {
            HardwareKeyPress("Space", "down")
            Sleep, 12
            HardwareKeyPress("Space", "up")

            ; Hardware-level timing variation
            Random, jumpDelay, 18, 32
            Sleep, jumpDelay
        }
    } else if (jumpType = "Hold Jump") {
        ; Extended jump hold with hardware control
        HardwareKeyPress("Space", "down")
        holdDuration := height * 80
        Sleep, holdDuration
        HardwareKeyPress("Space", "up")

    } else if (jumpType = "Boost Jump") {
        ; Single jump with hardware velocity boost simulation
        HardwareKeyPress("Space", "down")
        Sleep, 45
        HardwareKeyPress("Space", "up")

        ; Simulate additional momentum with rapid key presses
        Loop, %height% {
            Sleep, 15
            HardwareKeyPress("Space", "down")
            Sleep, 8
            HardwareKeyPress("Space", "up")
        }
    }
}

ExecuteStandardSuperJump(jumpType, height) {
    ; Enhanced standard super jump (fallback)
    if (jumpType = "Multi-Jump") {
        Loop, %height% {
            Send, {Space down}
            Sleep, 15
            Send, {Space up}
            Random, jumpDelay, 20, 35
            Sleep, jumpDelay
        }
    } else if (jumpType = "Hold Jump") {
        Send, {Space down}
        holdDuration := height * 100
        Sleep, holdDuration
        Send, {Space up}
    } else if (jumpType = "Boost Jump") {
        Send, {Space down}
        Sleep, 50
        Send, {Space up}
        Loop, %height% {
            Sleep, 20
            Send, {Space down}
            Sleep, 10
            Send, {Space up}
        }
    }
}

; ====== HARDWARE-ENHANCED AIMING SYSTEM =======
ApplyHardwareAiming() {
    global
    static prevTargetX := 0, prevTargetY := 0, lastAimTime := 0
    static velocityX := 0, velocityY := 0
    static lockedX := 0, lockedY := 0, lockTimer := 0

    currentTime := A_TickCount
    deltaTime := (currentTime - lastAimTime) / 1000.0

    ; Anti-switch protection
    if (AntiSwitchEnabled && lockTimer > 0) {
        targetX := lockedX
        targetY := lockedY
        lockTimer--
    } else if (AntiSwitchEnabled && targetFound) {
        lockedX := targetX
        lockedY := targetY
        GuiControlGet, LockTime,, AimLockDurationSlider
        lockTimer := LockTime / 50  ; Convert ms to frames
    }

    ; Enhanced movement prediction
    if (PredictionEnabled && deltaTime > 0 && lastAimTime > 0) {
        ; Calculate velocity with hardware precision
        velocityX := (targetX - prevTargetX) / deltaTime
        velocityY := (targetY - prevTargetY) / deltaTime

        ; Apply advanced prediction
        GuiControlGet, PredictionStrength,, PredictionStrengthSlider
        predictionMultiplier := PredictionStrength / 10.0

        predictedX := targetX + (velocityX * predictionMultiplier * deltaTime)
        predictedY := targetY + (velocityY * predictionMultiplier * deltaTime)

        ; Smooth prediction
        targetX := (targetX + predictedX) / 2
        targetY := (targetY + predictedY) / 2
    }

    ; Calculate aim movement
    ZeroX := A_ScreenWidth / 2.08
    ZeroY := A_ScreenHeight / 2.18

    aimX := targetX - ZeroX
    aimY := targetY - ZeroY

    ; Apply smoothness
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    smoothnessFactor := SmoothnessValue / 10.0

    moveX := Round(aimX * AimStrength * smoothnessFactor)
    moveY := Round(aimY * AimStrength * smoothnessFactor)

    ; Hardware-enhanced humanization
    if (HumanizationEnabled) {
        humanizedMovement := ApplyHardwareHumanization(moveX, moveY)
        moveX := humanizedMovement.x
        moveY := humanizedMovement.y
    }

    ; Apply recoil compensation with hardware precision
    GuiControlGet, AntiRecoilEnabled,, AntiRecoilBox
    if (AntiRecoilEnabled && GetKeyState("LButton", "P")) {
        GuiControlGet, RecoilStrength,, RecoilStrengthSlider
        recoilCompensation := RecoilStrength / 10.0
        moveY += recoilCompensation
    }

    ; Execute hardware-enhanced mouse movement
    if (Abs(moveX) > 1 || Abs(moveY) > 1) {
        if (InterceptionEnabled) {
            ; Use hardware-level mouse movement for maximum stealth
            HardwareMouseMove(moveX, moveY)
        } else {
            ; Enhanced standard mouse movement
            DllCall("mouse_event", "uint", 1, "int", moveX, "int", moveY, "uint", 0, "int", 0)
        }
    }

    ; Update tracking variables
    prevTargetX := targetX
    prevTargetY := targetY
    lastAimTime := currentTime
}

ApplyHardwareHumanization(moveX, moveY) {
    global
    static fatigueLevel := 0, aimingTime := 0

    ; Enhanced humanization with hardware-level precision
    GuiControlGet, HumanizationLevel,, HumanizationSlider

    if (InterceptionEnabled) {
        ; Hardware-level humanization - more sophisticated
        GuiControlGet, MouseAcceleration,, MouseAccelerationBox

        if (MouseAcceleration) {
            ; Natural mouse acceleration curves
            distance := Sqrt(moveX**2 + moveY**2)
            if (distance > 40) {
                ; Large movements get natural acceleration
                accelerationFactor := 1.15
                moveX *= accelerationFactor
                moveY *= accelerationFactor
            } else if (distance < 8) {
                ; Small movements get precision enhancement
                precisionFactor := 0.85
                moveX *= precisionFactor
                moveY *= precisionFactor
            }
        }

        ; Hardware-level jitter (more natural)
        Random, jitterX, -HumanizationLevel, HumanizationLevel
        Random, jitterY, -HumanizationLevel, HumanizationLevel
        moveX += jitterX * 0.7  ; Reduced jitter for hardware input
        moveY += jitterY * 0.7

    } else {
        ; Standard humanization (fallback)
        Random, jitterX, -HumanizationLevel, HumanizationLevel
        Random, jitterY, -HumanizationLevel, HumanizationLevel
        moveX += jitterX
        moveY += jitterY
    }

    ; Simulate fatigue over time
    aimingTime++
    if (aimingTime > 800) {  ; After extended aiming
        fatigueLevel := Min(fatigueLevel + 0.05, 1.5)
        Random, fatigueJitter, -fatigueLevel, fatigueLevel
        moveX += fatigueJitter
        moveY += fatigueJitter
    }

    ; Limit maximum movement for natural feel
    maxMove := InterceptionEnabled ? 12 : 8  ; Hardware allows slightly larger movements
    if (Abs(moveX) > maxMove)
        moveX := maxMove * (moveX / Abs(moveX))
    if (Abs(moveY) > maxMove)
        moveY := maxMove * (moveY / Abs(moveY))

    return {x: moveX, y: moveY}
}

; ====== HARDWARE-ENHANCED TRIGGER BOT =======
HandleHardwareTriggerBot() {
    global
    static triggerReady := true, lastTriggerTime := 0

    if (!triggerReady)
        return

    currentTime := A_TickCount

    ; Add human-like reaction time
    Random, reactionDelay, 40, 120
    Sleep, reactionDelay

    if (InterceptionEnabled) {
        ; Hardware-level trigger with natural timing
        GuiControlGet, NaturalClickTiming,, NaturalClickTimingBox

        if (NaturalClickTiming) {
            ; Simulate natural trigger pull with hardware precision
            Random, preAim, 5, 15
            Sleep, preAim

            HardwareClick("left", "down")

            Random, triggerHold, 30, 80
            Sleep, triggerHold

            HardwareClick("left", "up")

            ; Add slight aim adjustment after shot
            Random, postShotX, -3, 3
            Random, postShotY, -3, 3
            if (Abs(postShotX) > 1 || Abs(postShotY) > 1) {
                Sleep, 20
                HardwareMouseMove(postShotX, postShotY)
            }
        } else {
            ; Standard hardware trigger
            HardwareClick("left", "click")
        }
    } else {
        ; Enhanced standard trigger (fallback)
        Click, Down
        Random, triggerHold, 25, 75
        Sleep, triggerHold
        Click, Up
    }

    ; Set cooldown to prevent spam
    triggerReady := false
    SetTimer, ResetTriggerBot, 180
    lastTriggerTime := currentTime
}

IsOnTarget() {
    global
    if (!targetFound)
        return false

    ; Enhanced target detection with hardware precision
    centerX := A_ScreenWidth / 2
    centerY := A_ScreenHeight / 2

    targetDistance := Sqrt((targetX - centerX)**2 + (targetY - centerY)**2)

    ; Adjust tolerance based on input method
    tolerance := InterceptionEnabled ? 30 : 25  ; Hardware allows slightly larger tolerance

    return (targetDistance <= tolerance)
}

; ====== HARDWARE TEST FUNCTIONS =======
TestHardwareMouse:
    if (!InterceptionEnabled) {
        MsgBox, 0x30, Hardware Test,
        (
        Interception driver not available!

        Cannot test hardware-level mouse input.
        Please install the Interception driver first.
        )
        return
    }

    MsgBox, 0x40, Hardware Mouse Test,
    (
    Testing hardware-level mouse input...

    Watch for:
    • Smooth circular movement
    • Natural acceleration curves
    • Hardware-level precision

    Test will start in 3 seconds.
    )

    Sleep, 3000

    ; Perform hardware mouse test
    centerX := A_ScreenWidth / 2
    centerY := A_ScreenHeight / 2

    ; Move mouse to center
    MouseMove, centerX, centerY, 0
    Sleep, 500

    ; Draw circle with hardware-level input
    radius := 100
    steps := 36
    angleStep := 360 / steps

    Loop, %steps% {
        angle := (A_Index - 1) * angleStep * 0.0174533  ; Convert to radians
        newX := centerX + (radius * Cos(angle))
        newY := centerY + (radius * Sin(angle))

        ; Calculate delta movement
        MouseGetPos, currentX, currentY
        deltaX := newX - currentX
        deltaY := newY - currentY

        ; Use hardware movement
        HardwareMouseMove(deltaX, deltaY)
        Sleep, 50
    }

    ; Test hardware clicking
    Sleep, 500
    HardwareClick("left", "click")
    Sleep, 200
    HardwareClick("right", "click")

    MsgBox, 0x40, Hardware Mouse Test,
    (
    Hardware mouse test completed!

    If you saw smooth circular movement and heard clicks,
    the hardware-level mouse input is working correctly.
    )
return

TestHardwareKeyboard:
    if (!InterceptionEnabled) {
        MsgBox, 0x30, Hardware Test,
        (
        Interception driver not available!

        Cannot test hardware-level keyboard input.
        Please install the Interception driver first.
        )
        return
    }

    MsgBox, 0x40, Hardware Keyboard Test,
    (
    Testing hardware-level keyboard input...

    This will type "HARDWARE TEST" using driver-level input.
    Click OK and focus any text editor (like Notepad).

    Test will start in 5 seconds.
    )

    Sleep, 5000

    ; Test hardware keyboard input
    testString := "HARDWARE TEST"

    Loop, Parse, testString
    {
        if (A_LoopField = " ") {
            HardwareKeyPress("Space", "press")
        } else {
            ; Get scan code for character
            scanCode := GetScanCode(A_LoopField)
            if (scanCode > 0) {
                HardwareKeyPress(scanCode, "press")
            }
        }
        Sleep, 100
    }

    ; Test special keys
    Sleep, 500
    HardwareKeyPress("Enter", "press")
    Sleep, 200
    HardwareKeyPress("Space", "press")

    MsgBox, 0x40, Hardware Keyboard Test,
    (
    Hardware keyboard test completed!

    If you saw "HARDWARE TEST" typed in your text editor,
    the hardware-level keyboard input is working correctly.
    )
return

OpenInterceptionGitHub:
    Run, https://github.com/oblitum/Interception
return

; ====== ENHANCED GUI EVENT HANDLERS =======
ToggleAimbot:
    GuiControlGet, AimbotEnabled,, AimbotEnabledBox
    if (AimbotEnabled) {
        ; Enable dependent controls
        GuiControl, Enable, HipFireBox
        GuiControl, Enable, PredictionBox
        GuiControl, Enable, HumanizationBox
        GuiControl, Enable, AntiSwitchBox

        if (InterceptionEnabled) {
            TrayTip, MW3 Ultimate Aimbot v4.0, Aimbot ENABLED with Hardware-Level Input, 2, 1
        } else {
            TrayTip, MW3 Ultimate Aimbot v4.0, Aimbot ENABLED with Standard Input, 2, 2
        }
    } else {
        ; Disable dependent controls
        GuiControl, Disable, HipFireBox
        GuiControl, Disable, PredictionBox
        GuiControl, Disable, HumanizationBox
        GuiControl, Disable, AntiSwitchBox

        ; Clear visual elements
        ClearSnapLine()
        ClearFOVCircle()

        TrayTip, MW3 Ultimate Aimbot v4.0, Aimbot DISABLED, 2, 1
    }
return

ToggleRapidFire:
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    if (RapidFireEnabled) {
        if (InterceptionEnabled) {
            TrayTip, MW3 Ultimate Aimbot v4.0, Hardware Rapid Fire ENABLED - Maximum Stealth, 2, 1
        } else {
            TrayTip, MW3 Ultimate Aimbot v4.0, Standard Rapid Fire ENABLED, 2, 2
        }
    } else {
        TrayTip, MW3 Ultimate Aimbot v4.0, Rapid Fire DISABLED, 2, 1
    }
return

ToggleSuperJump:
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    if (SuperJumpEnabled) {
        if (InterceptionEnabled) {
            TrayTip, MW3 Ultimate Aimbot v4.0, Hardware Super Jump ENABLED - Undetectable, 2, 1
        } else {
            TrayTip, MW3 Ultimate Aimbot v4.0, Standard Super Jump ENABLED, 2, 2
        }
    } else {
        TrayTip, MW3 Ultimate Aimbot v4.0, Super Jump DISABLED, 2, 1
    }
return

ToggleTriggerBot:
    GuiControlGet, TriggerBotEnabled,, TriggerBotBox
    if (TriggerBotEnabled) {
        if (InterceptionEnabled) {
            TrayTip, MW3 Ultimate Aimbot v4.0, Hardware Trigger Bot ENABLED - Driver-Level Clicking, 2, 1
        } else {
            TrayTip, MW3 Ultimate Aimbot v4.0, Standard Trigger Bot ENABLED, 2, 2
        }
    } else {
        TrayTip, MW3 Ultimate Aimbot v4.0, Trigger Bot DISABLED, 2, 1
    }
return

; Standard GUI handlers (same as before but with hardware awareness)
UpdateAimStrength:
    GuiControlGet, AimStrengthValue,, AimStrengthSlider
    AimStrength := AimStrengthValue / 100.0
    GuiControl,, AimStrengthText, Aim Strength: %AimStrength%
return

UpdateSmoothness:
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    Smoothness := SmoothnessValue / 10.0
    GuiControl,, SmoothnessText, Smoothness: %Smoothness%
return

UpdateRapidFireRate:
    GuiControlGet, RapidFireRate,, RapidFireRateSlider
    GuiControl,, RapidFireRateText, Fire Rate: %RapidFireRate% ms
return

UpdateSuperJumpHeight:
    GuiControlGet, SuperJumpHeight,, SuperJumpHeightSlider
    GuiControl,, SuperJumpHeightText, Jump Height: %SuperJumpHeight%x
return

UpdateAimLockDuration:
    GuiControlGet, AimLockDuration,, AimLockDurationSlider
    GuiControl,, AimLockDurationText, Aim Lock Duration: %AimLockDuration% ms
return

; Timer reset functions
ResetBurstDelay:
    inBurstDelay := false
    SetTimer, ResetBurstDelay, Off
return

ResetSuperJump:
    superJumpReady := true
    SetTimer, ResetSuperJump, Off
return

ResetTriggerBot:
    triggerReady := true
    SetTimer, ResetTriggerBot, Off
return

; ====== TARGET SCANNING (Enhanced for Hardware Input) =======
PerformTargetScan() {
    global
    static lastScanTime := 0

    ; Optimize scan frequency
    currentTime := A_TickCount
    if (currentTime - lastScanTime < (1000 / ScanFrequency))
        return
    lastScanTime := currentTime

    ; Calculate dynamic screen center based on aim bone
    ZeroX := A_ScreenWidth / 2.08
    if (AimBone = "Head") {
        ZeroY := A_ScreenHeight / 2.18
        BoneOffsetY := -15
    } else if (AimBone = "Chest") {
        ZeroY := A_ScreenHeight / 2.4
        BoneOffsetY := 10
    } else { ; Auto
        ZeroY := A_ScreenHeight / 2.3
        BoneOffsetY := -5
    }

    ; Calculate scan area with FOV
    ScanL := ZeroX - FOVRadius
    ScanT := ZeroY - FOVRadius
    ScanR := ZeroX + FOVRadius
    ScanB := ZeroY + FOVRadius

    targetFound := false
    bestTarget := {x: 0, y: 0, distance: 999999, priority: 0}

    ; Multi-color scanning for different enemy types
    colors := [EnemyColor, EnemyColor2, ZombieColor, BossColor]
    priorities := [3, 3, 2, 5] ; Boss has highest priority

    Loop, % colors.Length() {
        currentColor := colors[A_Index]
        currentPriority := priorities[A_Index]

        ; Skip ally colors
        if (currentColor = AllyColor)
            continue

        PixelSearch, AimPixelX, AimPixelY, ScanL, ScanT, ScanR, ScanB, currentColor, ColorTolerance, Fast RGB
        if (!ErrorLevel) {
            ; Calculate distance and priority
            distance := Sqrt((AimPixelX - ZeroX)**2 + (AimPixelY - ZeroY)**2)

            ; Apply target priority logic
            GuiControlGet, TargetPriority,, TargetPriorityBox
            if (TargetPriority = "Smart") {
                score := currentPriority * 1000 - distance
                bestScore := bestTarget.priority * 1000 - bestTarget.distance
                if (score > bestScore) {
                    bestTarget.x := AimPixelX
                    bestTarget.y := AimPixelY
                    bestTarget.distance := distance
                    bestTarget.priority := currentPriority
                    targetFound := true
                }
            } else if (TargetPriority = "Closest") {
                if (distance < bestTarget.distance) {
                    bestTarget.x := AimPixelX
                    bestTarget.y := AimPixelY
                    bestTarget.distance := distance
                    bestTarget.priority := currentPriority
                    targetFound := true
                }
            }
        }
    }

    ; Set final target with offsets
    if (targetFound) {
        targetX := bestTarget.x + OffsetX
        targetY := bestTarget.y + OffsetY + BoneOffsetY
    }
}

; ====== ESSENTIAL GDI+ FUNCTIONS =======
Gdip_Startup() {
    if !DllCall("GetModuleHandle", "str", "gdiplus")
        DllCall("LoadLibrary", "str", "gdiplus")
    VarSetCapacity(si, 16, 0), si := Chr(1)
    DllCall("gdiplus\GdiplusStartup", "uint*", pToken, "uint", &si, "uint", 0)
    return pToken
}

Gdip_Shutdown(pToken) {
    DllCall("gdiplus\GdiplusShutdown", "uint", pToken)
    if hModule := DllCall("GetModuleHandle", "str", "gdiplus")
        DllCall("FreeLibrary", "uint", hModule)
    return 0
}

; ====== CONFIGURATION MANAGEMENT =======
UpdateConfigList() {
    ; Placeholder for config management
    return
}

UpdateLiveValues:
    ; Update all live text displays
    GuiControlGet, AimStrengthValue,, AimStrengthSlider
    AimStrength := AimStrengthValue / 100.0
    GuiControl,, AimStrengthText, Aim Strength: %AimStrength%

    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    Smoothness := SmoothnessValue / 10.0
    GuiControl,, SmoothnessText, Smoothness: %Smoothness%

    GuiControlGet, FOVValue,, FOVSlider
    FOVRadius := FOVValue
    GuiControl,, FOVText, FOV Radius: %FOVRadius% px

    GuiControlGet, MaxDistValue,, MaxDistanceSlider
    MaxAimDistance := MaxDistValue
    GuiControl,, MaxDistanceText, Max Distance: %MaxAimDistance% px
return

; ====== RAINBOW HEADER ANIMATION =======
UpdateRainbowColor:
    hue := Mod(hue + 3, 360)
    newColor := HSVtoRGB(hue, saturation, brightness)
    GuiControl, % "+c" . newColor, RainbowText
return

HSVtoRGB(h, s, v) {
    h := Mod(h, 360)
    s := s/255
    v := v/255

    if (s = 0) {
        r := v, g := v, b := v
    } else {
        h := h/60
        i := Floor(h)
        f := h - i
        p := v * (1 - s)
        q := v * (1 - s * f)
        t := v * (1 - s * (1 - f))

        if (i = 0) {
            r := v, g := t, b := p
        } else if (i = 1) {
            r := q, g := v, b := p
        } else if (i = 2) {
            r := p, g := v, b := t
        } else if (i = 3) {
            r := p, g := q, b := v
        } else if (i = 4) {
            r := t, g := p, b := v
        } else {
            r := v, g := p, b := q
        }
    }

    r := Round(r * 255)
    g := Round(g * 255)
    b := Round(b * 255)

    return Format("0x{:02X}{:02X}{:02X}", r, g, b)
}

; ====== VISUAL FUNCTIONS (Placeholders) =======
ClearSnapLine() {
    ; Placeholder
    return
}

ClearFOVCircle() {
    ; Placeholder
    return
}

; ====== HOTKEYS WITH HARDWARE AWARENESS =======
; Toggle GUI visibility
Insert::
    IfWinExist, MW3 Ultimate Aimbot v4.0 - Interception Edition {
        Gui, Hide
    } else {
        Gui, Show
    }
return

; Emergency stop with hardware cleanup
F12::
    ; Immediately disable all features
    GuiControl,, AimbotEnabledBox, 0
    GuiControl,, RapidFireBox, 0
    GuiControl,, SuperJumpBox, 0
    GuiControl,, TriggerBotBox, 0

    ; Stop all timers
    SetTimer, MainAimbotLoop, Off
    SetTimer, UpdateLiveValues, Off
    SetTimer, UpdateRainbowColor, Off

    if (InterceptionEnabled) {
        MsgBox, 0x30, Emergency Stop,
        (
        ALL FEATURES DISABLED!
        Hardware-level input stopped.

        Press F12 again to restart the system.
        )
    } else {
        MsgBox, 0x30, Emergency Stop,
        (
        ALL FEATURES DISABLED!
        Standard input stopped.

        Press F12 again to restart the system.
        )
    }

    ; Restart system after emergency stop
    Sleep, 1000
    SetTimer, MainAimbotLoop, %UpdateRate%
    SetTimer, UpdateLiveValues, 10
    SetTimer, UpdateRainbowColor, 50
return

; Hardware-aware feature toggles
F1::
    GuiControlGet, currentState,, AimbotEnabledBox
    newState := !currentState
    GuiControl,, AimbotEnabledBox, %newState%
    Gosub, ToggleAimbot
return

F7::
    GuiControlGet, currentState,, RapidFireBox
    newState := !currentState
    GuiControl,, RapidFireBox, %newState%
    Gosub, ToggleRapidFire
return

F8::
    GuiControlGet, currentState,, SuperJumpBox
    newState := !currentState
    GuiControl,, SuperJumpBox, %newState%
    Gosub, ToggleSuperJump
return

F9::
    GuiControlGet, currentState,, TriggerBotBox
    newState := !currentState
    GuiControl,, TriggerBotBox, %newState%
    Gosub, ToggleTriggerBot
return

; ====== CLEANUP AND EXIT =======
GuiMove:
    PostMessage, 0xA1, 2, , , A
return

Close:
GuiClose:
    ; Clean up all resources including Interception
    ClearSnapLine()
    ClearFOVCircle()
    SetTimer, MainAimbotLoop, Off
    SetTimer, UpdateLiveValues, Off
    SetTimer, UpdateRainbowColor, Off

    ; Cleanup Interception driver
    CleanupInterception()

    Gdip_Shutdown(pToken)
    ExitApp
return
