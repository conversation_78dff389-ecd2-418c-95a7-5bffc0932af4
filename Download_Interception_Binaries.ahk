; ====== INTERCEPTION BINARIES DOWNLOADER =======
; Downloads the correct compiled Interception files
; Automatically sets up everything needed for MW3 system
; ====== INTERCEPTION BINARIES DOWNLOADER =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== DOWNLOAD SETUP =======
TrayTip, Interception Setup, Setting up Interception binaries download..., 3, 1

; Create GUI for download progress
Gui, Destroy
Gui, Color, 0x2d2d30
Gui, Font, s12 Bold cLime, Arial
Gui, Add, Text, x10 y10 w480 Center, INTERCEPTION BINARIES SETUP

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y40 w460, This will download the correct Interception files for MW3 system

; Status display
Gui, Add, Text, x20 y70 w460 h100 vStatusText, Status: Ready to download Interception binaries...`n`nWhat will be downloaded:`n• install-interception.exe (driver installer)`n• interception.dll (library for AutoHotkey)`n• Command-line tools for testing

; Download button
Gui, Add, Button, x20 y180 w460 h40 gStartDownload, DOWNLOAD INTERCEPTION BINARIES

; Manual instructions
Gui, Font, s8 cCyan, Arial
Gui, Add, Text, x20 y240 w460, MANUAL DOWNLOAD INSTRUCTIONS:
Gui, Add, Text, x20 y260 w460, 1. Go to: https://github.com/oblitum/Interception/releases
Gui, Add, Text, x20 y280 w460, 2. Download: Interception.zip (latest release)
Gui, Add, Text, x20 y300 w460, 3. Extract to: C:\Interception\
Gui, Add, Text, x20 y320 w460, 4. Copy interception.dll to your MW3 script folder

; Safety notice
Gui, Font, s8 cYellow, Arial
Gui, Add, Text, x20 y350 w460, IMPORTANT: Only download from official GitHub repository
Gui, Add, Text, x20 y370 w460, SAFETY: Create system restore point before installing driver

Gui, Show, w500 h400, Interception Binaries Setup

; ====== DOWNLOAD FUNCTION =======
StartDownload:
    GuiControl,, StatusText, Starting download process...`n`nStep 1: Creating download directory...
    
    ; Create download directory
    downloadDir := A_ScriptDir . "\Interception-Binaries"
    FileCreateDir, %downloadDir%
    
    if (!FileExist(downloadDir)) {
        GuiControl,, StatusText, ERROR: Failed to create download directory!`n`nPlease create manually:`n%downloadDir%
        return
    }
    
    GuiControl,, StatusText, Step 2: Opening GitHub releases page...`n`nPlease download manually:`n1. Browser will open to GitHub releases`n2. Download Interception.zip`n3. Extract to: %downloadDir%
    
    ; Open GitHub releases page
    Run, https://github.com/oblitum/Interception/releases
    
    ; Wait a moment then show next steps
    Sleep, 3000
    
    GuiControl,, StatusText, Step 3: Manual download required...`n`nAfter downloading and extracting:`n1. Copy install-interception.exe to: %downloadDir%`n2. Copy interception.dll to: %A_ScriptDir%`n3. Run install-interception.exe as Administrator`n4. Restart your computer
    
    ; Create instruction file
    instructionFile := downloadDir . "\INSTALLATION_INSTRUCTIONS.txt"
    FileAppend, 
    (
INTERCEPTION DRIVER INSTALLATION INSTRUCTIONS
=============================================

STEP 1: DOWNLOAD FILES
----------------------
1. Download Interception.zip from: https://github.com/oblitum/Interception/releases
2. Extract the zip file
3. You should see these files:
   - install-interception.exe
   - interception.dll
   - Various sample programs

STEP 2: INSTALL DRIVER
----------------------
1. Copy install-interception.exe to: %downloadDir%
2. Open Command Prompt as Administrator
3. Navigate to: %downloadDir%
4. Run: install-interception.exe /install
5. RESTART YOUR COMPUTER (required!)

STEP 3: SETUP DLL
-----------------
1. Copy interception.dll to: %A_ScriptDir%
2. This allows AutoHotkey to communicate with the driver

STEP 4: TEST INSTALLATION
-------------------------
1. After restart, run: Test_Interception_Driver.ahk
2. Should show "Test Passed!" if everything works
3. If test fails, check the error messages

STEP 5: RUN MW3 SYSTEM
----------------------
1. Run: MW3_Interception_Driver.ahk as Administrator
2. Click "VALIDATE HARDWARE SETUP"
3. Click "START HARDWARE SYSTEM"
4. Test features in MW3 private match first

SAFETY NOTES:
=============
- Create system restore point before installing
- Only download from official GitHub repository
- Can uninstall with: install-interception.exe /uninstall
- Start with conservative settings for account safety

TROUBLESHOOTING:
===============
- If DLL not found: Copy interception.dll to script folder
- If driver fails: Run installer as Administrator
- If context fails: Restart computer after installation
- If still issues: Check Windows compatibility

Your MW3 script folder: %A_ScriptDir%
Download directory: %downloadDir%
    ), %instructionFile%
    
    TrayTip, Instructions Created, Installation instructions saved to INSTALLATION_INSTRUCTIONS.txt, 3, 1
    
    ; Open the instruction file
    Run, notepad.exe "%instructionFile%"
return

GuiClose:
    ExitApp
