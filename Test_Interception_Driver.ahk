; ====== INTERCEPTION DRIVER TEST SCRIPT =======
; Simple test to verify Interception driver installation
; Run this FIRST to ensure everything is working
; ====== INTERCEPTION DRIVER TEST SCRIPT =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input
SetWorkingDir %A_ScriptDir%

; ====== TEST INTERCEPTION DRIVER =======
TrayTip, Interception Test, Testing Interception driver installation..., 3, 1

; Test 1: Check if interception.dll exists
if (!FileExist("interception.dll")) {
    MsgBox, 16, Test Failed, interception.dll not found!`n`nPlease:`n1. Download interception.dll from GitHub`n2. Place it in the same folder as this script`n3. Run this test again
    ExitApp
}

; Test 2: Try to load the DLL
hInterception := DllCall("LoadLibrary", "Str", "interception.dll", "Ptr")
if (!hInterception) {
    Msg<PERSON><PERSON>, 16, Test Failed, Failed to load interception.dll!`n`nPossible issues:`n1. Interception driver not installed`n2. System not restarted after driver installation`n3. DLL architecture mismatch (32-bit vs 64-bit)
    ExitApp
}

; Test 3: Try to create Interception context
InterceptionContext := DllCall("interception.dll\interception_create_context", "Ptr")
if (!InterceptionContext) {
    MsgBox, 16, Test Failed, Failed to create Interception context!`n`nThis means:`n1. Interception driver is not properly installed`n2. Driver installation requires restart`n3. Need to run as Administrator
    DllCall("FreeLibrary", "Ptr", hInterception)
    ExitApp
}

; Test 4: Success!
MsgBox, 64, Test Passed!, Interception driver test PASSED!`n`nResults:`n✅ interception.dll found`n✅ DLL loaded successfully`n✅ Interception context created`n✅ Driver is properly installed`n`nYou can now run the MW3 Interception Driver!

; Cleanup
DllCall("interception.dll\interception_destroy_context", "Ptr", InterceptionContext)
DllCall("FreeLibrary", "Ptr", hInterception)

TrayTip, Test Complete, Interception driver test completed successfully!, 3, 1
ExitApp
