/*! For license information please see 778.js.LICENSE.txt */
"use strict";(self.webpackChunk_telescope_monorepo=self.webpackChunk_telescope_monorepo||[]).push([[778],{4758:(e,t,n)=>{n.r(t),n.d(t,{default:()=>qn});var r,o,i,a=n(2284),l=n(467),c=n(6540),s=n(1448),u=n(7578),d=n(436),f=n(2509),h=n(354),m=n(5147),v=n(3802),p=n(4212),g=n(8500);const y=(0,v.DU)(r||(r=(0,m.A)(["\n  ","\n\n  *,\n  *::before,\n  *::after {\n    margin: 0;\n    padding: 0;\n    box-sizing: inherit;\n    transition-property: none !important;\n\n    ","\n  }\n\n  html {\n    box-sizing: border-box;\n    font-size: ",";\n  }\n\n  body {\n    font-family: 'Hitmarker Text';\n    color: black;\n    font-weight: normal;\n  }\n"])),"local"===g.A.getState().global.env&&(0,v.AH)(o||(o=(0,m.A)(["\n          @font-face {\n              font-family: 'Hitmarker Text';\n              src: url(",") format('opentype');\n          }\n      "])),p),("PC"===g.A.getState().global.platform||"PS5"===g.A.getState().global.platform||"XBOXSX"===g.A.getState().global.platform)&&(0,v.AH)(i||(i=(0,m.A)(["\n            transition-property: all !important;\n        "]))),g.A.getState().global.is4k?"125%":"62.5%");var w,b=n(296),E=n(8168);var x,L=v.Ay.div(w||(w=(0,m.A)(["\n    border-radius: 0.2rem;\n    background: linear-gradient(0deg, rgba(26, 24, 25, 0.7847514005602241) 0%, rgba(61, 65, 66, 1) 67%, rgba(62, 62, 62, 1) 100%), #282828;\n\n    border: 0.1rem solid rgba(255, 255, 255, 0.25);\n\n    &:hover {\n        border: 0.1rem solid rgba(118, 219, 255, 0.8);\n    }\n\n    .radial-background {\n        background: radial-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.134));\n    }\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 5.4rem;\n    height: 5.4rem;\n    transition: all 0.2s;\n\n    &:hover {\n        color: #9aa39a;\n        background: linear-gradient(0deg, #2e4a54, #2e4a54),\n            radial-gradient(48.38% 48.38% at 49.77% 48.42%, rgba(104, 158, 171, 0) 0%, rgba(104, 158, 171, 0.312) 73.44%),\n            linear-gradient(0deg, rgba(118, 219, 255, 0.8), rgba(118, 219, 255, 0.8));\n\n        span {\n            opacity: 1;\n        }\n    }\n"]))),A=v.Ay.div(x||(x=(0,m.A)(["\n    width: ","rem;\n    height: ","rem;\n    display: ",";\n    opacity: ",";\n    justify-content: center;\n    align-items: center;\n    margin-left: ","rem;\n    margin-right: ","rem;\n"])),(function(e){return e.width}),(function(e){return e.height}),(function(e){return e.isPC?"none":"flex"}),(function(e){return e.opacity}),(function(e){return e.marginLeft?e.marginLeft:0}),(function(e){return e.marginRight?e.marginRight:0}));function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},O.apply(this,arguments)}const k=function(e){return c.createElement("svg",O({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 58 36"},e),c.createElement("path",{d:"M4 35c-1.65 0-3-1.35-3-3V12C1 5.93 5.93 1 12 1h34c6.07 0 11 4.93 11 11v20c0 1.65-1.35 3-3 3H4Z",style:{fill:"#2e2e2e"}}),c.createElement("path",{d:"M46 0H12C5.37 0 0 5.37 0 12v20c0 2.21 1.79 4 4 4h50c2.21 0 4-1.79 4-4V12c0-6.63-5.37-12-12-12ZM27.85 28H15V8h4.41v16.15h8.44V28Zm16.4 0H30.34v-3.73h4.79V13.36c-1.71.5-3.59.59-5.38.62V10.6c2.79-.06 4.44-.79 5.59-2.59h4.15v16.27h4.76v3.73Z",style:{fill:"#e7e7e7"}}))};var _,j;function S(){return S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},S.apply(this,arguments)}const P=function(e){return c.createElement("svg",S({viewBox:"0 0 47 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),_||(_=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.954 8.583c0-.994-.636-1.47-1.908-1.47h-2.662v2.98h2.861c1.113.04 1.709-.477 1.709-1.51ZM29.444 12.04h-3.06v3.497h3.02c1.351 0 2.066-.557 2.066-1.67 0-1.231-.675-1.827-2.026-1.827Z",fill:"#fff"})),j||(j=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M43.192 0H11.166c-.994 0-1.908.437-2.583 1.152L.834 10.013A3.372 3.372 0 0 0 0 12.238v8.345A3.417 3.417 0 0 0 3.417 24h39.855a3.417 3.417 0 0 0 3.417-3.417V3.457C46.649 1.55 45.099 0 43.192 0ZM12.517 17.722V4.967h2.82v10.41h6.24v2.345h-9.06Zm20.503-.994c-.835.676-1.907.994-3.258.994h-6.2V4.967h6.04c2.703 0 4.054 1.033 4.054 3.139 0 1.192-.596 2.106-1.749 2.702.795.238 1.391.636 1.749 1.192.397.556.596 1.232.596 2.066 0 1.113-.398 1.987-1.232 2.662Z",fill:"#fff"})))};function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const M=function(e){return c.createElement("svg",I({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 58 36",width:28,height:16},e),c.createElement("path",{d:"M4.02 35h50c1.65 0 3-1.35 3-3V12c0-6.07-4.93-11-11-11h-34c-6.07 0-11 4.93-11 11v20c0 1.65 1.35 3 3 3Z",style:{fill:"#2e2e2e"}}),c.createElement("path",{d:"M45.52 24.27v-.01 3.73H31.61v-3.73h4.79V13.35c-1.7.5-3.59.59-5.38.62v-3.38c2.8-.06 4.44-.8 5.59-2.59h4.15v16.27h4.76ZM28.81 28v.01h-5.03l-3.97-7.38h-2.38v7.38h-4.41v-20h8.18c4.91 0 6.85 1.85 6.85 6.32 0 3.26-1.06 5.14-3.56 5.88L28.81 28ZM58 12c0-6.63-5.37-12-12-12H12C5.37 0 0 5.37 0 12v20c0 2.21 1.79 4 4 4h50c2.21 0 4-1.79 4-4V12Zm-34.37 2.32c0-1.82-.76-2.56-2.91-2.56h-3.29v5.09h3.29c2.14 0 2.91-.74 2.91-2.53Z",style:{fill:"#e7e7e7"}}))};var Z,C;function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)}const H=function(e){return c.createElement("svg",N({viewBox:"0 0 47 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Z||(Z=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M32.093 8.569c0-.992-.635-1.468-1.905-1.468h-2.657v2.975h2.856c1.11.04 1.706-.476 1.706-1.507ZM30.585 12.02H27.53v3.49h3.015c1.389 0 2.063-.555 2.063-1.665 0-1.23-.674-1.825-2.023-1.825ZM17.256 7.14h-3.054v3.57h3.094c.595 0 1.07-.118 1.388-.396.357-.278.516-.754.516-1.428 0-.635-.159-1.11-.516-1.349-.357-.238-.793-.396-1.428-.396Z",fill:"#fff"})),C||(C=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M45.779 9.997 38.043 1.15C37.408.397 36.456 0 35.464 0H3.491A3.496 3.496 0 0 0 0 3.49v17.098A3.411 3.411 0 0 0 3.412 24H43.2a3.411 3.411 0 0 0 3.412-3.412v-8.33c0-.833-.278-1.666-.834-2.261Zm-26.222 7.696c-.159-.397-.278-1.111-.357-2.222-.08-1.15-.317-1.904-.635-2.221-.317-.357-.833-.516-1.587-.516h-2.816v4.998h-2.817V4.998h6.863c1.071 0 1.944.318 2.658.992.714.674 1.071 1.508 1.071 2.539 0 1.587-.674 2.658-2.023 3.213v.04c.436.119.793.357 1.071.634.238.318.437.675.595 1.072.159.396.199 1.07.238 1.943.04 1.15.198 1.944.516 2.34h-2.777v-.078Zm14.598-.992c-.833.674-1.904.992-3.253.992h-6.188V4.959h6.03c2.697 0 4.046 1.031 4.046 3.134 0 1.19-.595 2.102-1.745 2.697.793.238 1.388.635 1.745 1.19.397.556.595 1.23.595 2.063 0 1.11-.397 1.983-1.23 2.658Z",fill:"#fff"})))};var R;function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}const V=function(e){return c.createElement("svg",B({viewBox:"0 0 20 20",fill:"#E7E7E7",xmlns:"http://www.w3.org/2000/svg"},e),R||(R=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm4.588-16 1.41 1.412-4.58 4.586L16 14.575l-1.412 1.41-4.58-4.575L5.423 16l-1.41-1.412L8.596 10 4 5.41 5.412 4l4.594 4.588L14.588 4Z"})))};var G,T;function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},F.apply(this,arguments)}const z=function(e){return c.createElement("svg",F({viewBox:"0 0 24 24",fill:"#fff",xmlns:"http://www.w3.org/2000/svg"},e),G||(G=c.createElement("path",{d:"m11.819 8.241-1.585 4.506h3.17l-1.54-4.506h-.045Z",fill:"inherit"})),T||(T=c.createElement("path",{d:"M12 0C5.366 0 0 5.366 0 12s5.366 12 12 12 12-5.366 12-12S18.611 0 12 0Zm3.057 17.502-.929-2.74H9.532l-.974 2.74h-2.74l4.665-12.294h2.762l4.597 12.294h-2.785Z",fill:"inherit"})))};var U;function X(){return X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},X.apply(this,arguments)}const D=function(e){return c.createElement("svg",X({viewBox:"0 0 20 20",fill:"#e7e7e7",xmlns:"http://www.w3.org/2000/svg",width:20,height:20},e),U||(U=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm4-10a4 4 0 1 1-8 0 4 4 0 0 1 8 0Zm2 0a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z"})))};var q,Y;function K(){return K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},K.apply(this,arguments)}const W=function(e){return c.createElement("svg",K({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),q||(q=c.createElement("path",{d:"M14.015 8.74c0-.951-.611-1.404-1.834-1.404H9.623v2.875h2.762c1.087-.022 1.63-.498 1.63-1.471ZM12.589 12.045H9.623v3.374h2.898c1.313 0 1.97-.543 1.97-1.608.022-1.2-.634-1.766-1.902-1.766Z",fill:"#fff"})),Y||(Y=c.createElement("path",{d:"M12 0C5.366 0 0 5.366 0 12s5.366 12 12 12 12-5.366 12-12S18.634 0 12 0Zm4.008 16.551c-.793.634-1.834.95-3.148.95H6.906V5.209h5.796c2.604 0 3.917 1.018 3.917 3.034 0 1.154-.566 2.015-1.676 2.58.748.227 1.314.59 1.699 1.155.362.544.566 1.2.566 1.97 0 1.11-.408 1.97-1.2 2.604Z",fill:"#fff"})))};var $;function J(){return J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},J.apply(this,arguments)}const Q=function(e){return c.createElement("svg",J({viewBox:"0 0 20 20",fill:"#fff",xmlns:"http://www.w3.org/2000/svg",width:20,height:20},e),$||($=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm-4-6V6h8v8H6ZM4 4h12v12H4V4Z"})))};var ee;function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},te.apply(this,arguments)}const ne=function(e){return c.createElement("svg",te({viewBox:"0 0 20 20",fill:"inherit",xmlns:"http://www.w3.org/2000/svg"},e),ee||(ee=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm5.344-9.597c.281.506.281 1.08.281 1.627v.048c0 .856 0 1.619-.756 2.276-.474.41-1.17.616-1.881.616-.712 0-1.423-.206-1.897-.63-.474-.41-.726-.902-.726-1.75v-.52h1.986v.589c0 .451.281.67.637.67a.592.592 0 0 0 .474-.233c.18-.217.179-.807.178-1.14v-.131c0-.323.002-.838-.163-1.08-.104-.137-.252-.233-.504-.233h-.474V8.981h.474a.54.54 0 0 0 .415-.164c.185-.208.181-.636.178-.929v-.201c.004-.27.008-.634-.149-.84-.089-.109-.207-.19-.43-.19-.31 0-.562.19-.562.587v.67h-1.986v-.547c0-.793.252-1.326.726-1.736.474-.424 1.111-.615 1.823-.615.8 0 1.392.218 1.807.601.519.465.756 1.19.756 2.037 0 .506-.015 1.012-.252 1.477a1.692 1.692 0 0 1-.578.615c.267.192.474.397.623.657Zm-6.11 2.862v1.75H4.376V5.282h2.104v7.984h2.756Z"})))};var re;function oe(){return oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},oe.apply(this,arguments)}const ie=function(e){return c.createElement("svg",oe({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 52 50.1",width:20,height:20,fill:"#e7e7e7"},e),re||(re=c.createElement("path",{className:"ls-console_svg__cls-1",d:"M26.69 14.1v.01c0 4.31 2.37 6.28 7.3 6.28s7.33-2 7.33-6.08c0-3.71-1.6-5.47-6.2-6.14-2.78-.41-3.79-1.07-3.79-2.55 0-1.31.84-2.09 2.72-2.09 1.88 0 2.78.87 2.78 2.58h4.23c0-4.14-2.23-6.11-6.92-6.11-4.69 0-7.15 2.02-7.15 6.05 0 3.59 1.65 5.21 6.02 5.91 2.93.46 3.97 1.16 3.97 2.78 0 1.42-.96 2.11-2.87 2.11-2.12 0-3.19-.84-3.19-2.75h-4.23Zm-1.86 2.15H16.4V.14H12V20.1h12.83v-3.85Zm-4.848 27.721c0 3.31 3.137 6.192 5.893 6.129 2.756-.063 5.913-2.783 5.913-6.093l-11.806-.036ZM44 40.1c4.42 0 8-3.58 8-8s-3.58-8-8-8H8c-4.42 0-8 3.58-8 8s3.58 8 8 8h36Z"})))};function ae(){return ae=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ae.apply(this,arguments)}const le=function(e){return c.createElement("svg",ae({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 64",width:32,height:32,fill:"#e7e7e7"},e),c.createElement("path",{d:"M9.86 57h26.41c3.09 0 5.85-2.08 6.72-5.04l12.03-41.12c.27-.91.09-1.88-.48-2.64-.57-.76-1.45-1.2-2.4-1.2H9.86c-1.65 0-3 1.35-3 3v44c0 1.65 1.35 3 3 3Z",style:{fill:"#231f20",opacity:.2}}),c.createElement("path",{d:"M45.11 22h-.01v3.85h-5.35V42h-4.41V25.85h-5.35V22h15.12ZM29.79 42v.01h-5.03l-3.97-7.38h-2.38v7.38H14v-20h8.18c4.91 0 6.85 1.85 6.85 6.32 0 3.26-1.06 5.14-3.56 5.88L29.79 42Zm26.33-30.88h.01C56.88 8.56 54.96 6 52.29 6H10c-2.21 0-4 1.79-4 4v43.99c0 2.21 1.79 4 4 4h26.41c3.56 0 6.68-2.34 7.68-5.75l12.03-41.12Zm-31.5 17.2c0-1.82-.76-2.56-2.91-2.56h-3.29v5.09h3.29c2.15 0 2.91-.74 2.91-2.53Z",style:{fill:"#e7e7e7"}}))};function ce(){return ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ce.apply(this,arguments)}const se=function(e){return c.createElement("svg",ce({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 64"},e),c.createElement("path",{d:"M27.58 57c-3.09 0-5.85-2.07-6.72-5.04L8.84 10.84c-.27-.92-.09-1.88.48-2.64.57-.76 1.45-1.2 2.4-1.2H54c1.65 0 3 1.35 3 3v44c0 1.65-1.35 3-3 3H27.58Z",style:{fill:"#231f20",opacity:.2}}),c.createElement("path",{d:"M54 6H11.71c-2.67 0-4.59 2.56-3.84 5.12L19.9 52.24c1 3.41 4.12 5.75 7.68 5.75H54c2.21 0 4-1.79 4-4V10c0-2.21-1.79-4-4-4ZM36.85 42H24V22h4.41v16.15h8.44V42Zm14.26-16.15h-5.35V42h-4.41V25.85H36V22h15.12v3.85Z",style:{fill:"#fff"}}))};var ue;function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},de.apply(this,arguments)}const fe=function(e){return c.createElement("svg",de({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 50.29 52"},e),c.createElement("path",{d:"M19.87 51c-3.09 0-5.85-2.07-6.72-5.04L1.12 4.84c-.27-.92-.09-1.88.48-2.64C2.17 1.44 3.05 1 4 1h42.29c1.65 0 3 1.35 3 3v44c0 1.65-1.35 3-3 3H19.87Z",style:{fill:"#2e2e2e"}}),ue||(ue=c.createElement("path",{d:"M28.92 36H16.29V16.35h4.34v15.87h8.3v3.79Zm15.81-3.67V36H30.48v-2.02c0-3.73 1.68-5.95 6.07-8.01 2.95-1.39 3.93-2.37 3.93-4.02 0-1.42-.87-2.34-2.75-2.34s-2.89.95-2.89 2.98v.55h-4.05v-.93c0-4.08 2.46-6.21 6.97-6.21s6.97 2.17 6.97 5.92c0 3.27-1.71 5.35-6.01 7.28-2.66 1.19-3.67 1.94-3.93 3.12h9.94ZM46.29 0H4C1.33 0-.59 2.56.16 5.12l12.03 41.12c1 3.41 4.12 5.75 7.68 5.75h26.42c2.21 0 4-1.79 4-4V4c0-2.21-1.79-4-4-4Z"})))};var he;function me(){return me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},me.apply(this,arguments)}const ve=function(e){return c.createElement("svg",me({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 50.29 52",width:32,height:32,fill:"#e7e7e7"},e),c.createElement("path",{d:"M4 51h26.42c3.09 0 5.85-2.08 6.72-5.04L49.17 4.84c.27-.91.09-1.88-.48-2.64-.57-.76-1.45-1.2-2.4-1.2H4C2.35 1 1 2.35 1 4v44c0 1.65 1.35 3 3 3Z",style:{fill:"#2e2e2e"}}),he||(he=c.createElement("path",{d:"M46.29 0H4C1.79 0 0 1.79 0 4v44c0 2.21 1.79 4 4 4h26.42c3.55 0 6.68-2.34 7.68-5.75L50.12 5.12C50.87 2.56 48.95 0 46.28 0h.01ZM16.58 36l-3.9-7.25h-2.34V36H6V16.35h8.04c4.83 0 6.73 1.82 6.73 6.21 0 3.21-1.04 5.06-3.5 5.78L21.52 36h-4.94Zm21.18-3.67V36H23.51v-2.02c0-3.73 1.68-5.95 6.07-8.01 2.95-1.39 3.93-2.37 3.93-4.02 0-1.42-.87-2.34-2.75-2.34s-2.89.95-2.89 2.98v.55h-4.05v-.92c0-4.07 2.46-6.21 6.97-6.21s6.97 2.17 6.97 5.92c0 3.27-1.71 5.35-6.01 7.28-2.66 1.19-3.67 1.94-3.93 3.12h9.94Zm-21.159-9.885c-.085-1.636-1.378-2.419-2.717-2.39l-3.64.086.086 4.961 3.611-.086c2.11 0 2.603-1.392 2.632-2.503l.028-.068Z"})))};var pe,ge,ye;function we(){return we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},we.apply(this,arguments)}const be=function(e){return c.createElement("svg",we({id:"Prompts",xmlns:"http://www.w3.org/2000/svg",fill:"current",viewBox:"0 0 64 64"},e),pe||(pe=c.createElement("defs",null,c.createElement("style",null,".cls-1{fill:#fff}"))),c.createElement("g",{id:"D-Pad_Horizontal"},c.createElement("g",{style:{opacity:.2}},c.createElement("path",{d:"M26.08 62c-1.1 0-2-.9-2-2V46.94c0-1.34.52-2.59 1.46-3.54l5.74-5.74c.19-.19.44-.29.71-.29s.52.1.71.29l5.71 5.71c.94.94 1.46 2.2 1.46 3.54v13.1c0 1.1-.9 2-2 2h-11.8Zm21.01-21.92c-1.34 0-2.59-.52-3.54-1.46l-5.9-5.9a.996.996 0 0 1 0-1.41l5.81-5.81c.93-.93 2.22-1.46 3.54-1.46h12.99c1.1 0 2 .9 2 2V38.1c0 1.1-.9 2-2 2h-12.9Zm-15.1-13.44a.99.99 0 0 1-.71-.29l-5.74-5.74c-.94-.94-1.46-2.2-1.46-3.54V4c0-1.1.9-2 2-2h11.8c1.1 0 2 .9 2 2v13.1c0 1.34-.52 2.59-1.46 3.54l-5.71 5.71a.99.99 0 0 1-.71.29Zm-15.1 13.44c1.34 0 2.59-.52 3.54-1.46l5.9-5.9a.996.996 0 0 0 0-1.41l-5.81-5.81c-.94-.94-2.2-1.46-3.54-1.46H4c-1.1 0-2 .9-2 2V38.1c0 1.1.9 2 2 2h12.9Z",style:{fill:"#231f20"},id:"Protection"})),ge||(ge=c.createElement("path",{className:"cls-1",d:"M36.88 5v12.1c0 .53-.21 1.04-.59 1.41l-4.3 4.3-4.33-4.33c-.38-.38-.59-.88-.59-1.41V5h9.8m-4.88 36.19 4.3 4.3c.38.38.59.88.59 1.41V59h-9.8V46.94c0-.53.21-1.04.59-1.41L32 41.2M37.88 1h-11.8c-1.65 0-3 1.35-3 3v13.06c0 1.59.63 3.12 1.76 4.24l5.74 5.74c.39.39.9.59 1.41.59s1.02-.2 1.41-.59l5.71-5.71a5.987 5.987 0 0 0 1.76-4.24V4c0-1.65-1.35-3-3-3ZM32 36.36c-.51 0-1.02.2-1.41.59l-5.74 5.74a5.987 5.987 0 0 0-1.76 4.24v13.06c0 1.65 1.35 3 3 3h11.8c1.66 0 3-1.34 3-3v-13.1c0-1.59-.63-3.12-1.76-4.24l-5.71-5.71c-.39-.39-.9-.59-1.41-.59Z",id:"Unused"})),ye||(ye=c.createElement("path",{className:"cls-1",d:"m36.95 33.41 5.9 5.9a5.987 5.987 0 0 0 4.24 1.76h12.9c1.65 0 3-1.35 3-3V26.01c0-1.66-1.34-3-3-3H47c-1.59 0-3.12.63-4.24 1.76l-5.81 5.81c-.78.78-.78 2.05 0 2.83Zm-9.9-2.83-5.81-5.81A5.987 5.987 0 0 0 17 23.01H4c-1.65 0-3 1.35-3 3v12.06c0 1.65 1.35 3 3 3h12.9c1.59 0 3.12-.63 4.24-1.76l5.9-5.9c.78-.78.78-2.05 0-2.83Z",id:"Used"}))))};var Ee,xe,Le;function Ae(){return Ae=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ae.apply(this,arguments)}const Oe=function(e){return c.createElement("svg",Ae({id:"Prompts",fill:"current",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 64"},e),Ee||(Ee=c.createElement("defs",null,c.createElement("style",null,".cls-1{fill:#fff}.cls-3{fill:none;stroke:#fff;stroke-miterlimit:10;stroke-width:4px}"))),c.createElement("g",{id:"D-Pad_Horizontal"},c.createElement("path",{id:"Protection",d:"M24.83 62c-1.1 0-2-.9-2-2V41h-19c-1.1 0-2-.9-2-2V25c0-1.1.9-2 2-2h19V4c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2v19h19c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2h-19v19c0 1.1-.9 2-2 2h-14Z",style:{fill:"#231f20",opacity:.2}}),xe||(xe=c.createElement("g",{id:"Outline"},c.createElement("path",{className:"cls-1",d:"M38 5v54H26V5h12m1-4H25c-1.66 0-3 1.34-3 3v56c0 1.66 1.34 3 3 3h14c1.66 0 3-1.34 3-3V4c0-1.66-1.34-3-3-3Z"}),c.createElement("path",{className:"cls-3",d:"M42 22 22 42M42 42 22 22"}))),Le||(Le=c.createElement("path",{id:"Used",className:"cls-1",d:"M4 22c-1.66 0-3 1.34-3 3v14c0 1.66 1.34 3 3 3h18l10-10-10-10H4Zm8 10v6l-4.8-3.6a3 3 0 0 1 0-4.8L12 26v6Zm48-10H42L32 32l10 10h18c1.66 0 3-1.34 3-3V25c0-1.66-1.34-3-3-3Zm-8 16V26l8 6-8 6Z"}))))};var ke;function _e(){return _e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_e.apply(this,arguments)}const je=function(e){return c.createElement("svg",_e({viewBox:"0 0 20 20",fill:"#fff",xmlns:"http://www.w3.org/2000/svg",width:20,height:20},e),ke||(ke=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10ZM8.882 5.236 10 3l1.118 2.236L15 13l1 2H4l1-2 3.882-7.764ZM7.236 13 10 7.472 12.764 13H7.236Z"})))};var Se;function Pe(){return Pe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pe.apply(this,arguments)}const Ie=function(e){return c.createElement("svg",Pe({viewBox:"0 0 24 24",fill:"inherit",xmlns:"http://www.w3.org/2000/svg"},e),Se||(Se=c.createElement("path",{d:"M12 0C5.366 0 0 5.366 0 12s5.366 12 12 12 12-5.366 12-12S18.634 0 12 0Zm1.2 12.793v4.709h-2.694v-4.777L5.977 5.23h3.057l2.875 4.845 2.853-4.845h3.012L13.2 12.793Z"})))};var Me;function Ze(){return Ze=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ze.apply(this,arguments)}const Ce=function(e){return c.createElement("svg",Ze({className:"xbox-X-svg",viewBox:"0 0 107 106",fill:"inherit",xmlns:"http://www.w3.org/2000/svg"},e),Me||(Me=c.createElement("path",{className:"svg-fill-path default-white",d:"M53.1 0C23.8 0 .1 23.7.1 53s23.7 53 53 53 53-23.7 53-53-23.7-53-53-53Zm11 77.3L52.2 58.5 40.1 77.3H26.6l19-28.4-17.5-25.8h13.8l10.6 17.3 11-17.3h13.1L59.2 49l18.9 28.3h-14Z"})))},Ne=n.p+"ccca030fdb91cc4d3db3.png";var He;function Re(){return Re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Re.apply(this,arguments)}const Be=function(e){return c.createElement("svg",Re({viewBox:"0 0 16 32",fill:"inherit",xmlns:"http://www.w3.org/2000/svg"},e),He||(He=c.createElement("path",{d:"M0 0h4l12 16L4 32H0l12-16L0 0Z"})))};var Ve;function Ge(){return Ge=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ge.apply(this,arguments)}const Te=function(e){return c.createElement("svg",Ge({viewBox:"0 0 16 32",fill:"inherit",xmlns:"http://www.w3.org/2000/svg"},e),Ve||(Ve=c.createElement("path",{d:"M16 32h-4L0 16 12 0h4L4 16l12 16Z"})))};var Fe=function(e,t){return t?2*e:e},ze=function(e){var t=e.type,n=e.is4k,r=e.lastInputGamepad,o=e.fill;switch(t){case"LB_L1":if(3===r)return c.createElement(k,{width:Fe(28,n),height:Fe(16,n)});if(2===r)return c.createElement(P,{width:Fe(28,n),height:Fe(16,n)});break;case"RB_R1":if(3===r)return c.createElement(M,{width:Fe(28,n),height:Fe(16,n)});if(2===r)return c.createElement(H,{width:Fe(28,n),height:Fe(16,n)});break;case"XBA_PSCROSS":if(3===r)return c.createElement(V,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});if(2===r)return c.createElement(z,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});break;case"XBB_PSCIRCLE":if(3===r)return c.createElement(D,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});if(2===r)return c.createElement(W,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});break;case"XBX_PSSQUARE":if(3===r)return c.createElement(Q,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});if(2===r)return c.createElement(Ce,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});break;case"DPAD_LEFT_RIGHT":if(3===r)return c.createElement(be,{width:n?40:35,height:n?40:35,fill:o||"#e7e7e7"});if(2===r)return c.createElement(Oe,{width:n?40:35,height:n?40:35,fill:o||"#e7e7e7"});break;case"L3":if(3===r)return c.createElement(ne,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});if(2===r)return c.createElement(ie,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});break;case"RT_R2":if(3===r)return c.createElement(ve,{width:Fe(32,n),height:Fe(32,n),fill:o||"#e7e7e7"});if(2===r)return c.createElement(le,{width:Fe(32,n),height:Fe(32,n),fill:o||"#e7e7e7"});break;case"LT_L2":if(3===r)return c.createElement(fe,{width:Fe(32,n),height:Fe(32,n),fill:o||"#e7e7e7"});if(2===r)return c.createElement(se,{width:Fe(32,n),height:Fe(32,n),fill:o||"#e7e7e7"});break;case"XBY_PSTRIANGLE":if(3===r)return c.createElement(je,{width:Fe(20,n),height:Fe(20,n),fill:o||"#e7e7e7"});if(2===r)return c.createElement(Ie,{width:n?40:24,height:n?40:24,fill:o||"#e7e7e7"});break;default:return null}return null};function Ue(e){var t=e.lastInputDevice,n=e.lastInputGamepad,r=e.isCrossConfirm,o=e.is4k,i=e.type,a=e.style,l=Fe(0===t?20:24,o),s=i;return 1!==t||"rightArrow"!==i&&"leftArrow"!==i?("leftArrowLarge"!==i&&"rightArrowLarge"!==i||(s=r?"XBB_PSCIRCLE":"XBA_PSCROSS"),c.createElement("div",{style:{alignItems:"center",flexDirection:"row",display:"flex"}},"leftArrowLarge"===i&&c.createElement("img",{width:l,height:l,src:Ne}),0===t&&c.createElement(A,{width:2.2,height:2.2,marginLeft:0,marginRight:0,style:a,opacity:.9},c.createElement(ze,{type:s,is4k:o,lastInputGamepad:n,fill:null})),"rightArrowLarge"===i&&c.createElement("img",{style:{transform:"scaleX(-1)"},width:l,height:l,src:Ne}))):c.createElement(A,{width:2.2,height:2.2,marginLeft:0,marginRight:0,opacity:.9},"rightArrow"===i&&c.createElement(Be,{width:Fe(16),height:Fe(32),fill:"#9AA39A"}),"leftArrow"===i&&c.createElement(Te,{width:Fe(16),height:Fe(32),fill:"#9AA39A"}))}var Xe,De,qe,Ye,Ke,We,$e,Je,Qe,et,tt=["is4k","lastInputDevice","lastInputGamepad","isCrossConfirm","onClick","onMouseLeave","onMouseOver","type","buttonStyles"],nt=function(e){var t=e.is4k,n=e.lastInputDevice,r=e.lastInputGamepad,o=e.isCrossConfirm,i=e.onClick,a=e.onMouseLeave,l=e.onMouseOver,s=e.type,u=e.buttonStyles,d=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,tt);return c.createElement("div",{className:"radial-background"},c.createElement(L,(0,E.A)({onClick:i,onMouseLeave:a,onMouseOver:l,onFocus:l,style:u},d),c.createElement(Ue,{isCrossConfirm:o,lastInputDevice:n,lastInputGamepad:r,is4k:t,type:s})))};function rt(e){return c.createElement(nt,(0,E.A)({},e,{type:"leftArrowLarge"}))}function ot(e){return c.createElement(nt,(0,E.A)({},e,{type:"rightArrowLarge"}))}var it,at,lt=v.Ay.section.attrs({"data-ts":"news-container"})(Xe||(Xe=(0,m.A)(["\n    width: 192rem;\n    height: 80rem;\n    padding-left: 8rem;\n    overflow: hidden;\n    position: relative;\n\n    .news-list {\n        width: calc((174.4rem + 1.6rem) * ",");\n        height: 77.2rem;\n        display: flex;\n        align-items: flex-start;\n        transition: all 0.2s;\n\n        &-translate {\n            transform: translateX(","rem);\n        }\n    }\n\n    .carousel-controls {\n        display: flex;\n        justify-content: center;\n        width: 174.4rem;\n        .bar {\n            height: 0.4rem;\n            width: 2.8rem;\n            background: #323331;\n            transition: all 0.2s;\n\n            &:not(:first-child) {\n                margin-left: 0.8rem;\n            }\n\n            &-active {\n                background: ",";\n            }\n        }\n    }\n\n    .arrow {\n        position: absolute;\n        top: 42%;\n        display: flex;\n        align-items: center;\n        transition: all 0.2s;\n        overflow: visible;\n        border-radius: 0.4rem;\n        z-index: 5;\n        justify-content: center;\n\n        &-left {\n            left: 3%;\n            padding-right: 0.4rem;\n        }\n\n        &-right {\n            right: 3%;\n            padding-left: 0.4rem;\n        }\n    }\n"])),(function(e){return e.totalMessages||1}),(function(e){return-e.direction*(e.selectedMessage*e.direction*175.6)}),(function(e){return e.theme.colors.neutralLight})),ct=v.Ay.div.attrs({"data-ts":"news-item"})(De||(De=(0,m.A)(["\n    width: 174.4rem;\n    height: 75.6rem;\n    margin: 0 0.8rem;\n    overflow: hidden;\n    position: relative;\n    display: flex;\n    align-items: center;\n    transition: all 0.2s;\n    border-radius: 0.2rem;\n    border: 0.1rem solid ",";\n\n    .image {\n        width: 100%;\n        height: 100%;\n        background: ",";\n        background-size: cover;\n        background-position: center center;\n        transition: all 0.2s;\n    }\n\n    .content-overlay {\n        position: absolute;\n        top: 0;\n        right: 0;\n        bottom: 0;\n        left: 0;\n\n        .content-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            align-items: flex-end;\n            padding: 8rem;\n\n            ","\n            ","\n\n            ","\n        }\n\n        .content {\n            width: 60.8rem;\n            transition: all 0.2s;\n            ","\n            .message-title {\n                font-family: 'Hitmarker Normal';\n                font-size: 7.2rem;\n                color: ",";\n                text-transform: uppercase;\n                line-height: 1;\n                letter-spacing: ",";\n\n                ","\n\n                ","\n            }\n\n            .message-body {\n                font-size: 2rem;\n                color: ",";\n                margin-top: 0.8rem;\n                line-height: ",";\n                font-weight: normal;\n                letter-spacing: 0.1rem;\n                b,\n                strong {\n                    font-weight: bold;\n                    color: #e7e7e7;\n                }\n                ","\n\n                ","\n            }\n\n            .button-bar {\n                margin-top: 2.4rem;\n                display: flex;\n\n                .button {\n                    padding: 1.6rem 2rem;\n                    font-size: 2rem;\n                    font-weight: normal;\n                    text-transform: uppercase;\n                    width: 35rem;\n                    border-radius: 0.2rem;\n                    display: flex;\n                    align-items: center;\n                    cursor: pointer;\n\n                    .button-text {\n                        display: flex;\n                        flex: auto;\n                        justify-content: center;\n                    }\n\n                    &-primary {\n                        color: white;\n                        background: ",";\n                        border: 0.1rem solid ",";\n                    }\n\n                    &-secondary {\n                        color: white;\n                        background: linear-gradient(\n                                0deg,\n                                rgba(26, 24, 25, 0.7847514005602241) 0%,\n                                rgba(61, 65, 66, 1) 67%,\n                                rgba(62, 62, 62, 1) 100%\n                            ),\n                            #282828;\n                        border: 0.1rem solid ",";\n                    }\n                }\n            }\n        }\n    }\n"])),(function(e){return e.theme.colors.neutralLightLow}),(function(e){return e.imageUrl?"url(".concat(e.imageUrl,")"):"none"}),(function(e){return"center"===e.alignment&&(0,v.AH)(qe||(qe=(0,m.A)(["\n                    justify-content: center;\n                "])))}),(function(e){return"right"===e.alignment&&(0,v.AH)(Ye||(Ye=(0,m.A)(["\n                    justify-content: flex-end;\n                "])))}),(function(e){return"arabic"===e.language&&(0,v.AH)(Ke||(Ke=(0,m.A)(["\n                    text-align: right;\n                "])))}),(function(e){return"arabic"===e.language&&(0,v.AH)(We||(We=(0,m.A)(["\n                    direction: rtl;\n                "])))}),(function(e){return e.theme.colors.neutralLight}),(function(e){return"arabic"===e.language?"0":"0.3rem"}),(function(e){return"small"===e.titleFontSize&&(0,v.AH)($e||($e=(0,m.A)(["\n                        font-size: 4rem;\n                    "])))}),(function(e){return"large"===e.titleFontSize&&(0,v.AH)(Je||(Je=(0,m.A)(["\n                        font-size: 7.8rem;\n                    "])))}),(function(e){return e.theme.colors.neutralLightLow}),(function(e){return"arabic"===e.language?"130%":"115%"}),(function(e){return"normal"===e.longFontSize&&(0,v.AH)(Qe||(Qe=(0,m.A)(["\n                        font-size: 2.4rem;\n                    "])))}),(function(e){return"large"===e.longFontSize&&(0,v.AH)(et||(et=(0,m.A)(["\n                        font-size: 2.8rem;\n                    "])))}),(function(e){return e.theme.colors.gradients.active}),(function(e){return e.theme.colors.borders.darkBlue}),(function(e){return e.theme.colors.borders.default})),st=n(5458);function ut(){return ut=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ut.apply(this,arguments)}const dt=function(e){return c.createElement("svg",ut({viewBox:"0 0 24 24",fill:"#fff",xmlns:"http://www.w3.org/2000/svg"},e),it||(it=c.createElement("path",{d:"m11.819 8.241-1.585 4.506h3.17l-1.54-4.506h-.045Z",fill:"inherit"})),at||(at=c.createElement("path",{d:"M12 0C5.366 0 0 5.366 0 12s5.366 12 12 12 12-5.366 12-12S18.611 0 12 0Zm3.057 17.502-.929-2.74H9.532l-.974 2.74h-2.74l4.665-12.294h2.762l4.597 12.294h-2.785Z",fill:"inherit"})))};var ft,ht;function mt(){return mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mt.apply(this,arguments)}const vt=function(e){return c.createElement("svg",mt({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),ft||(ft=c.createElement("path",{d:"M14.015 8.74c0-.951-.611-1.404-1.834-1.404H9.623v2.875h2.762c1.087-.022 1.63-.498 1.63-1.471ZM12.589 12.045H9.623v3.374h2.898c1.313 0 1.97-.543 1.97-1.608.022-1.2-.634-1.766-1.902-1.766Z",fill:"inherit"})),ht||(ht=c.createElement("path",{d:"M12 0C5.366 0 0 5.366 0 12s5.366 12 12 12 12-5.366 12-12S18.634 0 12 0Zm4.008 16.551c-.793.634-1.834.95-3.148.95H6.906V5.209h5.796c2.604 0 3.917 1.018 3.917 3.034 0 1.154-.566 2.015-1.676 2.58.748.227 1.314.59 1.699 1.155.362.544.566 1.2.566 1.97 0 1.11-.408 1.97-1.2 2.604Z",fill:"inherit"})))};var pt;function gt(){return gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},gt.apply(this,arguments)}const yt=function(e){return c.createElement("svg",gt({viewBox:"0 0 20 20",fill:"#e7e7e7",xmlns:"http://www.w3.org/2000/svg",width:20,height:20},e),pt||(pt=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm4-10a4 4 0 1 1-8 0 4 4 0 0 1 8 0Zm2 0a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z"})))};var wt;function bt(){return bt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},bt.apply(this,arguments)}const Et=function(e){return c.createElement("svg",bt({viewBox:"0 0 20 20",fill:"inherit",xmlns:"http://www.w3.org/2000/svg",width:20,height:20},e),wt||(wt=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm4.588-16 1.41 1.412-4.58 4.586L16 14.575l-1.412 1.41-4.58-4.575L5.423 16l-1.41-1.412L8.596 10 4 5.41 5.412 4l4.594 4.588L14.588 4Z"})))};var xt;function Lt(){return Lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Lt.apply(this,arguments)}const At=function(e){return c.createElement("svg",Lt({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),xt||(xt=c.createElement("g",{fill:"#E7E7E7"},c.createElement("path",{d:"m4 12.66 9.734 9.7H20l-9.734-9.7H4ZM4 10.7 13.734 1H20l-9.734 9.7H4Z"}))))};var Ot;function kt(){return kt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},kt.apply(this,arguments)}const _t=function(e){return c.createElement("svg",kt({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Ot||(Ot=c.createElement("path",{d:"m20 12.66-9.734 9.7H4l9.734-9.7H20ZM20 10.7 10.266 1H4l9.734 9.7H20Z",fill:"#E7E7E7"})))};function jt(){return jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jt.apply(this,arguments)}const St=function(e){return c.createElement("svg",jt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 58 36"},e),c.createElement("path",{d:"M4 35c-1.65 0-3-1.35-3-3V12C1 5.93 5.93 1 12 1h34c6.07 0 11 4.93 11 11v20c0 1.65-1.35 3-3 3H4Z",style:{fill:"#2e2e2e"}}),c.createElement("path",{d:"M46 0H12C5.37 0 0 5.37 0 12v20c0 2.21 1.79 4 4 4h50c2.21 0 4-1.79 4-4V12c0-6.63-5.37-12-12-12ZM27.85 28H15V8h4.41v16.15h8.44V28Zm16.4 0H30.34v-3.73h4.79V13.36c-1.71.5-3.59.59-5.38.62V10.6c2.79-.06 4.44-.79 5.59-2.59h4.15v16.27h4.76v3.73Z",style:{fill:"#e7e7e7"}}))};var Pt,It;function Mt(){return Mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Mt.apply(this,arguments)}const Zt=function(e){return c.createElement("svg",Mt({viewBox:"0 0 47 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Pt||(Pt=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.954 8.583c0-.994-.636-1.47-1.908-1.47h-2.662v2.98h2.861c1.113.04 1.709-.477 1.709-1.51ZM29.444 12.04h-3.06v3.497h3.02c1.351 0 2.066-.557 2.066-1.67 0-1.231-.675-1.827-2.026-1.827Z",fill:"#fff"})),It||(It=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M43.192 0H11.166c-.994 0-1.908.437-2.583 1.152L.834 10.013A3.372 3.372 0 0 0 0 12.238v8.345A3.417 3.417 0 0 0 3.417 24h39.855a3.417 3.417 0 0 0 3.417-3.417V3.457C46.649 1.55 45.099 0 43.192 0ZM12.517 17.722V4.967h2.82v10.41h6.24v2.345h-9.06Zm20.503-.994c-.835.676-1.907.994-3.258.994h-6.2V4.967h6.04c2.703 0 4.054 1.033 4.054 3.139 0 1.192-.596 2.106-1.749 2.702.795.238 1.391.636 1.749 1.192.397.556.596 1.232.596 2.066 0 1.113-.398 1.987-1.232 2.662Z",fill:"#fff"})))};function Ct(){return Ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ct.apply(this,arguments)}const Nt=function(e){return c.createElement("svg",Ct({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 58 36"},e),c.createElement("path",{d:"M4.02 35c-1.65 0-3-1.35-3-3V12c0-6.07 4.93-11 11-11h34c6.07 0 11 4.93 11 11v20c0 1.65-1.35 3-3 3h-50Z",style:{fill:"#2e2e2e"}}),c.createElement("path",{d:"M23.63 14.32c0 1.79-.77 2.53-2.91 2.53h-3.29v-5.09h3.29c2.15 0 2.91.74 2.91 2.56ZM58 12v20c0 2.21-1.79 4-4 4H4c-2.21 0-4-1.79-4-4V12C0 5.37 5.37 0 12 0h34c6.63 0 12 5.37 12 12ZM28.81 28l-4.32-7.79c2.5-.74 3.56-2.62 3.56-5.88 0-4.47-1.94-6.32-6.85-6.32h-8.18v20h4.41v-7.38h2.38l3.97 7.38h5.03Zm16.71-3.73h-4.76V8h-4.15c-1.15 1.79-2.79 2.53-5.59 2.59v3.38c1.79-.03 3.68-.12 5.38-.62v10.91h-4.79v3.73h13.91v-3.73Z",style:{fill:"#e7e7e7"}}))};var Ht,Rt;function Bt(){return Bt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Bt.apply(this,arguments)}const Vt=function(e){return c.createElement("svg",Bt({viewBox:"0 0 47 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Ht||(Ht=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M32.093 8.569c0-.992-.635-1.468-1.905-1.468h-2.657v2.975h2.856c1.11.04 1.706-.476 1.706-1.507ZM30.585 12.02H27.53v3.49h3.015c1.389 0 2.063-.555 2.063-1.665 0-1.23-.674-1.825-2.023-1.825ZM17.256 7.14h-3.054v3.57h3.094c.595 0 1.07-.118 1.388-.396.357-.278.516-.754.516-1.428 0-.635-.159-1.11-.516-1.349-.357-.238-.793-.396-1.428-.396Z",fill:"#fff"})),Rt||(Rt=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M45.779 9.997 38.043 1.15C37.408.397 36.456 0 35.464 0H3.491A3.496 3.496 0 0 0 0 3.49v17.098A3.411 3.411 0 0 0 3.412 24H43.2a3.411 3.411 0 0 0 3.412-3.412v-8.33c0-.833-.278-1.666-.834-2.261Zm-26.222 7.696c-.159-.397-.278-1.111-.357-2.222-.08-1.15-.317-1.904-.635-2.221-.317-.357-.833-.516-1.587-.516h-2.816v4.998h-2.817V4.998h6.863c1.071 0 1.944.318 2.658.992.714.674 1.071 1.508 1.071 2.539 0 1.587-.674 2.658-2.023 3.213v.04c.436.119.793.357 1.071.634.238.318.437.675.595 1.072.159.396.199 1.07.238 1.943.04 1.15.198 1.944.516 2.34h-2.777v-.078Zm14.598-.992c-.833.674-1.904.992-3.253.992h-6.188V4.959h6.03c2.697 0 4.046 1.031 4.046 3.134 0 1.19-.595 2.102-1.745 2.697.793.238 1.388.635 1.745 1.19.397.556.595 1.23.595 2.063 0 1.11-.397 1.983-1.23 2.658Z",fill:"#fff"})))};var Gt;function Tt(){return Tt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Tt.apply(this,arguments)}const Ft=function(e){return c.createElement("svg",Tt({viewBox:"0 0 20 20",fill:"#fff",xmlns:"http://www.w3.org/2000/svg",width:20,height:20},e),Gt||(Gt=c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm-4-6V6h8v8H6ZM4 4h12v12H4V4Z"})))};var zt;const Ut=v.Ay.div(zt||(zt=(0,m.A)(["\n    width: ","rem;\n    height: ","rem;\n    display: ",";\n    justify-content: center;\n    align-items: center;\n    margin-left: ","rem;\n    margin-right: ","rem;\n"])),(function(e){return e.width}),(function(e){return e.height}),(function(e){return e.isPC?"none":"flex"}),(function(e){return e.marginLeft?e.marginLeft:0}),(function(e){return e.marginRight?e.marginRight:0}));var Xt=function(e){var t=e.type,n=e.is4k,r=e.lastInputGamepad,o=e.fill;switch(t){case"LB_L1":if(3===r)return c.createElement(St,{width:n?48:24,height:n?32:16});if(2===r)return c.createElement(Zt,{width:n?48:24,height:n?32:16});break;case"RB_R1":if(3===r)return c.createElement(Nt,{width:n?48:24,height:n?32:16});if(2===r)return c.createElement(Vt,{width:n?48:24,height:n?32:16});break;case"XBA_PSCROSS":if(3===r)return c.createElement(Et,{width:n?40:20,height:n?40:20,fill:o||"#e7e7e7"});if(2===r)return c.createElement(dt,{width:n?40:20,height:n?40:20,fill:"#e7e7e7"});break;case"XBB_PSCIRCLE":if(3===r)return c.createElement(yt,{width:n?40:20,height:n?40:20,fill:o||"#e7e7e7"});if(2===r)return c.createElement(vt,{width:n?40:20,height:n?40:20,fill:o||"#e7e7e7"});break;case"XBX_PSSQUARE":return c.createElement(Ft,{width:n?40:20,height:n?40:20,fill:o||"#e7e7e7"})}return null};const Dt=function(e){var t=e.type,n=e.width,r=void 0===n?3.2:n,o=e.height,i=void 0===o?3.2:o,a=e.marginLeft,l=e.marginRight,u=e.fill,d=e.style,f=(0,s.d4)((function(e){return e.global.lastInputDevice})),h=(0,s.d4)((function(e){return e.global.lastInputGamepad})),m=(0,s.d4)((function(e){return e.global.is4k}));return 1!==f||"rightArrow"!==t&&"leftArrow"!==t?0===f?c.createElement(Ut,{width:r,height:i,marginLeft:a,marginRight:l,style:d},c.createElement(Xt,{type:t,is4k:m,lastInputGamepad:h,fill:u})):null:c.createElement(Ut,{marginLeft:a,marginRight:l},"rightArrow"===t&&c.createElement(_t,{width:m?48:24,height:m?96:48,fill:u||"#9AA39A"}),"leftArrow"===t&&c.createElement(At,{width:m?48:24,height:m?96:48,fill:u||"#9AA39A"}))};var qt=n(7294);const Yt=function(e){var t,n,r,o,i,a,l,d,f,h,m,v=e.message,p=e.currentIndex,g=e.expand,y=e.direct,w=e.isConsole,E=(0,s.wA)(),x=(0,s.d4)((function(e){return e.motd.selectedMessage})),L=(0,s.d4)((function(e){return e.motd.arrowPressed})),A=(0,s.d4)((function(e){return e.global.isCrossConfirm})),O=(0,s.d4)((function(e){return e.global.isFocused})),k=(0,s.d4)((function(e){return e.global.language})),_=(0,c.useState)(!1),j=(0,b.A)(_,2),S=j[0],P=j[1],I=(0,c.useRef)();return(0,s.d4)((function(e){return e.motd.messageSource})),c.createElement(ct,{imageUrl:null!=v&&null!==(t=v.content)&&void 0!==t&&t.popupImageAdditions&&(null==v||null===(n=v.content)||void 0===n?void 0:n.popupImageAdditions.length)>0?function(e){if(e){if(p!==x)return I.current&&clearInterval(I.current),e.content.popupImage;if(p===x&&O){var t,n,r,o,i=null==e||null===(t=e.content)||void 0===t?void 0:t.popupImageAdditions,a=null==e||null===(n=e.display)||void 0===n?void 0:n.cycleTimeOfImage,l=0;if(0===i.length)return;(o=(0,st.A)(i)).unshift(null==e||null===(r=e.content)||void 0===r?void 0:r.popupImage),document.querySelectorAll(".image").length>0&&(document.querySelectorAll(".image")[p].style.backgroundImage="url(".concat(o[0],")")),I.current&&clearInterval(I.current),m=setInterval((function(){l>=o.length&&(l=0),document.querySelectorAll(".image")[p].style.backgroundImage="url(".concat(o[l],")"),l++}),a),I.current=m}}}(v):null==v||null===(r=v.content)||void 0===r?void 0:r.popupImage,alignment:null==v||null===(o=v.display)||void 0===o?void 0:o.motdAlignment,titleFontSize:null==v||null===(i=v.display)||void 0===i?void 0:i.motdTitleFontSize,longFontSize:null==v||null===(a=v.display)||void 0===a?void 0:a.motdLongFontSize,language:k},c.createElement("div",{className:"image"}),c.createElement("div",{className:"content-overlay"},c.createElement("div",{className:"content-container"},c.createElement("div",{className:"content"},(null==v||null===(l=v.content)||void 0===l?void 0:l.title)&&c.createElement("div",{className:"message-title"},v.content.title),(null==v||null===(d=v.content)||void 0===d?void 0:d.contentLong)&&c.createElement("div",{className:"message-body",dangerouslySetInnerHTML:{__html:v.content.contentLong}}),((null==v||null===(f=v.content)||void 0===f?void 0:f.backgroundVideo)||(null==v||null===(h=v.content)||void 0===h?void 0:h.navURL))&&c.createElement("div",{className:"button-bar"},c.createElement("div",{className:w||S||L?"button button-primary":"button button-secondary",onClick:function(){return v.content.backgroundVideo?g():y()},onMouseEnter:function(){w||(P(!0),E(u.Gc.setArrowPressed(!1)))},onMouseLeave:function(){return w?null:P(!1)}},c.createElement(Dt,{type:A?"XBA_PSCROSS":"XBB_PSCIRCLE",width:2,height:2,marginRight:.8,fill:"white"}),c.createElement("div",{className:"button-text"},function(){var e,t,n=null===qt.A||void 0===qt.A||null===(e=qt.A[k])||void 0===e?void 0:e.select;if(v&&v.content)return v.content.backgroundVideo?null===qt.A||void 0===qt.A||null===(t=qt.A[k])||void 0===t?void 0:t.watchVideo:v.content.navURL&&v.data.navMenu&&v.data.navMenu||n}())))))))};var Kt=n(7107),Wt=n(6847),$t=n(3265),Jt=n(2473);function Qt(){Qt=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof h?t:h,a=Object.create(i.prototype),l=new k(o||[]);return r(a,"_invoke",{value:x(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function h(){}function m(){}function v(){}var p={};s(p,i,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(_([])));y&&y!==t&&n.call(y,i)&&(p=y);var w=v.prototype=h.prototype=Object.create(p);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function o(r,i,l,c){var s=d(e[r],e,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==(0,a.A)(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){o("next",e,l,c)}),(function(e){o("throw",e,l,c)})):t.resolve(f).then((function(e){u.value=e,l(u)}),(function(e){return o("throw",e,l,c)}))}c(s.arg)}var i;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){o(e,n,t,r)}))}return i=i?i.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var l=L(a,n);if(l){if(l===f)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function L(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,L(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function _(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:j}}function j(){return{value:void 0,done:!0}}return m.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:m,configurable:!0}),m.displayName=s(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,s(e,c,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(E.prototype),s(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(w),s(w,c,"Generator"),s(w,i,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=_,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:_(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}const en=function(){var e=(0,s.wA)(),t=(0,s.d4)((function(e){return e.motd.messages})),n=(0,s.d4)((function(e){return e.motd.selectedMessage})),r=((0,s.d4)((function(e){return e.motd.activeCarousel})),(0,s.d4)((function(e){return e.motd.arrowPressed}))),o=(0,s.d4)((function(e){return e.global.lastInputDevice})),i=(0,s.d4)((function(e){return e.global.isFocused})),a=(0,s.d4)((function(e){return e.global.isCrossConfirm})),f=(0,c.useState)(1),h=(0,b.A)(f,2),m=h[0],v=h[1],p="undefined"!=typeof Telescope_API_LoadComplete,g=1!==o,y=((0,s.d4)((function(e){return e.global.platform})),(0,s.d4)((function(e){return e.global.lastInputGamepad}))),w=(0,s.d4)((function(e){return e.global.is4k}));(0,c.useEffect)((function(){return"dev"===(0,$t.A)()&&(0,d.Jz)("debug_dump","init",{isTelescope:p},0),function(){(0,d.n)(n)}}),[]);var E=function(e){e.tsPage="motd",(0,d.Iu)(e)},x=function(){n!==t.length-1&&(console.log("Flip to next page"),(0,Jt.$)("ui_radial_button_select"),E({type:"click"}),e(u.Gc.setSelectedMessage(n+1)),(0,d.rK)(n),v(-1))},L=function(){0!==n&&(console.log("Flip to previous page"),(0,Jt.$)("ui_radial_button_select"),E({type:"click"}),e(u.Gc.setSelectedMessage(n-1)),(0,d.rK)(n),v(1))},A=function(e){window.Telescope_API_ReleaseFocus&&Telescope_API_ReleaseFocus(e)},O=function(){e(u.Gc.isExpanded(!0)),(0,d.iE)(n),(0,d.n)(n),(0,d.rK)(n,!0),(0,d.mq)(n)},k=function(){var e,r;return!(null===(e=t[n])||void 0===e||null===(r=e.content)||void 0===r||!r.backgroundVideo)},_=function(){var e,r;return!(null===(e=t[n])||void 0===e||null===(r=e.content)||void 0===r||!r.navURL)},j=function(){var e=(0,l.A)(Qt().mark((function e(){var r,o;return Qt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(0,d.O7)(),(0,d.iE)(n),(0,d.n)(n),e.next=5,(0,d.rK)(n,!0);case 5:window.Telescope_API_GotoByUri&&Telescope_API_GotoByUri(null===(r=t[n])||void 0===r||null===(o=r.content)||void 0===o?void 0:o.navURL);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,Wt.A)(n,L,x,r&&k()?O:r&&_()&&j),(0,Kt.A)(1,{LEFT:L,RIGHT:x,LTRIG:function(){return A(4)},UP:function(){return A(3)},XBA_PSCROSS:a&&k()?O:a&&_()&&j,XBB_PSCIRCLE:!a&&k()?O:!a&&_()&&j}),c.createElement(lt,{selectedMessage:n,totalMessages:null==t?void 0:t.length,isFocused:i,direction:m,onWheel:function(e){e.deltaY>0?x():e.deltaY<0&&L()}},c.createElement("div",{className:"news-list news-list-translate"},t.map((function(e,t){return c.createElement(Yt,{message:e,currentIndex:t,key:e.messageId,expand:O,direct:j,isConsole:g})}))),t.length>1&&c.createElement("div",{className:"carousel-controls"},t.map((function(e,t){return c.createElement("div",{className:"bar ".concat(n===t&&"bar-active"),key:e.messageId})}))),1===o&&c.createElement(c.Fragment,null,0!==n&&c.createElement("div",{className:"arrow arrow-left"},c.createElement(rt,{is4k:w,isCrossConfirm:a,lastInputDevice:o,lastInputGamepad:y,onClick:function(){return L()}})),n!==t.length-1&&c.createElement("div",{className:"arrow arrow-right"},c.createElement(ot,{is4k:w,isCrossConfirm:a,lastInputDevice:o,lastInputGamepad:y,onClick:function(){return x()}}))))};var tn,nn,rn,on,an,ln=v.Ay.section.attrs({"data-ts":"expanded-news-container"})(tn||(tn=(0,m.A)(["\n    width: 192rem;\n    height: 108rem;\n    background: black;\n    overflow-x: hidden;\n    overflow-y: hidden;\n\n    .close {\n        width: max-content;\n        height: 5rem;\n        color: white;\n        font-size: 3.6rem;\n        display: flex;\n        align-items: center;\n        text-transform: uppercase;\n        font-size: 2rem;\n\n        &-position {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin: 0 1rem;\n            padding: 0.4rem;\n            border-radius: 0.2rem;\n        }\n    }\n"]))),cn=v.Ay.div.attrs({"data-ts":"expanded-news-item"})(nn||(nn=(0,m.A)(["\n    width: 100%;\n    height: 103.2rem;\n    background: #e5e5e5;\n    border-top-left-radius: 1.6rem;\n    border-top-right-radius: 1.6rem;\n    overflow: hidden;\n    background: ",";\n    background-repeat: no-repeat;\n    background-size: cover;\n    background-position: center center;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n    border-top-left-radius: 2rem;\n    border-top-right-radius: 2rem;\n    overflow: none;\n    border: none;\n\n    .expanded-content {\n        height: 100%;\n        color: ",";\n        font-size: 5rem;\n        padding-bottom: 7.2rem;\n        padding-left: 7.8rem;\n        display: flex;\n        flex-direction: column;\n        justify-content: flex-end;\n        max-width: 75.5rem;\n\n        &-title {\n            width: 100%;\n            font-size: 7.2rem;\n            overflow: hidden;\n            white-space: nowrap;\n            margin-bottom: 3.2rem;\n        }\n\n        &-long {\n            font-size: 2.4rem;\n            margin-bottom: 3.2rem;\n            white-space: pre-line;\n        }\n\n        .message-cta {\n            display: flex;\n            align-items: center;\n            width: 33.4rem;\n            background: ",";\n            padding: 0.4rem 2rem;\n            font-size: 2rem;\n            color: ",";\n            text-transform: uppercase;\n            z-index: 4;\n        }\n    }\n\n    .video-control-container {\n        display: flex;\n        z-index: 5;\n\n        &-hidden {\n            display: none;\n        }\n\n        .control {\n            width: 12.8rem;\n            height: 3.2rem;\n            border: 0.1rem solid ",";\n            background: linear-gradient(180deg, rgba(255, 255, 255, 0.14) 0%, rgba(255, 255, 255, 0) 114.81%), rgba(20, 19, 19, 1);\n            border-radius: 0.4rem;\n            transition: all 0.2s;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            position: relative;\n\n            &:not(:first-child) {\n                margin-left: 0.8rem;\n            }\n\n            &:hover {\n                background: ",";\n                border: 0.1rem solid ",";\n            }\n\n            &-active {\n                background: ",";\n                border: 0.1rem solid ",";\n            }\n        }\n    }\n"])),(function(e){return e.imageUrl?"linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .68), rgba(0, 0, 0, 0.85)), url(".concat(e.imageUrl,")"):"none"}),(function(e){return e.theme.colors.neutralLight}),(function(e){return e.theme.colors.primaryGreen}),(function(e){return e.theme.colors.neutralDark}),(function(e){return e.theme.colors.elPaso}),(function(e){return e.theme.colors.gradients.active}),(function(e){return e.theme.colors.borders.darkBlue}),(function(e){return e.theme.colors.gradients.active}),(function(e){return e.theme.colors.borders.darkBlue})),sn=(v.Ay.nav.attrs({"data-ts":"expanded-news-nav"})(rn||(rn=(0,m.A)(["\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: 3;\n\n    .button-control {\n        height: 3.2rem;\n        width: 3.2rem;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n\n        &-left {\n            margin-left: 2.1rem;\n            margin-right: 1.2rem;\n        }\n\n        &-right {\n            margin-right: 2.1rem;\n            margin-left: 1.2rem;\n        }\n    }\n\n    .message-nav {\n        display: flex;\n\n        &-button {\n            width: 44rem;\n            background: rgba(20, 19, 19, 1);\n            margin-right: 1rem;\n            font-size: 3.2rem;\n            color: ",";\n            text-transform: uppercase;\n            padding: 1.6rem;\n            text-align: center;\n            border-bottom: 0.4rem solid ",";\n            transition: all 0.2s;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            white-space: nowrap;\n\n            &:last-of-type {\n                margin-right: 0;\n            }\n\n            &-unfocused {\n                border-bottom: 0.4rem solid transparent;\n                background: rgba(20, 19, 19, 1);\n            }\n        }\n    }\n"])),(function(e){return e.theme.colors.neutralLight}),(function(e){return e.theme.colors.primaryGreen})),v.Ay.div.attrs({"data-ts":"news-video"})(on||(on=(0,m.A)(["\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .video {\n        height: 100%;\n        width: 100%;\n    }\n\n    video {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n        &::cue {\n            font-family: 'Hitmarker Normal';\n            text-shadow: 0.3rem 0.3rem black;\n            background: none;\n            background-image: none;\n        }\n    }\n"])))),un=v.Ay.div(an||(an=(0,m.A)(["\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    div.spinner {\n        position: relative;\n        width: 5.4rem;\n        height: 5.4rem;\n        display: inline-block;\n        margin-left: 50%;\n        margin-right: 50%;\n        padding: 1rem;\n        border-radius: 1rem;\n        z-index: 2;\n    }\n\n    div.spinner div {\n        width: 6%;\n        height: 16%;\n        background: #fff;\n        position: absolute;\n        left: 49%;\n        top: 43%;\n        opacity: 0;\n        border-radius: 5rem;\n        box-shadow: 0 0 0.3rem rgba(0, 0, 0, 1);\n        animation: fade 1s linear infinite;\n    }\n\n    @keyframes fade {\n        from {\n            opacity: 1;\n        }\n        to {\n            opacity: 0.25;\n        }\n    }\n\n    div.spinner div.bar1 {\n        transform: rotate(0deg) translate(0, -130%);\n        animation-delay: 0s;\n    }\n\n    div.spinner div.bar2 {\n        transform: rotate(30deg) translate(0, -130%);\n        animation-delay: -0.9167s;\n    }\n\n    div.spinner div.bar3 {\n        transform: rotate(60deg) translate(0, -130%);\n        animation-delay: -0.833s;\n    }\n    div.spinner div.bar4 {\n        transform: rotate(90deg) translate(0, -130%);\n        animation-delay: -0.7497s;\n    }\n    div.spinner div.bar5 {\n        transform: rotate(120deg) translate(0, -130%);\n        animation-delay: -0.667s;\n    }\n    div.spinner div.bar6 {\n        transform: rotate(150deg) translate(0, -130%);\n        animation-delay: -0.5837s;\n    }\n    div.spinner div.bar7 {\n        transform: rotate(180deg) translate(0, -130%);\n        animation-delay: -0.5s;\n    }\n    div.spinner div.bar8 {\n        transform: rotate(210deg) translate(0, -130%);\n        animation-delay: -0.4167s;\n    }\n    div.spinner div.bar9 {\n        transform: rotate(240deg) translate(0, -130%);\n        animation-delay: -0.333s;\n    }\n    div.spinner div.bar10 {\n        transform: rotate(270deg) translate(0, -130%);\n        animation-delay: -0.2497s;\n    }\n    div.spinner div.bar11 {\n        transform: rotate(300deg) translate(0, -130%);\n        animation-delay: -0.167s;\n    }\n    div.spinner div.bar12 {\n        transform: rotate(330deg) translate(0, -130%);\n        animation-delay: -0.0833s;\n    }\n"])));const dn=function(){return c.createElement(un,null,c.createElement("div",{className:"spinner"},c.createElement("div",{className:"bar1"}),c.createElement("div",{className:"bar2"}),c.createElement("div",{className:"bar3"}),c.createElement("div",{className:"bar4"}),c.createElement("div",{className:"bar5"}),c.createElement("div",{className:"bar6"}),c.createElement("div",{className:"bar7"}),c.createElement("div",{className:"bar8"}),c.createElement("div",{className:"bar9"}),c.createElement("div",{className:"bar10"}),c.createElement("div",{className:"bar11"}),c.createElement("div",{className:"bar12"})))};var fn;const hn=function(e){var t=e.setOnStartLoading,n=e.src,r=e.videoFocused,o=e.pause,i=e.restart,a=e.setRestart,l=e.setPause,s=e.setVideoMounted,u=e.subtitlePath,f=(0,c.useState)(!1),h=(0,b.A)(f,2),m=h[0],v=h[1];(0,c.useEffect)((function(){var e=!1;if(r&&n){if((fn=fn||document.createElement("video")).setAttribute("src",n),fn.setAttribute("type","video/mp4"),fn.setAttribute("preload","metadata"),u){var o=document.createElement("track");o.setAttribute("src",u),o.setAttribute("kind","subtitles"),o.setAttribute("default",""),o.mode="showing",fn.appendChild(o)}document.querySelector(".video").appendChild(fn),fn.onloadstart=function(){e||(v(!0),s(!0),t&&t(!1))},fn.oncanplay=function(){e||(v(!1),t&&t(!0))},fn.onwaiting=function(){e||v(!0)},fn.onended=function(){e||l(!0)},fn.onplaying=function(){console.log("PLAYING")},fn.onpause=function(){console.log("PASUED")}}return function(){e=!0,document.querySelector(".video video")&&(u&&(document.querySelector(".video track").src="",document.querySelector(".video track").remove()),document.querySelector(".video video").src="",document.querySelector(".video video").remove()),t&&t(!1)}}),[]);return(0,c.useEffect)((function(){var e;o?fn.pause():(e=(0,d.D8)(),fn.volume=e,fn.play())}),[o]),(0,c.useEffect)((function(){i&&(fn.currentTime=0,a(!1))}),[i]),c.createElement(sn,null,m&&c.createElement(dn,null),c.createElement("div",{className:"video"}))};var mn;function vn(){return vn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vn.apply(this,arguments)}const pn=function(e){return c.createElement("svg",vn({viewBox:"0 0 16 22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),mn||(mn=c.createElement("path",{d:"M5.667 1.667H1v18.666h4.667V1.667ZM15 1.667h-4.667v18.666H15V1.667Z",stroke:"#E7E7E7",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})))};var gn;function yn(){return yn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},yn.apply(this,arguments)}const wn=function(e){return c.createElement("svg",yn({viewBox:"0 0 24 22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),gn||(gn=c.createElement("path",{d:"M1.5 2.694v6.222m0 0h6.3m-6.3 0 4.872-4.521a9.495 9.495 0 0 1 5.36-2.637 9.552 9.552 0 0 1 5.89 1.074 9.379 9.379 0 0 1 4.053 4.354 9.227 9.227 0 0 1 .59 5.885 9.315 9.315 0 0 1-3.111 5.054 9.517 9.517 0 0 1-5.562 2.193 9.54 9.54 0 0 1-5.78-1.548 9.351 9.351 0 0 1-3.676-4.668",stroke:"#E7E7E7",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})))};var bn;function En(){return En=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},En.apply(this,arguments)}const xn=function(e){return c.createElement("svg",En({viewBox:"0 0 15 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),bn||(bn=c.createElement("path",{d:"M1 1.833v16.334L13.833 10 1 1.833Z",fill:"#E7E7E7",stroke:"#E7E7E7",strokeWidth:2,strokeLinejoin:"round"})))};var Ln=n(3092);const An=function(e){var t,n,r,o,i=e.close,a=(0,s.d4)((function(e){return e.motd.messages})),l=(0,s.d4)((function(e){return e.motd.selectedMessage})),u=(0,s.d4)((function(e){return e.global.is4k})),d=(0,s.d4)((function(e){return e.global.isCrossConfirm})),f=a[l],h=(0,c.useState)(1),m=(0,b.A)(h,2),v=m[0],p=m[1],g=(0,c.useState)(null),y=(0,b.A)(g,2),w=(y[0],y[1]),E=(0,c.useState)(!1),x=(0,b.A)(E,2),L=x[0],A=x[1],O=(0,c.useState)(!1),k=(0,b.A)(O,2),_=k[0],j=k[1],S=(0,c.useState)(!1),P=(0,b.A)(S,2),I=P[0],M=P[1],Z=(0,c.useState)(!1),C=(0,b.A)(Z,2),N=C[0],H=C[1],R=(0,Ln.sg)(H,2e3),B=function(){H(!0),R(!1)},V=function(){G[v].callback()},G=[{type:"restart",icon:c.createElement(wn,{width:u?48:24,height:u?44:22}),callback:function(){j(!0),A(!1)}},{type:"playback",icon:c.createElement(pn,{width:u?32:16,height:u?44:22}),altIcon:c.createElement(xn,{width:u?30:15,height:u?40:20}),callback:function(){return A(!L)},swap:L}];return(0,Kt.A)(1,{LEFT:function(){B(),p(0===v?G.length-1:v-1)},RIGHT:function(){B(),p(v===G.length-1?0:v+1)},XBB_PSCIRCLE:d?i:V,XBA_PSCROSS:d?V:i,LTRIG:function(){},DOWN:function(){},UP:function(){}}),(0,c.useEffect)((function(){window.Telescope_API_FadeMusic&&Telescope_API_FadeMusic(0,.2)}),[]),c.createElement(cn,{imageUrl:null==f||null===(t=f.content)||void 0===t?void 0:t.popupImage},c.createElement("div",{className:"expanded-content"},(null==f||null===(n=f.content)||void 0===n?void 0:n.backgroundVideo)&&c.createElement(hn,{videoFocused:!0,src:null==f||null===(r=f.content)||void 0===r?void 0:r.backgroundVideo,pause:L,restart:_,setRestart:j,setPause:A,setVideoMounted:M,subtitlePath:null==f||null===(o=f.display)||void 0===o?void 0:o.videoSubtitleBookKeeperPath}),I&&c.createElement("div",{className:"video-control-container ".concat(!L&&!N&&"video-control-container-hidden")},G.map((function(e,t){return c.createElement("div",{className:"control ".concat(v===t&&"control-active"),onClick:function(){p(t),e.callback()},key:e.type,onMouseEnter:function(){return w(t)},onMouseLeave:function(){return w(null)}},v===t&&c.createElement(Dt,{type:d?"XBA_PSCROSS":"XBB_PSCIRCLE",width:2,height:2,marginLeft:.2,fill:"#e7e7e7",style:{position:"absolute",left:"2rem"}}),e.swap&&e.altIcon||e.icon)})))))};function On(){On=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof h?t:h,a=Object.create(i.prototype),l=new k(o||[]);return r(a,"_invoke",{value:x(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function h(){}function m(){}function v(){}var p={};s(p,i,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(_([])));y&&y!==t&&n.call(y,i)&&(p=y);var w=v.prototype=h.prototype=Object.create(p);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function o(r,i,l,c){var s=d(e[r],e,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==(0,a.A)(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){o("next",e,l,c)}),(function(e){o("throw",e,l,c)})):t.resolve(f).then((function(e){u.value=e,l(u)}),(function(e){return o("throw",e,l,c)}))}c(s.arg)}var i;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){o(e,n,t,r)}))}return i=i?i.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var l=L(a,n);if(l){if(l===f)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function L(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,L(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function _(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:j}}function j(){return{value:void 0,done:!0}}return m.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:m,configurable:!0}),m.displayName=s(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,s(e,c,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(E.prototype),s(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(w),s(w,c,"Generator"),s(w,i,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=_,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:_(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}const kn=function(){var e,t=(0,s.wA)(),n=(0,s.d4)((function(e){return e.global.is4k})),r=(0,s.d4)((function(e){return e.global.isCrossConfirm})),o=(0,s.d4)((function(e){return e.global.lastInputDevice})),i=(0,s.d4)((function(e){return e.global.lastInputGamepad})),a=(0,s.d4)((function(e){return e.motd.selectedMessage})),f=(0,s.d4)((function(e){return e.global.language})),h=(0,s.d4)((function(e){return e.motd.messages})),m=(0,c.useState)(!1),v=(0,b.A)(m,2),p=v[0],g=v[1],y=function(){var e=(0,l.A)(On().mark((function e(){var n,r;return On().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=document.querySelector(".video video"),r=document.querySelector(".video track"),t(u.Gc.isExpanded(!1)),t(u.Gc.setNeedFullScreen(!1)),n&&(n.src="",r&&(n.textTracks[0].mode="hidden",r.src="",r.remove())),window.Telescope_API_SetDisplayMode&&Telescope_API_SetDisplayMode(0),Telescope_CB_LoseFocus(),(0,d.H5)(a,h[a].messageId),(0,d.Ek)(a);case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return c.createElement(ln,{isHovered:p},c.createElement("div",{className:"close"},c.createElement("div",{className:"close-position"},c.createElement(rt,{is4k:n,isCrossConfirm:r,lastInputDevice:o,lastInputGamepad:i,onClick:function(){return y()},onMouseEnter:function(){return g(!0)},onMouseLeave:function(){return g(!1)},buttonStyles:{width:"4.4rem",height:"4.4rem"}})),null===qt.A||void 0===qt.A||null===(e=qt.A[f])||void 0===e?void 0:e.close),c.createElement(An,{close:y}))};var _n,jn,Sn,Pn,In,Mn,Zn,Cn,Nn,Hn,Rn,Bn,Vn=v.Ay.section.attrs({"data-ts":"fullscreen-news-container"})(_n||(_n=(0,m.A)(["\n    width: 192rem;\n    height: 108rem;\n    padding: ",";\n    overflow: hidden;\n    position: relative;\n    background: black;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    background-color: transparent;\n\n    .news-list {\n        width: calc((192rem) * ",");\n        height: 100%;\n        display: flex;\n        align-items: flex-start;\n        transition: all 0.2s;\n        &-translate {\n            transform: translateX(","rem);\n        }\n    }\n\n    .divider {\n        border: 0.01rem solid rgba(231, 231, 231, 0.12);\n    }\n\n    .carousel-controls {\n        position: absolute;\n        display: flex;\n        justify-content: center;\n        width: 100%;\n        top: 89%;\n        left: 50%;\n        transform: translate(-50%, 0);\n        .bar {\n            height: 0.4rem;\n            width: 2.8rem;\n            background: #323331;\n            transition: all 0.2s;\n\n            &:not(:first-child) {\n                margin-left: 0.8rem;\n            }\n\n            &-active {\n                background: ",";\n            }\n        }\n    }\n\n    .arrow {\n        position: absolute;\n        top: 50%;\n        border-radius: 0.4rem;\n        z-index: 5;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        transition: all 0.2s;\n        overflow: visible;\n\n        &-left {\n            left: 1%;\n            padding-right: 0.4rem;\n        }\n\n        &-right {\n            right: 1%;\n            padding-left: 0.4rem;\n        }\n    }\n\n    .header-footer {\n        z-index: 1;\n        width: 100%;\n        margin: 0 auto;\n        padding: 0 4rem;\n        position: absolute;\n    }\n    .header {\n        &__title {\n            color: rgba(231, 231, 231, 0.6);\n            font-size: 3.2rem;\n            font-weight: 500;\n            letter-space: -0.01rem;\n            line-height: 3.8rem;\n            padding: 2.5rem 0;\n            text-transform: uppercase;\n        }\n    }\n    .footer {\n        font-weight: 700;\n        font-size: 2rem;\n        bottom: 2rem;\n        position: absolute;\n        .button {\n            margin-top: 1.5rem;\n            margin-left: auto;\n        }\n    }\n\n    .button {\n        padding: 1.6rem 2rem;\n        font-size: 2rem;\n        font-weight: normal;\n        text-transform: uppercase;\n        width: max-content;\n        border-radius: 0.2rem;\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n\n        &-long {\n            width: 35rem !important;\n        }\n\n        &-text {\n            font-family: 'Hitmarker Text';\n            display: flex;\n            flex: auto;\n            justify-content: center;\n        }\n\n\n        &-primary {\n            color: white;\n            background: ",";\n            border: 0.1rem solid ",";\n        }\n\n        &-secondary {\n            color: white;\n            background: linear-gradient(\n                    0deg,\n                    rgba(26, 24, 25, 0.7847514005602241) 0%,\n                    rgba(61, 65, 66, 1) 67%,\n                    rgba(62, 62, 62, 1) 100%\n                ),\n                #282828;\n            border: 0.1rem solid ",";\n        &-neutral {\n            color: white;\n            background: linear-gradient(180deg, rgba(231, 231, 231, 0.9) 0%, rgba(231, 231, 231, 0.6) 100%);\n            border: 0.01rem solid rgba(20, 19, 19, 0.24);\n\n            &:hover {\n                background: ",";\n\n                svg {\n                    fill: ",";\n                }\n            }\n        }\n    }\n"])),(function(e){return e.totalMessages,"0"}),(function(e){return e.totalMessages||1}),(function(e){return-e.direction*(e.selectedMessage*e.direction*192)}),(function(e){return e.theme.colors.neutralLight}),(function(e){return e.theme.colors.gradients.active}),(function(e){return e.theme.colors.borders.darkBlue}),(function(e){return e.theme.colors.borders.default}),(function(e){return e.theme.colors.primaryGreen}),(function(e){return e.theme.colors.neutralDark})),Gn=v.Ay.div.attrs({"data-ts":"fullscreen-news-item"})(jn||(jn=(0,m.A)(["\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    position: relative;\n    display: flex;\n    align-items: center;\n    transition: all 0.2s;\n\n    ","\n\n    .image {\n        width: 100%;\n        height: 100%;\n        background: ",";\n        background-size: cover;\n        background-position: center center;\n        // border: 0.2rem solid ",";\n        // border-top: 0.4rem solid ",";\n        // border-bottom: 0.4rem solid ",";\n\n        transition: all 0.2s;\n\n        ","\n    }\n\n    .content-overlay {\n        position: absolute;\n        height: 100%;\n        width: 100%;\n        bottom: 13%;\n\n        .content-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            align-items: flex-end;\n            padding: 0 8rem;\n\n            ","\n            ","\n\n                ","\n        }\n\n        .content {\n            width: 60.8rem;\n            transition: all 0.2s;\n\n            ","\n\n            .message-title {\n                font-family: 'Hitmarker Normal';\n                font-size: 7.2rem;\n                color: ",";\n                text-transform: uppercase;\n                line-height: 1;\n                letter-spacing: ",";\n\n                ","\n\n                ","\n            }\n\n            .message-body {\n                font-size: 2rem;\n                color: ",";\n                margin-top: 0.8rem;\n                line-height: 115%;\n                font-weight: normal;\n                letter-spacing: 0.1rem;\n\n                b,\n                strong {\n                    font-weight: bold;\n                    color: #e7e7e7;\n                }\n                ","\n\n                ","\n            }\n\n            .button-bar {\n                margin-top: 2.4rem;\n                display: flex;\n            }\n        }\n    }\n"])),(function(e){return!e.focused&&(0,v.AH)(Sn||(Sn=(0,m.A)(["\n            // width: 179.2rem;\n            // height: 74.8rem;\n        "])))}),(function(e){return e.imageUrl?"url(".concat(e.imageUrl,")"):"none"}),(function(e){return e.theme.colors.primaryGreen}),(function(e){return e.theme.colors.primaryGreen}),(function(e){return e.theme.colors.primaryGreen}),(function(e){return!e.focused&&(0,v.AH)(Pn||(Pn=(0,m.A)(["\n                background-image: ",";\n            "])),(function(e){return e.imageUrl?"url(".concat(e.imageUrl,")"):"none"}))}),(function(e){return"center"===e.alignment&&(0,v.AH)(In||(In=(0,m.A)(["\n                    justify-content: center;\n                "])))}),(function(e){return"right"===e.alignment&&(0,v.AH)(Mn||(Mn=(0,m.A)(["\n                    justify-content: flex-end;\n                "])))}),(function(e){return"arabic"===e.language&&(0,v.AH)(Zn||(Zn=(0,m.A)(["\n                    text-align: right;\n                "])))}),(function(e){return"arabic"===e.language&&(0,v.AH)(Cn||(Cn=(0,m.A)(["\n                    direction: rtl;\n                "])))}),(function(e){return e.theme.colors.neutralLight}),(function(e){return"arabic"===e.language?"0":"0.3rem"}),(function(e){return"small"===e.titleFontSize&&(0,v.AH)(Nn||(Nn=(0,m.A)(["\n                        font-size: 4rem;\n                    "])))}),(function(e){return"large"===e.titleFontSize&&(0,v.AH)(Hn||(Hn=(0,m.A)(["\n                        font-size: 7.8rem;\n                    "])))}),(function(e){return e.theme.colors.neutralLightLow}),(function(e){return"normal"===e.longFontSize&&(0,v.AH)(Rn||(Rn=(0,m.A)(["\n                        font-size: 2.4rem;\n                    "])))}),(function(e){return"large"===e.longFontSize&&(0,v.AH)(Bn||(Bn=(0,m.A)(["\n                        font-size: 2.8rem;\n                    "])))}));const Tn=function(e){var t,n,r,o,i,a,l,d,f,h,m,v=e.message,p=e.currentIndex,g=e.expand,y=e.direct,w=e.isConsole,E=(0,s.wA)(),x=(0,s.d4)((function(e){return e.motd.selectedMessage})),L=((0,s.d4)((function(e){return e.motd.messages})),(0,s.d4)((function(e){return e.motd.arrowPressed}))),A=(0,s.d4)((function(e){return e.global.isCrossConfirm})),O=(0,s.d4)((function(e){return e.global.isFocused})),k=(0,s.d4)((function(e){return e.global.language})),_=(0,c.useState)(!1),j=(0,b.A)(_,2),S=j[0],P=j[1],I=(0,c.useState)(!1),M=(0,b.A)(I,2),Z=(M[0],M[1],(0,c.useRef)());return c.createElement(Gn,{focused:p===x&&O,imageUrl:null!=v&&null!==(t=v.content)&&void 0!==t&&t.popupImageAdditions&&(null==v||null===(n=v.content)||void 0===n?void 0:n.popupImageAdditions.length)>0?function(e){if(e){if(p!==x)return Z.current&&clearInterval(Z.current),e.content.popupImage;if(p===x&&O){var t,n,r,o,i=null==e||null===(t=e.content)||void 0===t?void 0:t.popupImageAdditions,a=null==e||null===(n=e.display)||void 0===n?void 0:n.cycleTimeOfImage,l=0;if(0===i.length)return;(o=(0,st.A)(i)).unshift(null==e||null===(r=e.content)||void 0===r?void 0:r.popupImage),document.querySelectorAll(".image").length>0&&(document.querySelectorAll(".image")[p].style.backgroundImage="url(".concat(o[0],")")),Z.current&&clearInterval(Z.current),m=setInterval((function(){l>=o.length&&(l=0),document.querySelectorAll(".image")[p].style.backgroundImage="url(".concat(o[l],")"),l++}),a),Z.current=m}}}(v):null==v||null===(r=v.content)||void 0===r?void 0:r.popupImage,alignment:null==v||null===(o=v.display)||void 0===o?void 0:o.motdAlignment,titleFontSize:null==v||null===(i=v.display)||void 0===i?void 0:i.motdTitleFontSize,longFontSize:null==v||null===(a=v.display)||void 0===a?void 0:a.motdLongFontSize,language:k},c.createElement("div",{className:"image"}),c.createElement("div",{className:"content-overlay"},c.createElement("div",{className:"content-container"},c.createElement("div",{className:"content"},(null==v||null===(l=v.content)||void 0===l?void 0:l.title)&&c.createElement("div",{className:"message-title"},v.content.title),(null==v||null===(d=v.content)||void 0===d?void 0:d.contentLong)&&c.createElement("div",{className:"message-body",dangerouslySetInnerHTML:{__html:v.content.contentLong}}),((null==v||null===(f=v.content)||void 0===f?void 0:f.backgroundVideo)||(null==v||null===(h=v.content)||void 0===h?void 0:h.navURL))&&c.createElement("div",{className:"button-bar"},c.createElement("div",{className:w||S||L?"button button-long button-primary":"button button-long button-secondary",onClick:function(){return v.content.backgroundVideo?g():y()},onMouseEnter:function(){w||(P(!0),E(u.Gc.setArrowPressed(!1)))},onMouseLeave:function(){return w?null:P(!1)}},c.createElement(Dt,{type:A?"XBA_PSCROSS":"XBB_PSCIRCLE",width:2,height:2,marginRight:.8,fill:"white"}),c.createElement("div",{className:"button-text"},function(){var e,t,n=null===qt.A||void 0===qt.A||null===(e=qt.A[k])||void 0===e?void 0:e.select;if(v&&v.content)return v.content.backgroundVideo?null===qt.A||void 0===qt.A||null===(t=qt.A[k])||void 0===t?void 0:t.watchVideo:v.content.navURL&&v.data.navMenu&&v.data.navMenu||n}())))))))};function Fn(){Fn=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof h?t:h,a=Object.create(i.prototype),l=new k(o||[]);return r(a,"_invoke",{value:x(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function h(){}function m(){}function v(){}var p={};s(p,i,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(_([])));y&&y!==t&&n.call(y,i)&&(p=y);var w=v.prototype=h.prototype=Object.create(p);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function o(r,i,l,c){var s=d(e[r],e,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==(0,a.A)(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){o("next",e,l,c)}),(function(e){o("throw",e,l,c)})):t.resolve(f).then((function(e){u.value=e,l(u)}),(function(e){return o("throw",e,l,c)}))}c(s.arg)}var i;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){o(e,n,t,r)}))}return i=i?i.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var l=L(a,n);if(l){if(l===f)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function L(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,L(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function _(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:j}}function j(){return{value:void 0,done:!0}}return m.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:m,configurable:!0}),m.displayName=s(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,s(e,c,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(E.prototype),s(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(w),s(w,c,"Generator"),s(w,i,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=_,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:_(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}const zn=function(){var e,t,n=(0,s.wA)(),r=(0,s.d4)((function(e){return e.motd.messages})),o=(0,s.d4)((function(e){return e.motd.selectedMessage})),i=((0,s.d4)((function(e){return e.motd.activeCarousel})),(0,s.d4)((function(e){return e.motd.arrowPressed}))),a=(0,s.d4)((function(e){return e.global.lastInputDevice})),f=(0,s.d4)((function(e){return e.global.lastInputGamepad})),h=(0,s.d4)((function(e){return e.global.isFocused})),m=(0,s.d4)((function(e){return e.global.isCrossConfirm})),v=(0,s.d4)((function(e){return e.global.is4k})),p=(0,c.useState)(1),g=(0,b.A)(p,2),y=g[0],w=g[1],E="undefined"!=typeof Telescope_API_LoadComplete,x=(0,s.d4)((function(e){return e.global.language})),L=1!==a,A=((0,d.lh)("game_id"),(0,c.useState)(!1)),O=(0,b.A)(A,2),k=O[0],_=O[1];(0,c.useEffect)((function(){return"dev"===(0,$t.A)()&&(0,d.Jz)("debug_dump","init",{isTelescope:E}),function(){(0,d.n)(o)}}),[]);var j=function(e){e.tsPage="motd",(0,d.Iu)(e)},S=function(){o!==r.length-1&&(console.log("Flip to next page"),(0,Jt.$)("uin_iw9_bp_blade_move_right"),j({type:"click"}),n(u.Gc.setSelectedMessage(o+1)),(0,d.rK)(o),w(-1))},P=function(){0!==o&&(console.log("Flip to previous page"),(0,Jt.$)("uin_iw9_bp_blade_move_left"),j({type:"click"}),n(u.Gc.setSelectedMessage(o-1)),(0,d.rK)(o),w(1))},I=function(e){window.Telescope_API_ReleaseFocus&&Telescope_API_ReleaseFocus(e)},M=function(){var e=(0,l.A)(Fn().mark((function e(){return Fn().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n(u.Gc.isExpanded(!0)),(0,d.iE)(o),(0,d.n)(o),(0,d.rK)(o,!0),(0,d.mq)(o);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Z=function(){var e,t;return!(null===(e=r[o])||void 0===e||null===(t=e.content)||void 0===t||!t.backgroundVideo)},C=function(){var e,t;return!(null===(e=r[o])||void 0===e||null===(t=e.content)||void 0===t||!t.navURL)},N=function(){var e=(0,l.A)(Fn().mark((function e(){var t,n;return Fn().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(0,d.O7)(),(0,d.iE)(o),(0,d.n)(o),e.next=5,(0,d.rK)(o,!0);case 5:window.Telescope_API_GotoByUri&&Telescope_API_GotoByUri(null===(t=r[o])||void 0===t||null===(n=t.content)||void 0===n?void 0:n.navURL);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),H=function(){var e=(0,l.A)(Fn().mark((function e(){return Fn().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n(u.Gc.setNeedFullScreen(!1)),(0,Jt.$)("ui_select_back"),window.Telescope_API_SetDisplayMode&&Telescope_API_SetDisplayMode(0),Telescope_CB_LoseFocus(),(0,d.H5)(o,r[o].messageId);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,Wt.A)(o,P,S,i&&Z()?M:i&&C()&&N),(0,Kt.A)(1,{LEFT:P,RIGHT:S,LTRIG:function(){return I(4)},UP:function(){return I(3)},XBA_PSCROSS:m?m&&Z()?M:m&&C()&&N:H,XBB_PSCIRCLE:m?H:!m&&Z()?M:!m&&C()&&N}),c.createElement(Vn,{selectedMessage:o,totalMessages:null==r?void 0:r.length,isFocused:h,direction:y,onWheel:function(e){e.deltaY>0?S():e.deltaY<0&&P()},language:x},c.createElement("div",{className:"header header-footer"},c.createElement("h1",{className:"header__title"},null===qt.A||void 0===qt.A||null===(e=qt.A[x])||void 0===e?void 0:e.motdTitle),c.createElement("hr",{className:"divider"})),c.createElement("div",{className:"news-list news-list-translate"},r.map((function(e,t){return c.createElement(Tn,{message:e,currentIndex:t,key:e.messageId,expand:M,direct:N,isConsole:L})}))),c.createElement("div",{className:"footer header-footer"},c.createElement("hr",{className:"divider"}),c.createElement("div",{className:L||k||i?"button button-primary":"button button-secondary",onMouseEnter:function(){return _(!0)},onMouseLeave:function(){return _(!1)},onClick:function(){return H()}},c.createElement(Dt,{type:m?"XBB_PSCIRCLE":"XBA_PSCROSS",width:2,height:2,marginRight:.8,fill:"white"}),c.createElement("div",{className:"button-text"},null===qt.A||void 0===qt.A||null===(t=qt.A[x])||void 0===t?void 0:t.skip))),r.length>1&&c.createElement("div",{className:"carousel-controls"},r.map((function(e,t){return c.createElement("div",{className:"bar ".concat(o===t&&"bar-active"),key:e.messageId})}))),1===a&&c.createElement(c.Fragment,null,0!==o&&c.createElement("div",{className:"arrow arrow-left"},c.createElement(rt,{is4k:v,isCrossConfirm:m,lastInputDevice:a,lastInputGamepad:f,onClick:function(){return P()}})),o!==r.length-1&&c.createElement("div",{className:"arrow arrow-right"},c.createElement(ot,{is4k:v,isCrossConfirm:m,lastInputDevice:a,lastInputGamepad:f,onClick:function(){return S()}}))))};var Un=n(9725);function Xn(){Xn=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof h?t:h,a=Object.create(i.prototype),l=new k(o||[]);return r(a,"_invoke",{value:x(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function h(){}function m(){}function v(){}var p={};s(p,i,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(_([])));y&&y!==t&&n.call(y,i)&&(p=y);var w=v.prototype=h.prototype=Object.create(p);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function o(r,i,l,c){var s=d(e[r],e,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==(0,a.A)(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){o("next",e,l,c)}),(function(e){o("throw",e,l,c)})):t.resolve(f).then((function(e){u.value=e,l(u)}),(function(e){return o("throw",e,l,c)}))}c(s.arg)}var i;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){o(e,n,t,r)}))}return i=i?i.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var l=L(a,n);if(l){if(l===f)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function L(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,L(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function _(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:j}}function j(){return{value:void 0,done:!0}}return m.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:m,configurable:!0}),m.displayName=s(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,s(e,c,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(E.prototype),s(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(w),s(w,c,"Generator"),s(w,i,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=_,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:_(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}function Dn(e){document.querySelector("body");var t=document.createElement("img");return t.src=e,new Promise((function(n,r){setTimeout((function(){return r("Loading ".concat(e," timeout!"))}),5e3),t.addEventListener("load",(function(r){t.style.backgroundImage="url(".concat(e,")"),t=null,n()}))}))}const qn=function(){var e=(0,s.wA)(),t=((0,s.d4)((function(e){return e.motd.isPreload})),(0,s.d4)((function(e){return e.motd.messages}))),n=(0,s.d4)((function(e){return e.motd.isExpanded})),r=(0,s.d4)((function(e){return e.global.authToken})),o=(0,s.d4)((function(e){return e.global.env})),i=(0,s.d4)((function(e){return e.motd.isFirstView})),a=(0,s.d4)((function(e){return e.motd.needFullScreen})),m=(0,s.d4)((function(e){return e.motd.selectedMessage})),v=(0,s.d4)((function(e){return e.global.language})),p=(0,d.lh)("game_id");if("cert"!==o)return i||((0,d.ge)("isFirstView","1"),e(u.Gc.setIsFirstView(!0))),(0,c.useEffect)((function(){var t=function(){var t=(0,l.A)(Xn().mark((function t(){var n,r,o,i;return Xn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e((0,u.TC)());case 3:if(n=t.sent,0!==(r=n.payload).messages.length){t.next=11;break}(0,d.wi)("No messages were returned"),(0,d.eA)(!1),(0,d.Ln)({type:"api_load"}),t.next=19;break;case 11:return t.next=13,Dn(null===(o=r.messages[0])||void 0===o||null===(i=o.content)||void 0===i?void 0:i.popupImage);case 13:(0,d.eA)(!0),(0,d.Ln)({type:"ready"}),r.messages.forEach((function(e){d.zf.push({messageId:e.messageId}),console.log("INTERACTION STATUS ==========",d.zf)})),(0,d.Ek)(0),(0,d.rK)();case 19:t.next=25;break;case 21:t.prev=21,t.t0=t.catch(0),(0,d.wi)("API request failed",t.t0),(0,d.eA)(!1);case 25:case"end":return t.stop()}}),t,null,[[0,21]])})));return function(){return t.apply(this,arguments)}}();(0,d.Ln)({type:"start"}),t(),window.Telescope_CB_NeedFullScreenMotd=function(){return i},window.Telescope_CB_Reset=function(){e(u.Gc.setNeedFullScreen(!1)),e(u.Gc.isExpanded(!1))},window.Telescope_CB_LastInputDeviceUpdated=function(t,n){e(f.f.setLastInputDevice(t)),e(f.f.setLastInputGamepad(n))},window.Telescope_CB_GainFocus=function(t,n,r){e(f.f.setIsFocused(!0))},window.Telescope_CB_LoseFocus=function(){e(f.f.setIsFocused(!1))},window.Telescope_CB_SoundVolumeUpdated=function(e){Un.B.emit("volumeUpdated",e)},window.Telescope_CB_RequestFullScreenMotd=function(){(0,d.wi)("requested fullscreen"),e(u.Gc.setNeedFullScreen(!0))}}),[]),(0,c.useEffect)((function(){window.Telescope_CB_SwitchSleep=function(e){return 1===e?(Un.B.emit("sleep"),(0,d.n)(m),(0,d.rK)(m)):0===e&&(Un.B.emit("wake"),(0,d.Ek)(m)),!0}}),[m]),(0,c.useEffect)((function(){e(f.f.setLastInputDevice((0,d.lh)("last_input_device",!0))),e(f.f.setLastInputGamepad((0,d.lh)("last_input_gamepad",!0)))}),[(0,d.lh)("last_input_device",!0),(0,d.lh)("last_input_gamepad",!0)]),"dev"!==o&&"shaqa"!==o||r?t?c.createElement(h.A,{themeName:p},c.createElement(y,{language:v}),!n&&function(e){return e?c.createElement(zn,null):c.createElement(en,null)}(a),n&&c.createElement(kn,null)):c.createElement("div",null,"No Messages"):c.createElement("div",null)}},2473:(e,t,n)=>{n.d(t,{$:()=>r}),n(436);var r=function(e){"undefined"!=typeof Telescope_API_PlaySound&&Telescope_API_PlaySound(e)}},4212:(e,t,n)=>{e.exports=n.p+"31d6cfe0d16ae931b73c.ttf"}}]);