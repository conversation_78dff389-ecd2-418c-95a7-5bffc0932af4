; ====== DYNAMIC MEMORY OFFSET MANAGEMENT SYSTEM =======
; Advanced Pattern Scanning & Offset Validation for MW3/MWZ
; Handles Game Updates, Version Detection, and Automatic Fallbacks
; Critical Component for Memory-Based Features Safety
; ====== DYNAMIC MEMORY OFFSET MANAGEMENT SYSTEM =======

; ====== OFFSET MANAGEMENT CONFIGURATION =======
global OFFSET_MANAGER_ENABLED := false
global CURRENT_GAME_VERSION := ""
global LAST_OFFSET_VALIDATION := 0
global OFFSET_VALIDATION_INTERVAL := 30000  ; 30 seconds

; Offset Status Tracking
global OFFSETS_VALID := false
global LAST_VALIDATION_RESULT := {}
global OFFSET_FAILURE_COUNT := 0
global MAX_OFFSET_FAILURES := 3

; Game Process Information
global GAME_PROCESS_NAME := ""
global GAME_BASE_ADDRESS := 0
global GAME_MODULE_SIZE := 0
global GAME_BUILD_NUMBER := ""

; ====== MEMORY SIGNATURE DEFINITIONS =======
; Enhanced patterns based on common MW3/MWZ memory structures
; These patterns target common game engine structures
global MEMORY_SIGNATURES := {
    ; Player Data Signatures
    "player_base": {
        pattern: "48 8B 05 ? ? ? ? 48 8B 88 ? ? ? ? 48 85 C9",
        offset: 3,
        relative: true,
        description: "Player base pointer"
    },
    
    ; Entity List Signatures  
    "entity_list": {
        pattern: "48 8B 0D ? ? ? ? 8B 14 81 81 FA ? ? ? ?",
        offset: 3,
        relative: true,
        description: "Entity list base"
    },
    
    ; MWZ Resource Signatures
    "essence_base": {
        pattern: "89 91 ? ? ? ? 8B 81 ? ? ? ? 3B C2 7E",
        offset: 2,
        relative: false,
        description: "Essence storage offset"
    },
    
    "salvage_base": {
        pattern: "8B 91 ? ? ? ? 85 D2 74 ? 8B 81 ? ? ? ?",
        offset: 2,
        relative: false,
        description: "Salvage storage offset"
    },
    
    ; Game State Signatures
    "game_mode": {
        pattern: "83 3D ? ? ? ? ? 75 ? 48 8B 0D ? ? ? ?",
        offset: 2,
        relative: true,
        description: "Current game mode"
    },
    
    ; Health/Position Signatures
    "player_health": {
        pattern: "F3 0F 11 83 ? ? ? ? F3 0F 10 83 ? ? ? ?",
        offset: 4,
        relative: false,
        description: "Player health offset"
    },
    
    "player_position": {
        pattern: "F3 0F 11 86 ? ? ? ? F3 0F 11 8E ? ? ? ?",
        offset: 4,
        relative: false,
        description: "Player position offset"
    }
}

; ====== OFFSET VALIDATION RULES =======
global VALIDATION_RULES := {
    "essence": {
        min_value: 0,
        max_value: 100000,
        data_type: "UInt",
        size: 4,
        description: "MWZ Essence validation"
    },
    
    "salvage": {
        min_value: 0,
        max_value: 999,
        data_type: "UInt", 
        size: 4,
        description: "MWZ Salvage validation"
    },
    
    "health": {
        min_value: 0,
        max_value: 200,
        data_type: "Float",
        size: 4,
        description: "Player health validation"
    },
    
    "position": {
        min_value: -10000,
        max_value: 10000,
        data_type: "Float",
        size: 4,
        description: "Player position validation"
    }
}

; ====== OFFSET MANAGER INITIALIZATION =======
InitializeOffsetManager() {
    global
    
    ; Detect game process and get base information
    if (!DetectGameProcess()) {
        return {success: false, error: "Game process not found"}
    }
    
    ; Get game version and build information
    if (!GetGameVersionInfo()) {
        return {success: false, error: "Could not determine game version"}
    }
    
    ; Load known offsets for this version
    if (!LoadVersionSpecificOffsets()) {
        TrayTip, Offset Manager, No known offsets for this version - will attempt discovery, 3, 2
    }
    
    ; Perform initial offset discovery/validation
    discoveryResult := PerformOffsetDiscovery()
    
    if (discoveryResult.success) {
        OFFSETS_VALID := true
        OFFSET_MANAGER_ENABLED := true
        
        ; Start periodic validation
        SetTimer, ValidateOffsetsTimer, %OFFSET_VALIDATION_INTERVAL%
        
        return {success: true, offsets_found: discoveryResult.offsets_found}
    } else {
        return {success: false, error: discoveryResult.error}
    }
}

DetectGameProcess() {
    global
    
    ; Try different possible process names for MW3/MWZ
    processNames := ["cod.exe", "mw3.exe", "ModernWarfare3.exe", "MW3.exe"]
    
    for index, processName in processNames {
        Process, Exist, %processName%
        if (ErrorLevel) {
            GAME_PROCESS_NAME := processName
            
            ; Get process base address and module size
            if (GetProcessModuleInfo(processName)) {
                return true
            }
        }
    }
    
    return false
}

GetProcessModuleInfo(processName) {
    global
    
    ; Get process handle
    hProcess := DllCall("OpenProcess", "UInt", 0x1F0FFF, "Int", false, "UInt", ErrorLevel, "Ptr")
    if (!hProcess) {
        return false
    }
    
    ; Get module information
    VarSetCapacity(moduleInfo, 24, 0)
    result := DllCall("psapi.dll\GetModuleInformation", "Ptr", hProcess, "Ptr", 0, "Ptr", &moduleInfo, "UInt", 24)
    
    if (result) {
        GAME_BASE_ADDRESS := NumGet(moduleInfo, 0, "Ptr")
        GAME_MODULE_SIZE := NumGet(moduleInfo, 8, "UInt")
    }
    
    DllCall("CloseHandle", "Ptr", hProcess)
    return result != 0
}

GetGameVersionInfo() {
    global
    
    ; Get file version information
    gameExePath := GetProcessPath(GAME_PROCESS_NAME)
    if (!gameExePath) {
        return false
    }
    
    ; Extract version info from file
    VarSetCapacity(versionInfo, 1024, 0)
    versionSize := DllCall("version.dll\GetFileVersionInfoSize", "Str", gameExePath, "UInt*", 0, "UInt")
    
    if (versionSize > 0) {
        VarSetCapacity(versionData, versionSize, 0)
        if (DllCall("version.dll\GetFileVersionInfo", "Str", gameExePath, "UInt", 0, "UInt", versionSize, "Ptr", &versionData)) {
            ; Extract version string
            VarSetCapacity(versionPtr, 8, 0)
            VarSetCapacity(versionLen, 4, 0)
            
            if (DllCall("version.dll\VerQueryValue", "Ptr", &versionData, "Str", "\StringFileInfo\040904B0\FileVersion", "Ptr*", versionPtr, "UInt*", versionLen)) {
                CURRENT_GAME_VERSION := StrGet(NumGet(versionPtr), NumGet(versionLen))
                return true
            }
        }
    }
    
    ; Fallback: Use file timestamp as version identifier
    FileGetTime, fileTime, %gameExePath%, M
    CURRENT_GAME_VERSION := fileTime
    return true
}

GetProcessPath(processName) {
    ; Get full path of running process
    for process in ComObjGet("winmgmts:").ExecQuery("SELECT * FROM Win32_Process WHERE Name = '" . processName . "'") {
        return process.ExecutablePath
    }
    return ""
}

; ====== PATTERN SCANNING IMPLEMENTATION =======
PerformOffsetDiscovery() {
    global
    
    discoveredOffsets := {}
    successCount := 0
    totalSignatures := 0
    
    ; Scan for each signature
    for signatureName, signatureData in MEMORY_SIGNATURES {
        totalSignatures++
        
        scanResult := ScanForPattern(signatureData.pattern, signatureData.offset, signatureData.relative)
        
        if (scanResult.found) {
            discoveredOffsets[signatureName] := scanResult.address
            successCount++
        } else {
            ; Log failure but continue
            LogOffsetFailure(signatureName, "Pattern not found")
        }
    }
    
    ; Update global offsets with discovered values
    if (successCount > 0) {
        UpdateGlobalOffsets(discoveredOffsets)
        
        return {
            success: true,
            offsets_found: successCount,
            total_signatures: totalSignatures,
            success_rate: Round((successCount / totalSignatures) * 100, 1)
        }
    } else {
        return {success: false, error: "No valid signatures found"}
    }
}

ScanForPattern(pattern, patternOffset, isRelative) {
    global
    
    ; Convert pattern string to bytes
    patternBytes := ConvertPatternToBytes(pattern)
    if (!patternBytes) {
        return {found: false, error: "Invalid pattern format"}
    }
    
    ; Scan memory for pattern
    scanAddress := ScanMemoryForBytes(GAME_BASE_ADDRESS, GAME_MODULE_SIZE, patternBytes)
    
    if (scanAddress) {
        ; Calculate final address based on offset and relative flag
        if (isRelative) {
            ; Read relative offset and calculate absolute address
            relativeOffset := SafeReadMemory(scanAddress + patternOffset, 4, "Int")
            finalAddress := scanAddress + patternOffset + 4 + relativeOffset
        } else {
            ; Direct offset from pattern location
            finalAddress := SafeReadMemory(scanAddress + patternOffset, 4, "UInt")
        }
        
        return {found: true, address: finalAddress, scan_address: scanAddress}
    } else {
        return {found: false, error: "Pattern not found in memory"}
    }
}

ConvertPatternToBytes(pattern) {
    ; Convert IDA-style pattern (e.g., "48 8B 05 ? ? ? ?") to byte array
    bytes := []
    masks := []
    
    patternParts := StrSplit(pattern, " ")
    
    for index, part in patternParts {
        if (part = "?") {
            bytes.Push(0)
            masks.Push(false)  ; Wildcard
        } else {
            ; Convert hex string to number
            byteValue := "0x" . part
            bytes.Push(byteValue + 0)
            masks.Push(true)   ; Exact match
        }
    }
    
    return {bytes: bytes, masks: masks}
}

ScanMemoryForBytes(startAddress, size, patternData) {
    ; Scan memory region for byte pattern with wildcards
    bytes := patternData.bytes
    masks := patternData.masks
    patternLength := bytes.Length()
    
    ; Read memory region in chunks for efficiency
    chunkSize := 4096
    currentAddress := startAddress
    endAddress := startAddress + size - patternLength
    
    while (currentAddress < endAddress) {
        ; Read chunk of memory
        readSize := Min(chunkSize, endAddress - currentAddress)
        VarSetCapacity(memoryChunk, readSize, 0)
        
        if (DllCall("ReadProcessMemory", "Ptr", MW3_PROCESS_HANDLE, "Ptr", currentAddress, "Ptr", &memoryChunk, "UPtr", readSize, "Ptr", 0)) {
            ; Search for pattern in this chunk
            Loop, % readSize - patternLength + 1 {
                matchFound := true
                
                ; Check each byte in pattern
                Loop, %patternLength% {
                    if (masks[A_Index]) {  ; Not a wildcard
                        memoryByte := NumGet(memoryChunk, A_Index - 1 + A_Index - 1, "UChar")
                        if (memoryByte != bytes[A_Index]) {
                            matchFound := false
                            break
                        }
                    }
                }
                
                if (matchFound) {
                    return currentAddress + A_Index - 1
                }
            }
        }
        
        currentAddress += chunkSize - patternLength + 1
    }
    
    return 0  ; Pattern not found
}

; ====== OFFSET VALIDATION SYSTEM =======
ValidateCurrentOffsets() {
    global
    
    validationResults := {}
    overallValid := true
    
    ; Validate essence offset
    if (ESSENCE_BASE_ADDRESS) {
        essenceResult := ValidateMemoryValue(ESSENCE_BASE_ADDRESS, VALIDATION_RULES["essence"])
        validationResults["essence"] := essenceResult
        if (!essenceResult.valid) overallValid := false
    }
    
    ; Validate salvage offsets
    if (SALVAGE_BASE_ADDRESS) {
        salvageResult := ValidateMemoryValue(SALVAGE_BASE_ADDRESS, VALIDATION_RULES["salvage"])
        validationResults["salvage"] := salvageResult
        if (!salvageResult.valid) overallValid := false
    }
    
    ; Validate player health
    if (PLAYER_HEALTH_OFFSET) {
        healthResult := ValidateMemoryValue(GAME_BASE_ADDRESS + PLAYER_HEALTH_OFFSET, VALIDATION_RULES["health"])
        validationResults["health"] := healthResult
        if (!healthResult.valid) overallValid := false
    }
    
    ; Update global validation status
    OFFSETS_VALID := overallValid
    LAST_VALIDATION_RESULT := validationResults
    LAST_OFFSET_VALIDATION := A_TickCount
    
    if (!overallValid) {
        OFFSET_FAILURE_COUNT++
        
        if (OFFSET_FAILURE_COUNT >= MAX_OFFSET_FAILURES) {
            ; Too many failures - disable memory features
            DisableMemoryFeatures()
            TrayTip, Offset Manager, Memory offsets invalid - disabling memory features, 5, 2
        }
    } else {
        OFFSET_FAILURE_COUNT := 0  ; Reset failure count on success
    }
    
    return {valid: overallValid, results: validationResults}
}

ValidateMemoryValue(address, validationRule) {
    ; Read value from memory
    value := SafeReadMemory(address, validationRule.size, validationRule.data_type)
    
    if (value == "") {
        return {valid: false, error: "Failed to read memory", address: address}
    }
    
    ; Check if value is within expected range
    if (value < validationRule.min_value || value > validationRule.max_value) {
        return {
            valid: false, 
            error: "Value out of range", 
            value: value, 
            expected_range: validationRule.min_value . "-" . validationRule.max_value,
            address: address
        }
    }
    
    return {valid: true, value: value, address: address}
}

; ====== AUTOMATIC FALLBACK SYSTEM =======
DisableMemoryFeatures() {
    global
    
    ; Disable memory-based features safely
    MemoryTargetingEnabled := false
    ESSENCE_MODIFICATION_ENABLED := false
    SALVAGE_MODIFICATION_ENABLED := false
    RESOURCE_ENGINE_ENABLED := false
    
    ; Update GUI to reflect disabled state
    try {
        GuiControl,, MemoryTargetingBox, 0
        GuiControl,, ResourceEngineBox, 0
        GuiControl,, EssenceModBox, 0
        GuiControl,, SalvageModBox, 0
    } catch e {
        ; GUI not available
    }
    
    ; Enable fallback systems
    ColorFallbackEnabled := true
    AITargetingEnabled := true  ; Prefer AI vision if available
    
    TrayTip, Memory System, Switched to safe fallback modes - AI Vision + Color Detection, 3, 1
}

EnableMemoryFeatures() {
    global
    
    ; Re-enable memory features after successful validation
    if (OFFSETS_VALID) {
        ; Only re-enable if user had them enabled before
        ; This preserves user preferences
        
        TrayTip, Memory System, Memory offsets validated - memory features available, 3, 1
    }
}

; ====== VERSION-SPECIFIC OFFSET MANAGEMENT =======
LoadVersionSpecificOffsets() {
    global
    
    ; Load known good offsets for specific game versions
    ; This would be populated from a database of known working offsets
    
    knownOffsets := GetKnownOffsetsForVersion(CURRENT_GAME_VERSION)
    
    if (knownOffsets) {
        ; Apply known offsets
        ESSENCE_BASE_OFFSET := knownOffsets.essence_base
        SALVAGE_BASE_OFFSET := knownOffsets.salvage_base
        PLAYER_HEALTH_OFFSET := knownOffsets.player_health
        ENTITY_LIST_OFFSET := knownOffsets.entity_list
        
        return true
    }
    
    return false
}

GetKnownOffsetsForVersion(version) {
    ; Database of known working offsets for different game versions
    ; In a real implementation, this would be loaded from an external file
    
    static offsetDatabase := {
        "*******": {
            essence_base: 0x1A2B3C40,
            salvage_base: 0x1A2B3C48,
            player_health: 0x2D40,
            entity_list: 0x3E50
        },
        "1.0.1.0": {
            essence_base: 0x1A2B4C40,
            salvage_base: 0x1A2B4C48,
            player_health: 0x2D44,
            entity_list: 0x3E54
        }
    }
    
    return offsetDatabase.HasKey(version) ? offsetDatabase[version] : false
}

; ====== TIMER FUNCTIONS =======
ValidateOffsetsTimer:
    if (!OFFSET_MANAGER_ENABLED) {
        return
    }
    
    ; Perform periodic validation
    ValidateCurrentOffsets()
    
    ; If validation fails repeatedly, attempt rediscovery
    if (OFFSET_FAILURE_COUNT >= 2) {
        AttemptOffsetRediscovery()
    }
return

AttemptOffsetRediscovery() {
    global
    
    TrayTip, Offset Manager, Attempting offset rediscovery..., 2, 1
    
    ; Try to rediscover offsets
    discoveryResult := PerformOffsetDiscovery()
    
    if (discoveryResult.success) {
        OFFSET_FAILURE_COUNT := 0
        OFFSETS_VALID := true
        EnableMemoryFeatures()
        TrayTip, Offset Manager, Offsets rediscovered successfully!, 3, 1
    } else {
        TrayTip, Offset Manager, Offset rediscovery failed - using fallback modes, 3, 2
    }
}

; ====== UTILITY FUNCTIONS =======
LogOffsetFailure(signatureName, error) {
    ; Log offset failures for debugging (without compromising user safety)
    static logFile := A_ScriptDir . "\offset_debug.log"
    
    logEntry := A_Now . " - " . signatureName . ": " . error . "`n"
    FileAppend, %logEntry%, %logFile%
}

UpdateGlobalOffsets(discoveredOffsets) {
    global
    
    ; Update global offset variables with discovered values
    if (discoveredOffsets.HasKey("essence_base")) {
        ESSENCE_BASE_ADDRESS := discoveredOffsets["essence_base"]
    }
    
    if (discoveredOffsets.HasKey("salvage_base")) {
        SALVAGE_BASE_ADDRESS := discoveredOffsets["salvage_base"]
    }
    
    if (discoveredOffsets.HasKey("entity_list")) {
        ENTITY_LIST_BASE := discoveredOffsets["entity_list"]
    }
    
    if (discoveredOffsets.HasKey("player_health")) {
        PLAYER_HEALTH_OFFSET := discoveredOffsets["player_health"] - GAME_BASE_ADDRESS
    }
}

GetOffsetManagerStatus() {
    global
    
    return {
        enabled: OFFSET_MANAGER_ENABLED,
        offsets_valid: OFFSETS_VALID,
        game_version: CURRENT_GAME_VERSION,
        failure_count: OFFSET_FAILURE_COUNT,
        last_validation: LAST_OFFSET_VALIDATION,
        validation_results: LAST_VALIDATION_RESULT
    }
}

; ====== CLEANUP =======
CleanupOffsetManager() {
    global
    
    SetTimer, ValidateOffsetsTimer, Off
    OFFSET_MANAGER_ENABLED := false
    OFFSETS_VALID := false
}
