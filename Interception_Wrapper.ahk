; ====== INTERCEPTION DRIVER WRAPPER =======
; Hardware-level input simulation using Interception driver
; Provides undetectable mouse and keyboard input at driver level
; Version: 1.0 for MW3/MWZ Ultimate Aimbot System

; ====== INTERCEPTION CONSTANTS =======
; Device types
INTERCEPTION_MOUSE := 0
INTERCEPTION_KEYBOARD := 1

; Mouse states
INTERCEPTION_MOUSE_LEFT_BUTTON_DOWN := 0x001
INTERCEPTION_MOUSE_LEFT_BUTTON_UP := 0x002
INTERCEPTION_MOUSE_RIGHT_BUTTON_DOWN := 0x004
INTERCEPTION_MOUSE_RIGHT_BUTTON_UP := 0x008
INTERCEPTION_MOUSE_MIDDLE_BUTTON_DOWN := 0x010
INTERCEPTION_MOUSE_MIDDLE_BUTTON_UP := 0x020
INTERCEPTION_MOUSE_BUTTON_4_DOWN := 0x040
INTERCEPTION_MOUSE_BUTTON_4_UP := 0x080
INTERCEPTION_MOUSE_BUTTON_5_DOWN := 0x100
INTERCEPTION_MOUSE_BUTTON_5_UP := 0x200
INTERCEPTION_MOUSE_WHEEL := 0x400
INTERCEPTION_MOUSE_HWHEEL := 0x800

; Mouse flags
INTERCEPTION_MOUSE_MOVE_RELATIVE := 0x000
INTERCEPTION_MOUSE_MOVE_ABSOLUTE := 0x001
INTERCEPTION_MOUSE_VIRTUAL_DESKTOP := 0x002
INTERCEPTION_MOUSE_ATTRIBUTES_CHANGED := 0x004
INTERCEPTION_MOUSE_MOVE_NOCOALESCE := 0x008
INTERCEPTION_MOUSE_TERMSRV_SRC_SHADOW := 0x100

; Keyboard states
INTERCEPTION_KEY_DOWN := 0x00
INTERCEPTION_KEY_UP := 0x01
INTERCEPTION_KEY_E0 := 0x02
INTERCEPTION_KEY_E1 := 0x04

; ====== INTERCEPTION STRUCTURES =======
; InterceptionMouseStroke structure (12 bytes)
; typedef struct {
;     unsigned short state;     // +0
;     unsigned short flags;     // +2
;     short rolling;            // +4
;     int x;                    // +6
;     int y;                    // +10
;     unsigned int information; // +14
; } InterceptionMouseStroke;

; InterceptionKeyStroke structure (6 bytes)
; typedef struct {
;     unsigned short code;      // +0
;     unsigned short state;     // +2
;     unsigned int information; // +4
; } InterceptionKeyStroke;

; ====== GLOBAL VARIABLES =======
global InterceptionContext := 0
global InterceptionDLL := 0
global MouseDevice := 0
global KeyboardDevice := 0
global InterceptionInitialized := false

; ====== INTERCEPTION INITIALIZATION =======
InitializeInterception() {
    global
    
    ; Load interception.dll
    InterceptionDLL := DllCall("LoadLibrary", "Str", "interception.dll", "Ptr")
    if (!InterceptionDLL) {
        MsgBox, 0x10, Interception Error, 
        (
        Failed to load interception.dll!
        
        Please ensure:
        1. interception.dll is in the same folder as this script
        2. Interception driver is properly installed
        3. Running with administrator privileges
        
        Download from: https://github.com/oblitum/Interception
        )
        return false
    }
    
    ; Create interception context
    InterceptionContext := DllCall("interception.dll\interception_create_context", "Ptr")
    if (!InterceptionContext) {
        MsgBox, 0x10, Interception Error, Failed to create interception context!`nMake sure the Interception driver is installed.
        return false
    }
    
    ; Set device filter for all mice and keyboards
    DllCall("interception.dll\interception_set_filter", "Ptr", InterceptionContext, 
            "UShort", 1, "UShort", 0xFFFF)  ; All mice
    DllCall("interception.dll\interception_set_filter", "Ptr", InterceptionContext, 
            "UShort", 0, "UShort", 0xFFFF)  ; All keyboards
    
    ; Get first available mouse and keyboard devices
    MouseDevice := GetFirstDevice(INTERCEPTION_MOUSE)
    KeyboardDevice := GetFirstDevice(INTERCEPTION_KEYBOARD)
    
    if (MouseDevice = 0 || KeyboardDevice = 0) {
        MsgBox, 0x10, Interception Error, Failed to find mouse or keyboard devices!
        return false
    }
    
    InterceptionInitialized := true
    return true
}

GetFirstDevice(deviceType) {
    global
    ; Scan for first available device of specified type
    Loop, 20 {  ; Check first 20 device slots
        device := A_Index - 1
        if (DllCall("interception.dll\interception_is_mouse", "UShort", device) && deviceType = INTERCEPTION_MOUSE) {
            return device
        }
        if (DllCall("interception.dll\interception_is_keyboard", "UShort", device) && deviceType = INTERCEPTION_KEYBOARD) {
            return device
        }
    }
    return 0
}

; ====== HARDWARE-LEVEL MOUSE FUNCTIONS =======
InterceptionMouseMove(deltaX, deltaY) {
    global
    if (!InterceptionInitialized)
        return false
    
    ; Create mouse stroke structure (18 bytes total)
    VarSetCapacity(mouseStroke, 18, 0)
    
    ; Fill mouse stroke structure
    NumPut(0, mouseStroke, 0, "UShort")                    ; state = 0 (no buttons)
    NumPut(INTERCEPTION_MOUSE_MOVE_RELATIVE, mouseStroke, 2, "UShort")  ; flags = relative movement
    NumPut(0, mouseStroke, 4, "Short")                     ; rolling = 0 (no wheel)
    NumPut(deltaX, mouseStroke, 6, "Int")                  ; x delta
    NumPut(deltaY, mouseStroke, 10, "Int")                 ; y delta
    NumPut(0, mouseStroke, 14, "UInt")                     ; information = 0
    
    ; Send mouse stroke through driver
    result := DllCall("interception.dll\interception_send", "Ptr", InterceptionContext, 
                     "UShort", MouseDevice, "Ptr", &mouseStroke, "UInt", 1)
    
    return result > 0
}

InterceptionMouseClick(button := "left", action := "click") {
    global
    if (!InterceptionInitialized)
        return false
    
    ; Determine button states
    if (button = "left") {
        downState := INTERCEPTION_MOUSE_LEFT_BUTTON_DOWN
        upState := INTERCEPTION_MOUSE_LEFT_BUTTON_UP
    } else if (button = "right") {
        downState := INTERCEPTION_MOUSE_RIGHT_BUTTON_DOWN
        upState := INTERCEPTION_MOUSE_RIGHT_BUTTON_UP
    } else if (button = "middle") {
        downState := INTERCEPTION_MOUSE_MIDDLE_BUTTON_DOWN
        upState := INTERCEPTION_MOUSE_MIDDLE_BUTTON_UP
    } else {
        return false
    }
    
    ; Create mouse stroke structure
    VarSetCapacity(mouseStroke, 18, 0)
    NumPut(0, mouseStroke, 2, "UShort")  ; flags = 0
    NumPut(0, mouseStroke, 4, "Short")   ; rolling = 0
    NumPut(0, mouseStroke, 6, "Int")     ; x = 0
    NumPut(0, mouseStroke, 10, "Int")    ; y = 0
    NumPut(0, mouseStroke, 14, "UInt")   ; information = 0
    
    if (action = "down" || action = "click") {
        ; Send button down
        NumPut(downState, mouseStroke, 0, "UShort")
        DllCall("interception.dll\interception_send", "Ptr", InterceptionContext, 
               "UShort", MouseDevice, "Ptr", &mouseStroke, "UInt", 1)
    }
    
    if (action = "click") {
        ; Add small delay for realistic click timing
        Sleep, 10
    }
    
    if (action = "up" || action = "click") {
        ; Send button up
        NumPut(upState, mouseStroke, 0, "UShort")
        DllCall("interception.dll\interception_send", "Ptr", InterceptionContext, 
               "UShort", MouseDevice, "Ptr", &mouseStroke, "UInt", 1)
    }
    
    return true
}

InterceptionMouseWheel(direction, clicks := 1) {
    global
    if (!InterceptionInitialized)
        return false
    
    wheelDelta := (direction > 0) ? 120 : -120
    wheelDelta *= clicks
    
    ; Create mouse stroke structure
    VarSetCapacity(mouseStroke, 18, 0)
    NumPut(INTERCEPTION_MOUSE_WHEEL, mouseStroke, 0, "UShort")  ; state = wheel
    NumPut(0, mouseStroke, 2, "UShort")                         ; flags = 0
    NumPut(wheelDelta, mouseStroke, 4, "Short")                 ; rolling = wheel delta
    NumPut(0, mouseStroke, 6, "Int")                            ; x = 0
    NumPut(0, mouseStroke, 10, "Int")                           ; y = 0
    NumPut(0, mouseStroke, 14, "UInt")                          ; information = 0
    
    result := DllCall("interception.dll\interception_send", "Ptr", InterceptionContext, 
                     "UShort", MouseDevice, "Ptr", &mouseStroke, "UInt", 1)
    
    return result > 0
}

; ====== HARDWARE-LEVEL KEYBOARD FUNCTIONS =======
InterceptionKeyPress(scanCode, action := "press") {
    global
    if (!InterceptionInitialized)
        return false
    
    ; Create keyboard stroke structure (8 bytes)
    VarSetCapacity(keyStroke, 8, 0)
    NumPut(scanCode, keyStroke, 0, "UShort")  ; scan code
    NumPut(0, keyStroke, 4, "UInt")           ; information = 0
    
    if (action = "down" || action = "press") {
        ; Send key down
        NumPut(INTERCEPTION_KEY_DOWN, keyStroke, 2, "UShort")
        DllCall("interception.dll\interception_send", "Ptr", InterceptionContext, 
               "UShort", KeyboardDevice, "Ptr", &keyStroke, "UInt", 1)
    }
    
    if (action = "press") {
        ; Add small delay for realistic key press timing
        Sleep, 15
    }
    
    if (action = "up" || action = "press") {
        ; Send key up
        NumPut(INTERCEPTION_KEY_UP, keyStroke, 2, "UShort")
        DllCall("interception.dll\interception_send", "Ptr", InterceptionContext, 
               "UShort", KeyboardDevice, "Ptr", &keyStroke, "UInt", 1)
    }
    
    return true
}

; Common key scan codes
GetScanCode(key) {
    static scanCodes := {  "Space": 0x39, "W": 0x11, "A": 0x1E, "S": 0x1F, "D": 0x20
                        , "Shift": 0x2A, "Ctrl": 0x1D, "Alt": 0x38, "Tab": 0x0F
                        , "Enter": 0x1C, "Esc": 0x01, "F1": 0x3B, "F2": 0x3C
                        , "F3": 0x3D, "F4": 0x3E, "F5": 0x3F, "F6": 0x40
                        , "F7": 0x41, "F8": 0x42, "F9": 0x43, "F10": 0x44
                        , "F11": 0x57, "F12": 0x58, "1": 0x02, "2": 0x03
                        , "3": 0x04, "4": 0x05, "5": 0x06, "6": 0x07
                        , "7": 0x08, "8": 0x09, "9": 0x0A, "0": 0x0B }
    
    return scanCodes.HasKey(key) ? scanCodes[key] : 0
}

; ====== CLEANUP FUNCTIONS =======
CleanupInterception() {
    global
    if (InterceptionContext) {
        DllCall("interception.dll\interception_destroy_context", "Ptr", InterceptionContext)
        InterceptionContext := 0
    }
    if (InterceptionDLL) {
        DllCall("FreeLibrary", "Ptr", InterceptionDLL)
        InterceptionDLL := 0
    }
    InterceptionInitialized := false
}

; ====== ENHANCED INPUT FUNCTIONS =======
; These functions provide the same interface as standard AHK but use hardware-level injection

HardwareMouseMove(deltaX, deltaY) {
    ; Enhanced mouse movement with natural acceleration curves
    if (!InterceptionInitialized) {
        ; Fallback to standard method if Interception not available
        DllCall("mouse_event", "uint", 1, "int", deltaX, "int", deltaY, "uint", 0, "int", 0)
        return
    }
    
    ; Apply natural mouse acceleration
    distance := Sqrt(deltaX**2 + deltaY**2)
    if (distance > 50) {
        ; Large movements get slight acceleration
        deltaX *= 1.1
        deltaY *= 1.1
    } else if (distance < 5) {
        ; Very small movements get precision enhancement
        deltaX *= 0.9
        deltaY *= 0.9
    }
    
    ; Split large movements into smaller chunks for realism
    if (Abs(deltaX) > 20 || Abs(deltaY) > 20) {
        steps := Max(Abs(deltaX), Abs(deltaY)) // 15
        stepX := deltaX / steps
        stepY := deltaY / steps
        
        Loop, %steps% {
            InterceptionMouseMove(Round(stepX), Round(stepY))
            Sleep, 1  ; Tiny delay between steps
        }
    } else {
        InterceptionMouseMove(deltaX, deltaY)
    }
}

HardwareClick(button := "left", action := "click") {
    if (!InterceptionInitialized) {
        ; Fallback to standard method
        Click, %button%, , , 1, 0, %action%
        return
    }
    
    ; Add slight randomization to click timing
    Random, clickDelay, 8, 18
    
    if (action = "click") {
        InterceptionMouseClick(button, "down")
        Sleep, clickDelay
        InterceptionMouseClick(button, "up")
    } else {
        InterceptionMouseClick(button, action)
    }
}

HardwareKeyPress(key, action := "press") {
    scanCode := GetScanCode(key)
    if (!scanCode || !InterceptionInitialized) {
        ; Fallback to standard method
        if (action = "press")
            Send, {%key%}
        else if (action = "down")
            Send, {%key% down}
        else if (action = "up")
            Send, {%key% up}
        return
    }
    
    InterceptionKeyPress(scanCode, action)
}
