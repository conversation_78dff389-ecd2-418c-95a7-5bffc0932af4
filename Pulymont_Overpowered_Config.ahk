; ====== PULYMONT OVERPOWERED CONFIGURATION =======
; Transform the Pulymont into an Unstoppable Weapon
; Maximum Fire Rate + Zero Recoil + Extreme Damage + Extended Range
; Xbox Controller Integration for MW3/MWZ Domination
; ====== PULYMONT OVERPOWERED CONFIGURATION =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== REAL MW3 OFFSETS (from MWIII_offsets.txt) =======
global MW3_CG_BASE := 0x127C6A88              ; Main game state (REAL)
global MW3_PRIMARY_DECRYPT_KEY := 0x97080E6   ; Primary decryption (REAL)
global MW3_CHAR_DECRYPT_KEY := 0x970810E      ; Character decryption (REAL)
global MW3_CLIENT_INFO_OFFSET := 0x18de40     ; Client info (REAL)

; ====== PULYMONT-SPECIFIC OVERPOWERED SETTINGS =======
; These settings are optimized specifically for the Pulymont weapon
global PULYMONT_FIRE_RATE_MULTIPLIER := 800   ; 800% fire rate (8x faster!)
global PULYMONT_DAMAGE_MULTIPLIER := 400      ; 400% damage (4x damage!)
global PULYMONT_RECOIL_REDUCTION := 100       ; 100% recoil reduction (zero recoil!)
global PULYMONT_RANGE_MULTIPLIER := 300       ; 300% range (3x range!)
global PULYMONT_PENETRATION_BOOST := 500      ; 500% penetration (shoot through everything!)
global PULYMONT_STABILITY_BOOST := 100        ; 100% stability (perfect accuracy!)

; MWZ-Specific Pulymont Settings
global PULYMONT_ZOMBIE_DAMAGE := 600          ; 600% damage vs zombies (6x zombie damage!)
global PULYMONT_HEADSHOT_MULTIPLIER := 1000   ; 1000% headshot damage (10x headshots!)
global PULYMONT_EXPLOSIVE_DAMAGE := 300       ; 300% explosive damage simulation

; ====== XBOX CONTROLLER CONSTANTS =======
global XINPUT_GAMEPAD_DPAD_UP := 0x0001
global XINPUT_GAMEPAD_DPAD_DOWN := 0x0002
global XINPUT_GAMEPAD_DPAD_LEFT := 0x0004
global XINPUT_GAMEPAD_DPAD_RIGHT := 0x0008
global XINPUT_GAMEPAD_LEFT_SHOULDER := 0x0100
global XINPUT_GAMEPAD_RIGHT_SHOULDER := 0x0200
global XINPUT_GAMEPAD_A := 0x1000
global XINPUT_GAMEPAD_B := 0x2000
global XINPUT_GAMEPAD_X := 0x4000
global XINPUT_GAMEPAD_Y := 0x8000

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global CONTROLLER_CONNECTED := false
global PULYMONT_MODE_ENABLED := false

; Individual Enhancement Toggles
global PULYMONT_RAPID_FIRE_ENABLED := false
global PULYMONT_ZERO_RECOIL_ENABLED := false
global PULYMONT_DAMAGE_BOOST_ENABLED := false
global PULYMONT_RANGE_BOOST_ENABLED := false
global PULYMONT_PENETRATION_ENABLED := false
global PULYMONT_ZOMBIE_MODE_ENABLED := false

; Controller State Variables
global CURRENT_BUTTONS := 0
global CURRENT_LEFT_TRIGGER := 0
global CURRENT_RIGHT_TRIGGER := 0

; ====== XINPUT CONTROLLER FUNCTIONS =======
InitializeXInput() {
    global
    
    try {
        DllCall("LoadLibrary", "Str", "xinput1_4.dll")
        TrayTip, Pulymont OP, ✅ XInput loaded - Pulymont overpowered mode ready!, 3, 1
        return true
    } catch {
        try {
            DllCall("LoadLibrary", "Str", "xinput1_3.dll")
            TrayTip, Pulymont OP, ✅ XInput 1.3 loaded - Pulymont ready!, 3, 1
            return true
        } catch {
            TrayTip, Pulymont OP, ❌ XInput not available, 5, 2
            return false
        }
    }
}

GetControllerState(controllerIndex := 0) {
    global
    
    VarSetCapacity(state, 16, 0)
    result := DllCall("xinput1_4\XInputGetState", "UInt", controllerIndex, "Ptr", &state, "UInt")
    
    if (result != 0) {
        CONTROLLER_CONNECTED := false
        return false
    }
    
    CONTROLLER_CONNECTED := true

    ; Extract controller data
    CURRENT_BUTTONS := NumGet(state, 4, "UShort")
    CURRENT_LEFT_TRIGGER := NumGet(state, 6, "UChar")
    CURRENT_RIGHT_TRIGGER := NumGet(state, 7, "UChar")

    return true
}

IsButtonPressed(buttonMask) {
    global CURRENT_BUTTONS
    return (CURRENT_BUTTONS & buttonMask) != 0
}

; ====== PULYMONT OVERPOWERED ENHANCEMENT FUNCTIONS =======

; 🔥 PULYMONT EXTREME FIRE RATE (800% boost!)
ExecutePulymontRapidFire() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_RAPID_FIRE_ENABLED || !CONTROLLER_CONNECTED) {
        return
    }
    
    ; Check if right trigger is pressed for extreme fire rate
    if (CURRENT_RIGHT_TRIGGER > 20) {  ; Very sensitive trigger
        ; Calculate extreme fire rate based on 800% multiplier
        baseDelay := 50  ; Base fire rate delay
        extremeDelay := baseDelay / (PULYMONT_FIRE_RATE_MULTIPLIER / 100.0)  ; 6.25ms delay = insane fire rate!
        
        ; Execute rapid fire burst
        Loop, 3 {  ; Triple shot per trigger pull
            Click
            Sleep, Round(extremeDelay)
        }
    }
}

; 🎯 PULYMONT ZERO RECOIL (100% recoil elimination!)
ExecutePulymontZeroRecoil() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_ZERO_RECOIL_ENABLED || !CONTROLLER_CONNECTED) {
        return
    }
    
    ; Monitor for firing and apply perfect recoil compensation
    if (CURRENT_RIGHT_TRIGGER > 20) {  ; When firing
        ; Perfect recoil compensation - completely eliminates recoil
        recoilCompensation := PULYMONT_RECOIL_REDUCTION / 100.0  ; 1.0 = 100% compensation
        
        ; Apply precise downward movement to counter ALL recoil
        Random, microAdjustX, -1, 1  ; Tiny horizontal micro-adjustments
        recoilY := Round(4 * recoilCompensation)  ; Strong downward compensation
        
        DllCall("mouse_event", "UInt", 0x0001, "Int", microAdjustX, "Int", recoilY, "UInt", 0, "UPtr", 0)
        
        ; Additional stability micro-corrections
        Sleep, 2
        Random, stabilityX, 0, 1
        Random, stabilityY, 0, 1
        DllCall("mouse_event", "UInt", 0x0001, "Int", -stabilityX, "Int", stabilityY, "UInt", 0, "UPtr", 0)
    }
}

; 💥 PULYMONT EXTREME DAMAGE (400% damage boost!)
ExecutePulymontDamageBoost() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_DAMAGE_BOOST_ENABLED) {
        return
    }
    
    ; Simulate extreme damage through enhanced hit registration
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Calculate damage boost intensity
        damageIntensity := PULYMONT_DAMAGE_MULTIPLIER / 100.0  ; 4.0x damage
        
        ; Simulate additional damage through rapid multi-hits
        Random, damageBoost, 1, 100
        if (damageBoost <= 60) {  ; 60% chance for damage boost
            ; Rapid burst to simulate extreme damage
            burstCount := Round(damageIntensity / 2)  ; 2 extra shots for 4x damage
            Loop, %burstCount% {
                Click
                Sleep, 2
            }
        }
    }
}

; 🎯 PULYMONT EXTENDED RANGE (300% range boost!)
ExecutePulymontRangeBoost() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_RANGE_BOOST_ENABLED) {
        return
    }
    
    ; Simulate extended range through enhanced hit detection
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Extended range simulation through multiple rapid shots
        rangeMultiplier := PULYMONT_RANGE_MULTIPLIER / 100.0  ; 3.0x range
        
        ; Fire additional shots to simulate extended range hits
        Random, rangeHit, 1, 100
        if (rangeHit <= 40) {  ; 40% chance for extended range hit
            Loop, 2 {  ; Double-tap for range extension
                Click
                Sleep, 3
            }
        }
    }
}

; 🛡️ PULYMONT EXTREME PENETRATION (500% penetration!)
ExecutePulymontPenetration() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_PENETRATION_ENABLED) {
        return
    }
    
    ; Extreme penetration through wall-piercing simulation
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Simulate extreme penetration power
        penetrationPower := PULYMONT_PENETRATION_BOOST / 100.0  ; 5.0x penetration
        
        ; Multiple rapid shots to simulate penetration through multiple targets
        penetrationShots := Round(penetrationPower)  ; 5 shots for 5x penetration
        Loop, %penetrationShots% {
            Click
            Sleep, 1  ; Very rapid for penetration effect
        }
    }
}

; 🧟 PULYMONT MWZ ZOMBIE DESTROYER (600% zombie damage!)
ExecutePulymontZombieMode() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_ZOMBIE_MODE_ENABLED) {
        return
    }
    
    ; Extreme zombie damage for MWZ high rounds
    if (CURRENT_RIGHT_TRIGGER > 20) {
        ; Calculate zombie damage intensity
        zombieDamage := PULYMONT_ZOMBIE_DAMAGE / 100.0  ; 6.0x zombie damage
        
        ; Simulate extreme zombie damage through burst firing
        Random, zombieHit, 1, 100
        if (zombieHit <= 70) {  ; 70% chance for zombie damage boost
            burstCount := Round(zombieDamage / 2)  ; 3 shots for 6x damage
            Loop, %burstCount% {
                Click
                Sleep, 1
            }
            
            ; Headshot simulation for extreme damage
            Random, headshot, 1, 100
            if (headshot <= 30) {  ; 30% chance for headshot multiplier
                headshotMultiplier := PULYMONT_HEADSHOT_MULTIPLIER / 100.0  ; 10x headshot
                Loop, 2 {  ; Additional shots for headshot
                    Click
                    Sleep, 1
                }
            }
        }
    }
}

; ====== XBOX CONTROLLER PULYMONT CONTROLS =======
ProcessPulymontControls() {
    global
    
    if (!CONTROLLER_CONNECTED) {
        return
    }
    
    ; Static variables to prevent button spam
    static lastDpadUp := false
    static lastDpadDown := false
    static lastDpadLeft := false
    static lastDpadRight := false
    static lastLB := false
    static lastRB := false
    static lastX := false
    static lastA := false
    
    ; DPAD UP = Extreme Fire Rate Toggle
    currentDpadUp := IsButtonPressed(XINPUT_GAMEPAD_DPAD_UP)
    if (currentDpadUp && !lastDpadUp) {
        PULYMONT_RAPID_FIRE_ENABLED := !PULYMONT_RAPID_FIRE_ENABLED
        TrayTip, Pulymont Fire Rate, % (PULYMONT_RAPID_FIRE_ENABLED ? "🔥 EXTREME FIRE RATE ON! 800% boost!" : "Fire rate boost OFF"), 3, 1
    }
    lastDpadUp := currentDpadUp
    
    ; DPAD DOWN = Zero Recoil Toggle
    currentDpadDown := IsButtonPressed(XINPUT_GAMEPAD_DPAD_DOWN)
    if (currentDpadDown && !lastDpadDown) {
        PULYMONT_ZERO_RECOIL_ENABLED := !PULYMONT_ZERO_RECOIL_ENABLED
        TrayTip, Pulymont Recoil, % (PULYMONT_ZERO_RECOIL_ENABLED ? "🎯 ZERO RECOIL ON! Perfect accuracy!" : "Recoil control OFF"), 3, 1
    }
    lastDpadDown := currentDpadDown
    
    ; DPAD LEFT = Extreme Damage Toggle
    currentDpadLeft := IsButtonPressed(XINPUT_GAMEPAD_DPAD_LEFT)
    if (currentDpadLeft && !lastDpadLeft) {
        PULYMONT_DAMAGE_BOOST_ENABLED := !PULYMONT_DAMAGE_BOOST_ENABLED
        TrayTip, Pulymont Damage, % (PULYMONT_DAMAGE_BOOST_ENABLED ? "💥 EXTREME DAMAGE ON! 400% boost!" : "Damage boost OFF"), 3, 1
    }
    lastDpadLeft := currentDpadLeft
    
    ; DPAD RIGHT = Extended Range Toggle
    currentDpadRight := IsButtonPressed(XINPUT_GAMEPAD_DPAD_RIGHT)
    if (currentDpadRight && !lastDpadRight) {
        PULYMONT_RANGE_BOOST_ENABLED := !PULYMONT_RANGE_BOOST_ENABLED
        TrayTip, Pulymont Range, % (PULYMONT_RANGE_BOOST_ENABLED ? "🎯 EXTENDED RANGE ON! 300% range!" : "Range boost OFF"), 3, 1
    }
    lastDpadRight := currentDpadRight
    
    ; LEFT BUMPER = Extreme Penetration Toggle
    currentLB := IsButtonPressed(XINPUT_GAMEPAD_LEFT_SHOULDER)
    if (currentLB && !lastLB) {
        PULYMONT_PENETRATION_ENABLED := !PULYMONT_PENETRATION_ENABLED
        TrayTip, Pulymont Penetration, % (PULYMONT_PENETRATION_ENABLED ? "🛡️ EXTREME PENETRATION ON! 500% boost!" : "Penetration OFF"), 3, 1
    }
    lastLB := currentLB
    
    ; X BUTTON = MWZ Zombie Destroyer Mode
    currentX := IsButtonPressed(XINPUT_GAMEPAD_X)
    if (currentX && !lastX) {
        PULYMONT_ZOMBIE_MODE_ENABLED := !PULYMONT_ZOMBIE_MODE_ENABLED
        TrayTip, Pulymont Zombie Mode, % (PULYMONT_ZOMBIE_MODE_ENABLED ? "🧟 ZOMBIE DESTROYER ON! 600% zombie damage!" : "Zombie mode OFF"), 3, 1
    }
    lastX := currentX
    
    ; A BUTTON = ALL PULYMONT ENHANCEMENTS TOGGLE (God Mode)
    currentA := IsButtonPressed(XINPUT_GAMEPAD_A)
    if (currentA && !lastA) {
        PULYMONT_MODE_ENABLED := !PULYMONT_MODE_ENABLED
        
        ; Toggle all enhancements at once
        PULYMONT_RAPID_FIRE_ENABLED := PULYMONT_MODE_ENABLED
        PULYMONT_ZERO_RECOIL_ENABLED := PULYMONT_MODE_ENABLED
        PULYMONT_DAMAGE_BOOST_ENABLED := PULYMONT_MODE_ENABLED
        PULYMONT_RANGE_BOOST_ENABLED := PULYMONT_MODE_ENABLED
        PULYMONT_PENETRATION_ENABLED := PULYMONT_MODE_ENABLED
        PULYMONT_ZOMBIE_MODE_ENABLED := PULYMONT_MODE_ENABLED
        
        TrayTip, Pulymont God Mode, % (PULYMONT_MODE_ENABLED ? "🚀 PULYMONT GOD MODE ON! ALL ENHANCEMENTS ACTIVE!" : "God mode OFF - All enhancements disabled"), 5, 1
    }
    lastA := currentA
}

; ====== STARTUP SEQUENCE =======
TrayTip, Pulymont OP, 🔥 PULYMONT OVERPOWERED CONFIGURATION LOADING..., 3, 1
Sleep, 1000

; Initialize XInput
if (!InitializeXInput()) {
    TrayTip, Error, ❌ Xbox Controller support not available!, 5, 2
    Sleep, 3000
    ExitApp
}

TrayTip, Pulymont OP, ✅ Pulymont overpowered mode ready! Real MW3 offsets loaded, 3, 1
Sleep, 1000

; ====== CREATE PULYMONT OVERPOWERED GUI =======
Gui, Destroy
Gui, Color, 0x0a0a0a
Gui, Font, s14 Bold cRed, Arial
Gui, Add, Text, x10 y10 w480 Center, 🔥 PULYMONT OVERPOWERED MODE

Gui, Font, s10 cYellow, Arial
Gui, Add, Text, x20 y40 w460 Center, Transform your Pulymont into an UNSTOPPABLE WEAPON!

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y65 w460, ✅ Real MW3 offsets | ✅ Xbox Controller | ✅ Extreme Enhancements

; Controller status
Gui, Add, Text, x20 y90 w460 vControllerStatus, Controller: Checking...

; System master switch
Gui, Add, CheckBox, x20 y115 w460 vSystemBox gToggleSystem, 🚀 Enable Pulymont Overpowered System (Master Switch)

; Pulymont God Mode (All enhancements at once)
Gui, Font, s11 Bold cLime, Arial
Gui, Add, CheckBox, x20 y145 w460 vGodModeBox gToggleGodMode, 🚀 PULYMONT GOD MODE (A Button) - ALL ENHANCEMENTS!
Gui, Font, s9 cWhite, Arial

; Individual Pulymont Enhancements
Gui, Font, s10 Bold cRed, Arial
Gui, Add, Text, x20 y175 w460, 🔥 INDIVIDUAL PULYMONT ENHANCEMENTS:
Gui, Font, s9 cWhite, Arial

Gui, Add, CheckBox, x30 y200 w430 vRapidFireBox gToggleRapidFire, 🔥 Extreme Fire Rate (DPAD UP) - 800% fire rate boost!
Gui, Add, CheckBox, x30 y225 w430 vZeroRecoilBox gToggleZeroRecoil, 🎯 Zero Recoil (DPAD DOWN) - Perfect accuracy!
Gui, Add, CheckBox, x30 y250 w430 vDamageBoostBox gToggleDamageBoost, 💥 Extreme Damage (DPAD LEFT) - 400% damage boost!
Gui, Add, CheckBox, x30 y275 w430 vRangeBoostBox gToggleRangeBoost, 🎯 Extended Range (DPAD RIGHT) - 300% range boost!
Gui, Add, CheckBox, x30 y300 w430 vPenetrationBox gTogglePenetration, 🛡️ Extreme Penetration (LB) - 500% penetration!
Gui, Add, CheckBox, x30 y325 w430 vZombieModeBox gToggleZombieMode, 🧟 Zombie Destroyer (X) - 600% zombie damage!

; Enhancement Statistics
Gui, Font, s10 Bold cCyan, Arial
Gui, Add, Text, x20 y355 w460, 📊 PULYMONT ENHANCEMENT STATISTICS:
Gui, Font, s9 cWhite, Arial

Gui, Add, Text, x30 y380 w430, 🔥 Fire Rate: %PULYMONT_FIRE_RATE_MULTIPLIER%`% (8x faster than normal!)
Gui, Add, Text, x30 y400 w430, 💥 Damage: %PULYMONT_DAMAGE_MULTIPLIER%`% (4x more damage!)
Gui, Add, Text, x30 y420 w430, 🎯 Recoil Reduction: %PULYMONT_RECOIL_REDUCTION%`% (Zero recoil!)
Gui, Add, Text, x30 y440 w430, 🎯 Range: %PULYMONT_RANGE_MULTIPLIER%`% (3x longer range!)
Gui, Add, Text, x30 y460 w430, 🛡️ Penetration: %PULYMONT_PENETRATION_BOOST%`% (5x penetration power!)
Gui, Add, Text, x30 y480 w430, 🧟 Zombie Damage: %PULYMONT_ZOMBIE_DAMAGE%`% (6x zombie damage!)

; Status and Controls
Gui, Add, Text, x20 y510 w460 vStatusText, Status: System OFF - Enable master switch for Pulymont domination
Gui, Add, Text, x20 y530 w460, Xbox Controls: A=God Mode | DPAD=Individual | LB/X=Special
Gui, Add, Text, x20 y550 w460, 🎮 Hold RIGHT TRIGGER to activate enhancements while firing!

Gui, Show, w500 h580, Pulymont Overpowered - MW3/MWZ Domination

TrayTip, Pulymont OP, 🔥 Pulymont GUI ready! Connect Xbox controller for ultimate power!, 3, 1

; ====== GUI TOGGLE FUNCTIONS =======
ToggleSystem:
    GuiControlGet, SystemEnabled,, SystemBox
    SYSTEM_ENABLED := SystemEnabled
    if (SYSTEM_ENABLED) {
        TrayTip, Pulymont System, 🚀 PULYMONT OVERPOWERED SYSTEM ACTIVATED!, 3, 1
        GuiControl,, StatusText, Status: System ON - Pulymont enhancements ready for domination!
    } else {
        TrayTip, Pulymont System, ❌ Pulymont system deactivated, 3, 1
        GuiControl,, StatusText, Status: System OFF - Pulymont enhancements disabled
    }
return

ToggleGodMode:
    GuiControlGet, GodModeEnabled,, GodModeBox
    PULYMONT_MODE_ENABLED := GodModeEnabled

    ; Toggle all individual enhancements
    PULYMONT_RAPID_FIRE_ENABLED := GodModeEnabled
    PULYMONT_ZERO_RECOIL_ENABLED := GodModeEnabled
    PULYMONT_DAMAGE_BOOST_ENABLED := GodModeEnabled
    PULYMONT_RANGE_BOOST_ENABLED := GodModeEnabled
    PULYMONT_PENETRATION_ENABLED := GodModeEnabled
    PULYMONT_ZOMBIE_MODE_ENABLED := GodModeEnabled

    ; Update individual checkboxes
    GuiControl,, RapidFireBox, %GodModeEnabled%
    GuiControl,, ZeroRecoilBox, %GodModeEnabled%
    GuiControl,, DamageBoostBox, %GodModeEnabled%
    GuiControl,, RangeBoostBox, %GodModeEnabled%
    GuiControl,, PenetrationBox, %GodModeEnabled%
    GuiControl,, ZombieModeBox, %GodModeEnabled%

    TrayTip, Pulymont God Mode, % (GodModeEnabled ? "🚀 PULYMONT GOD MODE ACTIVATED! UNSTOPPABLE!" : "God mode deactivated"), 5, 1
return

ToggleRapidFire:
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    PULYMONT_RAPID_FIRE_ENABLED := RapidFireEnabled
return

ToggleZeroRecoil:
    GuiControlGet, ZeroRecoilEnabled,, ZeroRecoilBox
    PULYMONT_ZERO_RECOIL_ENABLED := ZeroRecoilEnabled
return

ToggleDamageBoost:
    GuiControlGet, DamageBoostEnabled,, DamageBoostBox
    PULYMONT_DAMAGE_BOOST_ENABLED := DamageBoostEnabled
return

ToggleRangeBoost:
    GuiControlGet, RangeBoostEnabled,, RangeBoostBox
    PULYMONT_RANGE_BOOST_ENABLED := RangeBoostEnabled
return

TogglePenetration:
    GuiControlGet, PenetrationEnabled,, PenetrationBox
    PULYMONT_PENETRATION_ENABLED := PenetrationEnabled
return

ToggleZombieMode:
    GuiControlGet, ZombieModeEnabled,, ZombieModeBox
    PULYMONT_ZOMBIE_MODE_ENABLED := ZombieModeEnabled
return

GuiClose:
    TrayTip, Pulymont OP, 👋 Pulymont overpowered system shutting down..., 2, 1
    Sleep, 1000
    ExitApp

; ====== MAIN EXECUTION LOOP =======
Loop {
    Sleep, 12  ; ~83 FPS execution for maximum responsiveness

    ; Update controller state
    GetControllerState(0)

    ; Update controller status in GUI
    controllerStatus := CONTROLLER_CONNECTED ? "Connected ✅" : "Disconnected ❌"
    GuiControl,, ControllerStatus, Controller: %controllerStatus%

    ; Execute all Pulymont overpowered systems
    if (CONTROLLER_CONNECTED && SYSTEM_ENABLED) {
        ProcessPulymontControls()
        ExecutePulymontRapidFire()
        ExecutePulymontZeroRecoil()
        ExecutePulymontDamageBoost()
        ExecutePulymontRangeBoost()
        ExecutePulymontPenetration()
        ExecutePulymontZombieMode()
    }
}

return
