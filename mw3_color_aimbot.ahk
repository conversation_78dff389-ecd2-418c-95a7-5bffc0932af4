; MW3/MWZ Color-Based Aimbot
; Based on working BO6 techniques from UnknownCheats
; RICOCHET-PROOF External Aimbot

#NoEnv
#SingleInstance Force
#Persistent
CoordMode, Pixel, Screen, RGB
CoordMode, Mouse, Screen
#InstallKeybdHook
#UseHook
SendMode, InputThenPlay
SetBatchLines, -1
ListLines, Off

; ====== ANTI-RICOCHET PROTECTION ======
; Hide from screenshots and screen capture
Gui +LastFound +AlwaysOnTop -Caption +ToolWindow
hWnd := WinExist()
; Method 1: SetWindowDisplayAffinity - Hides from screen capture
DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
; Method 2: Add WS_EX_NOREDIRECTIONBITMAP extended style  
WinSet, ExStyle, +0x00200000, ahk_id %hWnd%

; ====== CONFIGURATION ======
; Enemy nameplate color (MW3/MWZ default red)
EnemyColor := 0xFF0000  ; Red color for MW3/MWZ enemies
ColorVariation := 30    ; Color tolerance
FOVSize := 150         ; Field of view radius
AimStrength := 0.8     ; Aim speed (0.1 = slow, 1.0 = instant)
AimBone := "head"      ; "head" or "chest"
EnablePrediction := true
AntiRecoil := false
RecoilStrength := 2

; Offsets for different aim bones
HeadOffsetY := -10     ; Pixels above detected color
ChestOffsetY := 15     ; Pixels below detected color

; ====== HOTKEYS ======
F1::ToggleAimbot()
F2::ToggleGUI()
F3::ToggleAntiRecoil()
Insert::ToggleGUI()

; ====== VARIABLES ======
AimbotEnabled := true
GUIVisible := true
LastTargetX := 0
LastTargetY := 0
TargetLocked := false
RecoilActive := false

; ====== CREATE GUI ======
CreateGUI()

; ====== MAIN AIMBOT LOOP ======
SetTimer, AimbotLoop, 10  ; 100Hz update rate
SetTimer, AntiRecoilLoop, 5  ; 200Hz for recoil

AimbotLoop:
    if (!AimbotEnabled)
        return
        
    ; Get mouse position for FOV center
    MouseGetPos, MouseX, MouseY
    
    ; Search for enemies in FOV
    FoundTarget := false
    TargetX := 0
    TargetY := 0
    
    ; Scan in spiral pattern for better performance
    Loop, %FOVSize% {
        Radius := A_Index * 2
        if (Radius > FOVSize)
            break
            
        ; Check 8 points around circle
        Loop, 8 {
            Angle := (A_Index - 1) * 45 * 0.0174533  ; Convert to radians
            CheckX := MouseX + (Radius * Cos(Angle))
            CheckY := MouseY + (Radius * Sin(Angle))
            
            ; Check if coordinates are on screen
            if (CheckX < 0 || CheckX > A_ScreenWidth || CheckY < 0 || CheckY > A_ScreenHeight)
                continue
                
            PixelGetColor, FoundColor, %CheckX%, %CheckY%, RGB
            
            ; Check if color matches enemy nameplate
            if (ColorMatch(FoundColor, EnemyColor, ColorVariation)) {
                TargetX := CheckX
                TargetY := CheckY
                FoundTarget := true
                break
            }
        }
        
        if (FoundTarget)
            break
    }
    
    ; Apply aim if target found
    if (FoundTarget) {
        ; Apply bone offset
        if (AimBone = "head")
            TargetY += HeadOffsetY
        else
            TargetY += ChestOffsetY
            
        ; Prediction system
        if (EnablePrediction && TargetLocked) {
            PredictX := TargetX + (TargetX - LastTargetX) * 2
            PredictY := TargetY + (TargetY - LastTargetY) * 2
            TargetX := PredictX
            TargetY := PredictY
        }
        
        ; Calculate aim movement
        DeltaX := TargetX - MouseX
        DeltaY := TargetY - MouseY
        
        ; Apply smoothing
        MoveX := DeltaX * AimStrength
        MoveY := DeltaY * AimStrength
        
        ; Move mouse
        DllCall("mouse_event", "UInt", 0x01, "Int", MoveX, "Int", MoveY, "UInt", 0, "Ptr", 0)
        
        ; Update tracking
        LastTargetX := TargetX
        LastTargetY := TargetY
        TargetLocked := true
    } else {
        TargetLocked := false
    }
return

AntiRecoilLoop:
    if (!AntiRecoil || !RecoilActive)
        return
        
    ; Simple downward recoil compensation
    DllCall("mouse_event", "UInt", 0x01, "Int", 0, "Int", RecoilStrength, "UInt", 0, "Ptr", 0)
return

; ====== FUNCTIONS ======
ColorMatch(Color1, Color2, Variation) {
    R1 := (Color1 >> 16) & 0xFF
    G1 := (Color1 >> 8) & 0xFF
    B1 := Color1 & 0xFF
    
    R2 := (Color2 >> 16) & 0xFF
    G2 := (Color2 >> 8) & 0xFF
    B2 := Color2 & 0xFF
    
    return (Abs(R1 - R2) <= Variation && Abs(G1 - G2) <= Variation && Abs(B1 - B2) <= Variation)
}

ToggleAimbot() {
    AimbotEnabled := !AimbotEnabled
    UpdateGUI()
    if (AimbotEnabled)
        TrayTip, MW3 Aimbot, Aimbot Enabled, 1
    else
        TrayTip, MW3 Aimbot, Aimbot Disabled, 1
}

ToggleGUI() {
    if (GUIVisible) {
        Gui, Hide
        GUIVisible := false
    } else {
        Gui, Show
        GUIVisible := true
    }
}

ToggleAntiRecoil() {
    AntiRecoil := !AntiRecoil
    UpdateGUI()
    if (AntiRecoil)
        TrayTip, MW3 Aimbot, Anti-Recoil Enabled, 1
    else
        TrayTip, MW3 Aimbot, Anti-Recoil Disabled, 1
}

CreateGUI() {
    ; Create modern GUI with anti-screenshot protection
    Gui, Add, Text, x10 y10 w200 h20 Center, MW3/MWZ External Aimbot
    Gui, Add, Text, x10 y35 w200 h1 0x10  ; Separator line
    
    ; Status indicators
    Gui, Add, Text, x10 y45 w100 h20 vStatusText, Status: Enabled
    Gui, Add, Text, x10 y65 w100 h20 vTargetText, Target: None
    
    ; Controls
    Gui, Add, Text, x10 y90 w200 h20, FOV Size: %FOVSize%
    Gui, Add, Slider, x10 y110 w180 h20 vFOVSlider Range50-300 gUpdateFOV, %FOVSize%
    
    Gui, Add, Text, x10 y140 w200 h20, Aim Strength: %AimStrength%
    Gui, Add, Slider, x10 y160 w180 h20 vStrengthSlider Range1-100 gUpdateStrength, % (AimStrength * 100)
    
    Gui, Add, Checkbox, x10 y190 w180 h20 vPredictionCheck gUpdatePrediction Checked%EnablePrediction%, Prediction System
    Gui, Add, Checkbox, x10 y210 w180 h20 vRecoilCheck gUpdateRecoil Checked%AntiRecoil%, Anti-Recoil
    
    ; Hotkey info
    Gui, Add, Text, x10 y240 w200 h60, Hotkeys:`nF1 - Toggle Aimbot`nF2 - Toggle GUI`nF3 - Toggle Anti-Recoil
    
    ; Apply anti-ricochet protection to GUI
    Gui, +LastFound +AlwaysOnTop -Caption +ToolWindow
    hWnd := WinExist()
    DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
    WinSet, ExStyle, +0x00200000, ahk_id %hWnd%
    
    Gui, Show, w220 h310, MW3 Aimbot
    UpdateGUI()
}

UpdateGUI() {
    StatusText := AimbotEnabled ? "Enabled" : "Disabled"
    TargetText := TargetLocked ? "Locked" : "Searching"
    
    GuiControl,, StatusText, Status: %StatusText%
    GuiControl,, TargetText, Target: %TargetText%
}

UpdateFOV() {
    Gui, Submit, NoHide
    FOVSize := FOVSlider
    GuiControl,, Static4, FOV Size: %FOVSize%
}

UpdateStrength() {
    Gui, Submit, NoHide
    AimStrength := StrengthSlider / 100.0
    GuiControl,, Static5, Aim Strength: %AimStrength%
}

UpdatePrediction() {
    Gui, Submit, NoHide
    EnablePrediction := PredictionCheck
}

UpdateRecoil() {
    Gui, Submit, NoHide
    AntiRecoil := RecoilCheck
}

; ====== MOUSE HOOKS FOR RECOIL ======
~LButton::
    RecoilActive := true
return

~LButton Up::
    RecoilActive := false
return

; ====== EXIT HANDLER ======
GuiClose:
ExitApp

; ====== STARTUP MESSAGE ======
TrayTip, MW3/MWZ Aimbot, External aimbot loaded`nRicochet-proof design`nPress F1 to toggle, 3
