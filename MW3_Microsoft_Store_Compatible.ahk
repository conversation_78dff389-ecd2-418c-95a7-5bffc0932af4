; ====== MW3 MICROSOFT STORE COMPATIBLE SYSTEM =======
; Safe input simulation without memory access
; Compatible with Microsoft Store MW3 security restrictions
; Version: 1.0 MICROSOFT STORE EDITION
; ====== MW3 MICROSOFT STORE COMPATIBLE SYSTEM =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input
SetWorkingDir %A_ScriptDir%

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global MW3_WINDOW_TITLE := "Call of Duty: Modern Warfare III"
global MW3_WINDOW_HANDLE := 0
global VALIDATION_PASSED := false

; Feature toggles
global RAPID_FIRE_ENABLED := false
global SUPER_JUMP_ENABLED := false
global RECOIL_COMPENSATION_ENABLED := false

; Settings
global RAPID_FIRE_DELAY := 50
global SUPER_JUMP_STRENGTH := 3
global RECOIL_COMPENSATION_STRENGTH := 0.3

; ====== STARTUP =======
TrayTip, MW3 Microsoft Store Compatible, Starting Microsoft Store compatible system, 3, 1

; ====== CREATE GUI =======
Gui, Destroy
Gui, Color, 0x2d2d30
Gui, Font, s14 Bold cLime, Arial
Gui, Add, Text, x10 y10 w480 Center, MW3 MICROSOFT STORE COMPATIBLE

Gui, Font, s10 cWhite, Arial
Gui, Add, Text, x20 y40 w460, Status: Initializing Microsoft Store compatible system

; Status display
Gui, Add, Text, x20 y70 w460 h60 vStatusDisplay, Checking MW3 window - Microsoft Store compatible - No memory access required

; Feature controls
Gui, Font, s11 Bold cYellow, Arial
Gui, Add, Text, x20 y140 w460, AVAILABLE FEATURES:

Gui, Font, s9 cWhite, Arial
Gui, Add, Checkbox, x30 y170 w200 vRapidFireCheck gToggleRapidFire, Rapid Fire
Gui, Add, Checkbox, x250 y170 w200 vSuperJumpCheck gToggleSuperJump, Super Jump
Gui, Add, Checkbox, x30 y200 w200 vRecoilCheck gToggleRecoil, Recoil Compensation

; Settings
Gui, Add, Text, x30 y240, Rapid Fire Delay (ms):
Gui, Add, Edit, x180 y238 w60 vRapidFireDelayEdit, %RAPID_FIRE_DELAY%
Gui, Add, Button, x250 y237 w80 h23 gUpdateSettings, Update

; Control buttons
Gui, Add, Button, x20 y280 w220 h40 gValidateSystem, VALIDATE MW3 WINDOW
Gui, Add, Button, x260 y280 w220 h40 gToggleSystem, START SYSTEM

; Instructions
Gui, Font, s8 cCyan, Arial
Gui, Add, Text, x20 y340 w460, MICROSOFT STORE MW3 INSTRUCTIONS:
Gui, Add, Text, x20 y360 w460, 1. Start MW3 and get to main menu or in-game
Gui, Add, Text, x20 y380 w460, 2. Click "Validate MW3 Window" to detect the game
Gui, Add, Text, x20 y400 w460, 3. Enable desired features using checkboxes
Gui, Add, Text, x20 y420 w460, 4. Click "Start System" to activate

; Safety notice
Gui, Font, s8 cYellow, Arial
Gui, Add, Text, x20 y450 w460, Microsoft Store Compatible - No memory access required
Gui, Add, Text, x20 y470 w460, Uses safe input simulation only - Account safe

Gui, Show, w500 h500, MW3 Microsoft Store Compatible

; Auto-validate on startup
SetTimer, AutoValidate, 2000

; ====== VALIDATE MW3 WINDOW =======
ValidateSystem:
    GuiControl,, StatusDisplay, Searching for MW3 window...
    
    ; Try different window titles for Microsoft Store MW3
    windowTitles := "Call of Duty: Modern Warfare III|Modern Warfare III|MW3|Call of Duty|COD"
    StringSplit, titleArray, windowTitles, |
    
    MW3_WINDOW_HANDLE := 0
    foundTitle := ""
    
    Loop, %titleArray0% {
        testTitle := titleArray%A_Index%
        WinGet, testHandle, ID, %testTitle%
        if (testHandle) {
            MW3_WINDOW_HANDLE := testHandle
            foundTitle := testTitle
            break
        }
    }
    
    if (MW3_WINDOW_HANDLE) {
        VALIDATION_PASSED := true
        GuiControl,, StatusDisplay, MW3 window found: %foundTitle% - Window handle: %MW3_WINDOW_HANDLE% - System ready for activation
        TrayTip, Validation Success, MW3 window detected successfully!, 3, 1
    } else {
        VALIDATION_PASSED := false
        GuiControl,, StatusDisplay, MW3 window not found - Make sure MW3 is running and visible - Try running as administrator
        TrayTip, Validation Failed, MW3 window not found - Start MW3 first, 5, 2
    }
return

; ====== AUTO VALIDATE =======
AutoValidate:
    if (!VALIDATION_PASSED) {
        Gosub, ValidateSystem
    }
    SetTimer, AutoValidate, 5000  ; Check every 5 seconds
return

; ====== TOGGLE SYSTEM =======
ToggleSystem:
    if (!VALIDATION_PASSED) {
        TrayTip, System Error, Please validate MW3 window first!, 3, 2
        return
    }
    
    SYSTEM_ENABLED := !SYSTEM_ENABLED
    
    if (SYSTEM_ENABLED) {
        GuiControl,, StatusDisplay, SYSTEM ACTIVE - MW3 window: %MW3_WINDOW_HANDLE% - Features enabled - Use hotkeys in MW3
        GuiControl, Text, ToggleSystem, STOP SYSTEM
        TrayTip, System Active, MW3 Microsoft Store system activated!, 3, 1
        
        ; Start feature timers
        if (RAPID_FIRE_ENABLED) {
            SetTimer, RapidFireLoop, %RAPID_FIRE_DELAY%
        }
        if (SUPER_JUMP_ENABLED) {
            SetTimer, SuperJumpLoop, 100
        }
        if (RECOIL_COMPENSATION_ENABLED) {
            SetTimer, RecoilLoop, 10
        }
    } else {
        GuiControl,, StatusDisplay, SYSTEM STOPPED - All features disabled - Click Start System to reactivate
        GuiControl, Text, ToggleSystem, START SYSTEM
        TrayTip, System Stopped, MW3 system deactivated, 2, 1
        
        ; Stop all timers
        SetTimer, RapidFireLoop, Off
        SetTimer, SuperJumpLoop, Off
        SetTimer, RecoilLoop, Off
    }
return

; ====== FEATURE TOGGLES =======
ToggleRapidFire:
    Gui, Submit, NoHide
    RAPID_FIRE_ENABLED := RapidFireCheck
    if (RAPID_FIRE_ENABLED && SYSTEM_ENABLED) {
        SetTimer, RapidFireLoop, %RAPID_FIRE_DELAY%
        TrayTip, Rapid Fire, Rapid Fire enabled, 2, 1
    } else {
        SetTimer, RapidFireLoop, Off
        TrayTip, Rapid Fire, Rapid Fire disabled, 2, 1
    }
return

ToggleSuperJump:
    Gui, Submit, NoHide
    SUPER_JUMP_ENABLED := SuperJumpCheck
    if (SUPER_JUMP_ENABLED && SYSTEM_ENABLED) {
        SetTimer, SuperJumpLoop, 100
        TrayTip, Super Jump, Super Jump enabled, 2, 1
    } else {
        SetTimer, SuperJumpLoop, Off
        TrayTip, Super Jump, Super Jump disabled, 2, 1
    }
return

ToggleRecoil:
    Gui, Submit, NoHide
    RECOIL_COMPENSATION_ENABLED := RecoilCheck
    if (RECOIL_COMPENSATION_ENABLED && SYSTEM_ENABLED) {
        SetTimer, RecoilLoop, 10
        TrayTip, Recoil Compensation, Recoil compensation enabled, 2, 1
    } else {
        SetTimer, RecoilLoop, Off
        TrayTip, Recoil Compensation, Recoil compensation disabled, 2, 1
    }
return

; ====== UPDATE SETTINGS =======
UpdateSettings:
    Gui, Submit, NoHide
    RAPID_FIRE_DELAY := RapidFireDelayEdit
    if (RAPID_FIRE_DELAY < 10) {
        RAPID_FIRE_DELAY := 10
    }
    if (RAPID_FIRE_DELAY > 1000) {
        RAPID_FIRE_DELAY := 1000
    }
    GuiControl,, RapidFireDelayEdit, %RAPID_FIRE_DELAY%
    TrayTip, Settings Updated, Rapid fire delay: %RAPID_FIRE_DELAY%ms, 2, 1
return

; ====== FEATURE LOOPS =======
RapidFireLoop:
    if (!SYSTEM_ENABLED || !RAPID_FIRE_ENABLED) {
        return
    }
    
    ; Check if MW3 window is active and left mouse button is held
    WinGet, activeWindow, ID, A
    if (activeWindow = MW3_WINDOW_HANDLE && GetKeyState("LButton", "P")) {
        ; Simulate rapid clicking
        Click
    }
return

SuperJumpLoop:
    if (!SYSTEM_ENABLED || !SUPER_JUMP_ENABLED) {
        return
    }
    
    ; Check if MW3 window is active and space is pressed
    WinGet, activeWindow, ID, A
    if (activeWindow = MW3_WINDOW_HANDLE && GetKeyState("Space", "P")) {
        ; Simulate multiple jump inputs for super jump
        Loop, %SUPER_JUMP_STRENGTH% {
            Send, {Space}
            Sleep, 5
        }
        Sleep, 100  ; Prevent spam
    }
return

RecoilLoop:
    if (!SYSTEM_ENABLED || !RECOIL_COMPENSATION_ENABLED) {
        return
    }
    
    ; Check if MW3 window is active and left mouse button is held
    WinGet, activeWindow, ID, A
    if (activeWindow = MW3_WINDOW_HANDLE && GetKeyState("LButton", "P")) {
        ; Simple recoil compensation - move mouse down slightly
        DllCall("mouse_event", "UInt", 0x01, "Int", 0, "Int", 1, "UInt", 0, "Ptr", 0)
    }
return

GuiClose:
    ExitApp
