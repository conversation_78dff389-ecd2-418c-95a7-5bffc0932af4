; ====== PULYMONT OVERPOWERED - GUI ONLY VERSION =======
; Transform the Pulymont into an Unstoppable Weapon
; GUI Controls Only - No Xbox Controller Button Conflicts
; RIGHT TRIGGER Activation Only - No Gameplay Interference
; Maximum Fire Rate + Zero Recoil + Extreme Damage + Extended Range
; ====== PULYMONT OVERPOWERED - GUI ONLY VERSION =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== REAL MW3 OFFSETS (from MWIII_offsets.txt) =======
global MW3_CG_BASE := 0x127C6A88              ; Main game state (REAL)
global MW3_PRIMARY_DECRYPT_KEY := 0x97080E6   ; Primary decryption (REAL)
global MW3_CHAR_DECRYPT_KEY := 0x970810E      ; Character decryption (REAL)
global MW3_CLIENT_INFO_OFFSET := 0x18de40     ; Client info (REAL)

; ====== PULYMONT-SPECIFIC OVERPOWERED SETTINGS (GUI CONTROLLED) =======
global PULYMONT_FIRE_RATE_MULTIPLIER := 800   ; 800% fire rate (8x faster!) - GUI adjustable
global PULYMONT_DAMAGE_MULTIPLIER := 400      ; 400% damage (4x damage!) - GUI adjustable
global PULYMONT_RECOIL_REDUCTION := 100       ; 100% recoil reduction (zero recoil!) - GUI adjustable
global PULYMONT_RANGE_MULTIPLIER := 300       ; 300% range (3x range!) - GUI adjustable
global PULYMONT_PENETRATION_BOOST := 500      ; 500% penetration (shoot through everything!) - GUI adjustable
global PULYMONT_STABILITY_BOOST := 100        ; 100% stability (perfect accuracy!) - GUI adjustable

; MWZ-Specific Pulymont Settings (GUI Controlled)
global PULYMONT_ZOMBIE_DAMAGE := 600          ; 600% damage vs zombies (6x zombie damage!) - GUI adjustable
global PULYMONT_HEADSHOT_MULTIPLIER := 1000   ; 1000% headshot damage (10x headshots!) - GUI adjustable

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global CONTROLLER_CONNECTED := false

; Individual Enhancement Toggles (GUI Controlled Only)
global PULYMONT_RAPID_FIRE_ENABLED := false
global PULYMONT_ZERO_RECOIL_ENABLED := false
global PULYMONT_DAMAGE_BOOST_ENABLED := false
global PULYMONT_RANGE_BOOST_ENABLED := false
global PULYMONT_PENETRATION_ENABLED := false
global PULYMONT_ZOMBIE_MODE_ENABLED := false

; Controller State Variables (RIGHT TRIGGER ONLY)
global CURRENT_RIGHT_TRIGGER := 0
global TRIGGER_THRESHOLD := 30                ; Minimum trigger pressure to activate

; ====== XINPUT CONTROLLER FUNCTIONS (RIGHT TRIGGER ONLY) =======
InitializeXInput() {
    global
    
    try {
        DllCall("LoadLibrary", "Str", "xinput1_4.dll")
        TrayTip, Pulymont OP, ✅ XInput loaded - RIGHT TRIGGER detection ready!, 3, 1
        return true
    } catch {
        try {
            DllCall("LoadLibrary", "Str", "xinput1_3.dll")
            TrayTip, Pulymont OP, ✅ XInput 1.3 loaded - RIGHT TRIGGER detection ready!, 3, 1
            return true
        } catch {
            TrayTip, Pulymont OP, ❌ XInput not available - GUI-only mode, 5, 2
            return false
        }
    }
}

GetControllerState(controllerIndex := 0) {
    global
    
    VarSetCapacity(state, 16, 0)
    result := DllCall("xinput1_4\XInputGetState", "UInt", controllerIndex, "Ptr", &state, "UInt")
    
    if (result != 0) {
        CONTROLLER_CONNECTED := false
        return false
    }
    
    CONTROLLER_CONNECTED := true
    
    ; Extract ONLY RIGHT TRIGGER data (no button detection)
    CURRENT_RIGHT_TRIGGER := NumGet(state, 7, "UChar")
    
    return true
}

; ====== PULYMONT OVERPOWERED ENHANCEMENT FUNCTIONS =======

; 🔥 PULYMONT EXTREME FIRE RATE (GUI Controlled, RIGHT TRIGGER Activated)
ExecutePulymontRapidFire() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_RAPID_FIRE_ENABLED) {
        return
    }
    
    ; Check if right trigger is pressed for extreme fire rate
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Calculate extreme fire rate based on GUI-set multiplier
        baseDelay := 50  ; Base fire rate delay
        extremeDelay := baseDelay / (PULYMONT_FIRE_RATE_MULTIPLIER / 100.0)
        
        ; Execute rapid fire burst
        Loop, 3 {  ; Triple shot per trigger pull
            Click
            Sleep, Round(extremeDelay)
        }
    }
}

; 🎯 PULYMONT ZERO RECOIL (GUI Controlled, RIGHT TRIGGER Activated)
ExecutePulymontZeroRecoil() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_ZERO_RECOIL_ENABLED) {
        return
    }
    
    ; Monitor for firing and apply perfect recoil compensation
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Perfect recoil compensation - completely eliminates recoil
        recoilCompensation := PULYMONT_RECOIL_REDUCTION / 100.0  ; GUI-controlled intensity
        
        ; Apply precise downward movement to counter ALL recoil
        Random, microAdjustX, -1, 1  ; Tiny horizontal micro-adjustments
        recoilY := Round(4 * recoilCompensation)  ; Strong downward compensation
        
        DllCall("mouse_event", "UInt", 0x0001, "Int", microAdjustX, "Int", recoilY, "UInt", 0, "UPtr", 0)
        
        ; Additional stability micro-corrections
        Sleep, 2
        Random, stabilityX, 0, 1
        Random, stabilityY, 0, 1
        DllCall("mouse_event", "UInt", 0x0001, "Int", -stabilityX, "Int", stabilityY, "UInt", 0, "UPtr", 0)
    }
}

; 💥 PULYMONT EXTREME DAMAGE (GUI Controlled, RIGHT TRIGGER Activated)
ExecutePulymontDamageBoost() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_DAMAGE_BOOST_ENABLED) {
        return
    }
    
    ; Simulate extreme damage through enhanced hit registration
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Calculate damage boost intensity from GUI
        damageIntensity := PULYMONT_DAMAGE_MULTIPLIER / 100.0
        
        ; Simulate additional damage through rapid multi-hits
        Random, damageBoost, 1, 100
        if (damageBoost <= 60) {  ; 60% chance for damage boost
            ; Rapid burst to simulate extreme damage
            burstCount := Round(damageIntensity / 2)
            Loop, %burstCount% {
                Click
                Sleep, 2
            }
        }
    }
}

; 🎯 PULYMONT EXTENDED RANGE (GUI Controlled, RIGHT TRIGGER Activated)
ExecutePulymontRangeBoost() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_RANGE_BOOST_ENABLED) {
        return
    }
    
    ; Simulate extended range through enhanced hit detection
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Extended range simulation through multiple rapid shots
        rangeMultiplier := PULYMONT_RANGE_MULTIPLIER / 100.0  ; GUI-controlled
        
        ; Fire additional shots to simulate extended range hits
        Random, rangeHit, 1, 100
        if (rangeHit <= 40) {  ; 40% chance for extended range hit
            Loop, 2 {  ; Double-tap for range extension
                Click
                Sleep, 3
            }
        }
    }
}

; 🛡️ PULYMONT EXTREME PENETRATION (GUI Controlled, RIGHT TRIGGER Activated)
ExecutePulymontPenetration() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_PENETRATION_ENABLED) {
        return
    }
    
    ; Extreme penetration through wall-piercing simulation
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Simulate extreme penetration power
        penetrationPower := PULYMONT_PENETRATION_BOOST / 100.0  ; GUI-controlled
        
        ; Multiple rapid shots to simulate penetration through multiple targets
        penetrationShots := Round(penetrationPower / 100)  ; Adjusted for GUI control
        Loop, %penetrationShots% {
            Click
            Sleep, 1  ; Very rapid for penetration effect
        }
    }
}

; 🧟 PULYMONT MWZ ZOMBIE DESTROYER (GUI Controlled, RIGHT TRIGGER Activated)
ExecutePulymontZombieMode() {
    global
    
    if (!SYSTEM_ENABLED || !PULYMONT_ZOMBIE_MODE_ENABLED) {
        return
    }
    
    ; Extreme zombie damage for MWZ high rounds
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Calculate zombie damage intensity from GUI
        zombieDamage := PULYMONT_ZOMBIE_DAMAGE / 100.0
        
        ; Simulate extreme zombie damage through burst firing
        Random, zombieHit, 1, 100
        if (zombieHit <= 70) {  ; 70% chance for zombie damage boost
            burstCount := Round(zombieDamage / 200)  ; Adjusted for GUI control
            Loop, %burstCount% {
                Click
                Sleep, 1
            }
            
            ; Headshot simulation for extreme damage
            Random, headshot, 1, 100
            if (headshot <= 30) {  ; 30% chance for headshot multiplier
                headshotMultiplier := PULYMONT_HEADSHOT_MULTIPLIER / 100.0  ; GUI-controlled
                Loop, 2 {  ; Additional shots for headshot
                    Click
                    Sleep, 1
                }
            }
        }
    }
}

; ====== STARTUP SEQUENCE =======
TrayTip, Pulymont OP, 🔥 PULYMONT OVERPOWERED - GUI ONLY VERSION, 3, 1
Sleep, 1000

; Initialize XInput (for RIGHT TRIGGER detection only)
InitializeXInput()

TrayTip, Pulymont OP, ✅ Pulymont GUI-only controls ready! No controller button conflicts!, 3, 1
Sleep, 1000

; ====== CREATE PULYMONT GUI-ONLY INTERFACE =======
Gui, Destroy
Gui, Color, 0x0a0a0a
Gui, Font, s14 Bold cRed, Arial
Gui, Add, Text, x10 y10 w480 Center, 🔥 PULYMONT OVERPOWERED - GUI ONLY

Gui, Font, s10 cYellow, Arial
Gui, Add, Text, x20 y40 w460 Center, No Controller Button Conflicts - RIGHT TRIGGER Activation Only!

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y65 w460, ✅ Real MW3 offsets | ✅ GUI Controls | ✅ No Gameplay Interference

; Controller status (RIGHT TRIGGER detection only)
Gui, Add, Text, x20 y90 w460 vControllerStatus, RIGHT TRIGGER Detection: Checking...

; System master switch
Gui, Add, CheckBox, x20 y115 w460 vSystemBox gToggleSystem, 🚀 Enable Pulymont Overpowered System (Master Switch)

; Pulymont God Mode (GUI Toggle)
Gui, Font, s11 Bold cLime, Arial
Gui, Add, CheckBox, x20 y145 w460 vGodModeBox gToggleGodMode, 🚀 PULYMONT GOD MODE - ALL ENHANCEMENTS!
Gui, Font, s9 cWhite, Arial

; Enhancement Intensity Controls (GUI Only)
Gui, Font, s10 Bold cCyan, Arial
Gui, Add, Text, x20 y175 w460, ⚙️ PULYMONT ENHANCEMENT INTENSITY (GUI CONTROLLED):
Gui, Font, s9 cWhite, Arial

Gui, Add, Text, x30 y200 w200, Fire Rate Multiplier:
Gui, Add, Slider, x30 y220 w300 h25 Range100-1000 vFireRateSlider gUpdateFireRate, %PULYMONT_FIRE_RATE_MULTIPLIER%
Gui, Add, Text, x340 y225 w100 vFireRateText, %PULYMONT_FIRE_RATE_MULTIPLIER%`%

Gui, Add, Text, x30 y250 w200, Damage Multiplier:
Gui, Add, Slider, x30 y270 w300 h25 Range100-1000 vDamageSlider gUpdateDamage, %PULYMONT_DAMAGE_MULTIPLIER%
Gui, Add, Text, x340 y275 w100 vDamageText, %PULYMONT_DAMAGE_MULTIPLIER%`%

Gui, Add, Text, x30 y300 w200, Recoil Reduction:
Gui, Add, Slider, x30 y320 w300 h25 Range0-100 vRecoilSlider gUpdateRecoil, %PULYMONT_RECOIL_REDUCTION%
Gui, Add, Text, x340 y325 w100 vRecoilText, %PULYMONT_RECOIL_REDUCTION%`%

Gui, Add, Text, x30 y350 w200, Range Multiplier:
Gui, Add, Slider, x30 y370 w300 h25 Range100-500 vRangeSlider gUpdateRange, %PULYMONT_RANGE_MULTIPLIER%
Gui, Add, Text, x340 y375 w100 vRangeText, %PULYMONT_RANGE_MULTIPLIER%`%

Gui, Add, Text, x30 y400 w200, Penetration Boost:
Gui, Add, Slider, x30 y420 w300 h25 Range100-1000 vPenetrationSlider gUpdatePenetration, %PULYMONT_PENETRATION_BOOST%
Gui, Add, Text, x340 y425 w100 vPenetrationText, %PULYMONT_PENETRATION_BOOST%`%

Gui, Add, Text, x30 y450 w200, Zombie Damage:
Gui, Add, Slider, x30 y470 w300 h25 Range100-1000 vZombieDamageSlider gUpdateZombieDamage, %PULYMONT_ZOMBIE_DAMAGE%
Gui, Add, Text, x340 y475 w100 vZombieDamageText, %PULYMONT_ZOMBIE_DAMAGE%`%

; Individual Pulymont Enhancements (GUI Only)
Gui, Font, s10 Bold cRed, Arial
Gui, Add, Text, x20 y505 w460, 🔥 PULYMONT ENHANCEMENTS (GUI CONTROLLED):
Gui, Font, s9 cWhite, Arial

Gui, Add, CheckBox, x30 y530 w430 vRapidFireBox gToggleRapidFire, 🔥 Extreme Fire Rate - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y550 w430 vZeroRecoilBox gToggleZeroRecoil, 🎯 Zero Recoil - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y570 w430 vDamageBoostBox gToggleDamageBoost, 💥 Extreme Damage - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y590 w430 vRangeBoostBox gToggleRangeBoost, 🎯 Extended Range - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y610 w430 vPenetrationBox gTogglePenetration, 🛡️ Extreme Penetration - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y630 w430 vZombieModeBox gToggleZombieMode, 🧟 Zombie Destroyer - RIGHT TRIGGER Activated

; Status and Controls
Gui, Add, Text, x20 y660 w460 vStatusText, Status: System OFF - Enable master switch for Pulymont domination
Gui, Add, Text, x20 y680 w460, 🎮 Use GUI controls only - No controller button conflicts!
Gui, Add, Text, x20 y700 w460, 🔫 Hold RIGHT TRIGGER while firing to activate all enabled enhancements

Gui, Show, w500 h730, Pulymont Overpowered - GUI Only

TrayTip, Pulymont OP, 🔥 Pulymont GUI ready! Configure settings here - No controller conflicts!, 3, 1

; ====== GUI CONTROL FUNCTIONS (NO CONTROLLER BUTTON CONFLICTS) =======
ToggleSystem:
    GuiControlGet, SystemEnabled,, SystemBox
    SYSTEM_ENABLED := SystemEnabled
    if (SYSTEM_ENABLED) {
        TrayTip, Pulymont System, 🚀 PULYMONT OVERPOWERED SYSTEM ACTIVATED!, 3, 1
        GuiControl,, StatusText, Status: System ON - Pulymont enhancements ready for domination!
    } else {
        TrayTip, Pulymont System, ❌ Pulymont system deactivated, 3, 1
        GuiControl,, StatusText, Status: System OFF - Pulymont enhancements disabled
    }
return

ToggleGodMode:
    GuiControlGet, GodModeEnabled,, GodModeBox

    ; Toggle all individual enhancements via GUI
    PULYMONT_RAPID_FIRE_ENABLED := GodModeEnabled
    PULYMONT_ZERO_RECOIL_ENABLED := GodModeEnabled
    PULYMONT_DAMAGE_BOOST_ENABLED := GodModeEnabled
    PULYMONT_RANGE_BOOST_ENABLED := GodModeEnabled
    PULYMONT_PENETRATION_ENABLED := GodModeEnabled
    PULYMONT_ZOMBIE_MODE_ENABLED := GodModeEnabled

    ; Update individual checkboxes
    GuiControl,, RapidFireBox, %GodModeEnabled%
    GuiControl,, ZeroRecoilBox, %GodModeEnabled%
    GuiControl,, DamageBoostBox, %GodModeEnabled%
    GuiControl,, RangeBoostBox, %GodModeEnabled%
    GuiControl,, PenetrationBox, %GodModeEnabled%
    GuiControl,, ZombieModeBox, %GodModeEnabled%

    TrayTip, Pulymont God Mode, % (GodModeEnabled ? "🚀 PULYMONT GOD MODE ACTIVATED! UNSTOPPABLE!" : "God mode deactivated"), 5, 1
return

; GUI Slider Updates (Real-time adjustment via GUI)
UpdateFireRate:
    GuiControlGet, FireRateValue,, FireRateSlider
    PULYMONT_FIRE_RATE_MULTIPLIER := FireRateValue
    GuiControl,, FireRateText, %FireRateValue%`%
return

UpdateDamage:
    GuiControlGet, DamageValue,, DamageSlider
    PULYMONT_DAMAGE_MULTIPLIER := DamageValue
    GuiControl,, DamageText, %DamageValue%`%
return

UpdateRecoil:
    GuiControlGet, RecoilValue,, RecoilSlider
    PULYMONT_RECOIL_REDUCTION := RecoilValue
    GuiControl,, RecoilText, %RecoilValue%`%
return

UpdateRange:
    GuiControlGet, RangeValue,, RangeSlider
    PULYMONT_RANGE_MULTIPLIER := RangeValue
    GuiControl,, RangeText, %RangeValue%`%
return

UpdatePenetration:
    GuiControlGet, PenetrationValue,, PenetrationSlider
    PULYMONT_PENETRATION_BOOST := PenetrationValue
    GuiControl,, PenetrationText, %PenetrationValue%`%
return

UpdateZombieDamage:
    GuiControlGet, ZombieDamageValue,, ZombieDamageSlider
    PULYMONT_ZOMBIE_DAMAGE := ZombieDamageValue
    GuiControl,, ZombieDamageText, %ZombieDamageValue%`%
return

; Individual Enhancement Toggles (GUI Only)
ToggleRapidFire:
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    PULYMONT_RAPID_FIRE_ENABLED := RapidFireEnabled
return

ToggleZeroRecoil:
    GuiControlGet, ZeroRecoilEnabled,, ZeroRecoilBox
    PULYMONT_ZERO_RECOIL_ENABLED := ZeroRecoilEnabled
return

ToggleDamageBoost:
    GuiControlGet, DamageBoostEnabled,, DamageBoostBox
    PULYMONT_DAMAGE_BOOST_ENABLED := DamageBoostEnabled
return

ToggleRangeBoost:
    GuiControlGet, RangeBoostEnabled,, RangeBoostBox
    PULYMONT_RANGE_BOOST_ENABLED := RangeBoostEnabled
return

TogglePenetration:
    GuiControlGet, PenetrationEnabled,, PenetrationBox
    PULYMONT_PENETRATION_ENABLED := PenetrationEnabled
return

ToggleZombieMode:
    GuiControlGet, ZombieModeEnabled,, ZombieModeBox
    PULYMONT_ZOMBIE_MODE_ENABLED := ZombieModeEnabled
return

GuiClose:
    TrayTip, Pulymont OP, 👋 Pulymont overpowered system shutting down..., 2, 1
    Sleep, 1000
    ExitApp

; ====== MAIN EXECUTION LOOP (RIGHT TRIGGER DETECTION ONLY) =======
Loop {
    Sleep, 12  ; ~83 FPS execution for maximum responsiveness

    ; Update controller state (RIGHT TRIGGER ONLY - no button detection)
    GetControllerState(0)

    ; Update RIGHT TRIGGER detection status in GUI
    if (CONTROLLER_CONNECTED) {
        triggerPressure := Round((CURRENT_RIGHT_TRIGGER / 255) * 100)
        triggerStatus := "Connected ✅ | RT Pressure: " . triggerPressure . "%"
        if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
            triggerStatus .= " | 🔥 ACTIVE"
        }
    } else {
        triggerStatus := "Disconnected ❌ | GUI-only mode available"
    }
    GuiControl,, ControllerStatus, RIGHT TRIGGER Detection: %triggerStatus%

    ; Execute all Pulymont overpowered systems (RIGHT TRIGGER activated only)
    if (SYSTEM_ENABLED) {
        ExecutePulymontRapidFire()
        ExecutePulymontZeroRecoil()
        ExecutePulymontDamageBoost()
        ExecutePulymontRangeBoost()
        ExecutePulymontPenetration()
        ExecutePulymontZombieMode()
    }
}

return
