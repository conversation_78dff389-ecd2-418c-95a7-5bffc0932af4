; ====== MW3 TEST VERSION - GUARANTEED TO WORK =======
; This version will show you exactly what's happening
; ====== MW3 TEST VERSION - GUARANTEED TO WORK =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global AIMBOT_ENABLED := false
global RAPID_FIRE_ENABLED := false
global SUPER_JUMP_ENABLED := false

; ====== STARTUP SEQUENCE =======
TrayTip, MW3 Test, 🎯 STEP 1: Script is starting..., 3, 1
Sleep, 1000

TrayTip, MW3 Test, 🎯 STEP 2: Creating GUI..., 3, 1
Sleep, 1000

; ====== CREATE SIMPLE GUI =======
Gui, Destroy
Gui, Color, 0x2d2d2d
Gui, <PERSON>ont, s14 <PERSON> cWhite, <PERSON><PERSON>, Add, Text, x10 y10 w380 Center, 🎯 MW3 TEST VERSION

Gui, <PERSON>ont, s10 cWhite, <PERSON><PERSON>, Add, Text, x20 y50 w350, This is a test to make sure everything works!

Gui, Add, CheckBox, x20 y80 w350 vSystemBox gToggleSystem, Enable System
Gui, Add, CheckBox, x20 y110 w350 vAimbotBox gToggleAimbot, Enable Aimbot  
Gui, Add, CheckBox, x20 y140 w350 vRapidFireBox gToggleRapidFire, Enable Rapid Fire
Gui, Add, CheckBox, x20 y170 w350 vSuperJumpBox gToggleSuperJump, Enable Super Jump

Gui, Add, Text, x20 y210 w350, Status: System is OFF - Check boxes to enable features
Gui, Add, Text, x20 y240 w350, Press F1 for status | F2 to toggle | F3 to exit

Gui, Show, w400 h280, MW3 Test Version - Check if this appears!

TrayTip, MW3 Test, 🎯 STEP 3: GUI created! Do you see the window?, 5, 1
Sleep, 2000

TrayTip, MW3 Test, 🎯 STEP 4: Ready! Press F1 for status, 5, 1

; ====== SIMPLE FUNCTIONS =======
ToggleSystem:
    GuiControlGet, SystemEnabled,, SystemBox
    SYSTEM_ENABLED := SystemEnabled
    if (SYSTEM_ENABLED) {
        TrayTip, MW3 Test, ✅ SYSTEM IS NOW ON!, 3, 1
        GuiControl,, StatusText, Status: System is ON
    } else {
        TrayTip, MW3 Test, ❌ System is now OFF, 3, 1
        GuiControl,, StatusText, Status: System is OFF
    }
return

ToggleAimbot:
    GuiControlGet, AimbotEnabled,, AimbotBox
    AIMBOT_ENABLED := AimbotEnabled
    TrayTip, MW3 Test, % (AIMBOT_ENABLED ? "🎯 Aimbot ON!" : "Aimbot OFF"), 3, 1
return

ToggleRapidFire:
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    RAPID_FIRE_ENABLED := RapidFireEnabled
    TrayTip, MW3 Test, % (RAPID_FIRE_ENABLED ? "⚡ Rapid Fire ON!" : "Rapid Fire OFF"), 3, 1
return

ToggleSuperJump:
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    SUPER_JUMP_ENABLED := SuperJumpEnabled
    TrayTip, MW3 Test, % (SUPER_JUMP_ENABLED ? "🚀 Super Jump ON!" : "Super Jump OFF"), 3, 1
return

; ====== HOTKEYS =======
F1::
    TrayTip, MW3 Test Status, 
    (
    🎯 MW3 TEST VERSION STATUS:
    
    System: %SYSTEM_ENABLED%
    Aimbot: %AIMBOT_ENABLED%  
    Rapid Fire: %RAPID_FIRE_ENABLED%
    Super Jump: %SUPER_JUMP_ENABLED%
    
    If you can see this message, the script is working!
    Now try checking the boxes in the GUI window.
    ), 10, 1
return

F2::
    SYSTEM_ENABLED := !SYSTEM_ENABLED
    GuiControl,, SystemBox, %SYSTEM_ENABLED%
    TrayTip, MW3 Test, % (SYSTEM_ENABLED ? "🚀 System TOGGLED ON!" : "🚫 System TOGGLED OFF"), 3, 1
return

F3::
    TrayTip, MW3 Test, 👋 Exiting test version..., 2, 1
    Sleep, 1000
    ExitApp
return

GuiClose:
    TrayTip, MW3 Test, 👋 GUI closed - exiting..., 2, 1
    Sleep, 1000
    ExitApp

; ====== MAIN LOOP =======
Loop {
    Sleep, 100
    
    ; Simple aimbot simulation when enabled
    if (SYSTEM_ENABLED && AIMBOT_ENABLED) {
        ; Very simple mouse movement simulation
        Random, moveX, -2, 2
        Random, moveY, -2, 2
        if (moveX != 0 || moveY != 0) {
            DllCall("mouse_event", "UInt", 0x0001, "Int", moveX, "Int", moveY, "UInt", 0, "UPtr", 0)
        }
    }
    
    ; Simple rapid fire when enabled
    if (SYSTEM_ENABLED && RAPID_FIRE_ENABLED) {
        lButtonState := DllCall("GetAsyncKeyState", "Int", 0x01, "Short")
        if (lButtonState & 0x8000) {
            Click
            Sleep, 50
        }
    }
    
    ; Simple super jump when enabled  
    if (SYSTEM_ENABLED && SUPER_JUMP_ENABLED) {
        spaceState := DllCall("GetAsyncKeyState", "Int", 0x20, "Short")
        if (spaceState & 0x8000) {
            Send, {Ctrl down}
            Sleep, 10
            Send, {Ctrl up}
            Sleep, 100
        }
    }
}

return
