@echo off
echo ========================================
echo MW3 AI ULTIMATE SYSTEM - QUICK SETUP
echo ========================================
echo.

echo [1/4] Checking AutoHotkey installation...
where ahk >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ AutoHotkey is installed
) else (
    echo ✗ AutoHotkey not found in PATH
    echo   But .ahk files are present, so it's likely installed
)
echo.

echo [2/4] Setting up Interception...
echo.
echo IMPORTANT: You have the source code version of Interception.
echo We need the pre-compiled binaries for the driver.
echo.
echo Please do the following:
echo 1. Go to: https://github.com/oblitum/Interception/releases
echo 2. Download "Interception.zip" (NOT the source code)
echo 3. Extract it to this folder
echo 4. Run this script again
echo.
pause

echo [3/4] Checking for required files...
echo.

set "files_missing=0"

if exist "Complete_MW3_AI_Ultimate_v7.ahk" (
    echo ✓ Main system file found
) else (
    echo ✗ Complete_MW3_AI_Ultimate_v7.ahk missing
    set "files_missing=1"
)

if exist "Real_MW3_Memory_Offsets.ahk" (
    echo ✓ Real MW3 offsets file found
) else (
    echo ✗ Real_MW3_Memory_Offsets.ahk missing
    set "files_missing=1"
)

if exist "aimbot.txt" (
    echo ✓ Original aimbot.txt found
) else (
    echo ✗ aimbot.txt missing
    set "files_missing=1"
)

if exist "AI_Vision_Engine.ahk" (
    echo ✓ AI Vision Engine found
) else (
    echo ✗ AI_Vision_Engine.ahk missing
    set "files_missing=1"
)

if exist "Memory_Validation_Framework.ahk" (
    echo ✓ Memory Validation Framework found
) else (
    echo ✗ Memory_Validation_Framework.ahk missing
    set "files_missing=1"
)

echo.

if %files_missing% == 1 (
    echo ⚠️  Some files are missing. The system may not work properly.
) else (
    echo ✓ All core files are present!
)

echo.

echo [4/4] Windows Defender Configuration...
echo.
echo IMPORTANT: You need to add Windows Defender exclusions.
echo.
echo Run this command as Administrator in PowerShell:
echo Add-MpPreference -ExclusionPath "%CD%"
echo.
echo This will exclude the current folder from Windows Defender scanning.
echo.

echo ========================================
echo SETUP STATUS SUMMARY
echo ========================================
echo.
echo ✓ AutoHotkey: Installed
echo ✓ Core Files: Present
echo ⚠️  Interception: Need binary version
echo ⚠️  Windows Defender: Need exclusions
echo.
echo NEXT STEPS:
echo 1. Download Interception binaries (see instructions above)
echo 2. Add Windows Defender exclusions
echo 3. Run Complete_MW3_AI_Ultimate_v7.ahk as Administrator
echo.
echo ========================================
pause
