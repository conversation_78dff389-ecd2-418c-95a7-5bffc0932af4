; MW3/MWZ Xbox Controller Setup - Easy Version
; Copy this entire script into a .ahk file and run it

#NoEnv
#SingleInstance Force
#Persistent
SendMode, InputThenPlay
SetBatchLines, -1

; ====== ANTI-DETECTION PROTECTION ======
Gui +LastFound +AlwaysOnTop -Caption +ToolWindow
hWnd := WinExist()
DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
WinSet, ExStyle, +0x00200000, ahk_id %hWnd%

; ====== EASY SETTINGS ======
RapidFireEnabled := false
RapidFireRate := 80  ; Milliseconds between shots (realistic)
SuperJumpEnabled := false
AntiRecoilEnabled := false

; ====== SIMPLE HOTKEYS ======
F1::
    RapidFireEnabled := !RapidFireEnabled
    if (RapidFireEnabled)
        TrayTip, MW3 Mods, Rapid Fire ON - Hold RT to use, 2
    else
        TrayTip, MW3 Mods, Rapid Fire OFF, 2
    UpdateGUI()
return

F2::
    SuperJumpEnabled := !SuperJumpEnabled
    if (SuperJumpEnabled)
        TrayTip, MW3 Mods, Super Jump ON - Press A to use, 2
    else
        TrayTip, MW3 Mods, Super Jump OFF, 2
    UpdateGUI()
return

F3::
    AntiRecoilEnabled := !AntiRecoilEnabled
    if (AntiRecoilEnabled)
        TrayTip, MW3 Mods, Anti-Recoil ON, 2
    else
        TrayTip, MW3 Mods, Anti-Recoil OFF, 2
    UpdateGUI()
return

Insert::ToggleGUI()

; ====== CONTROLLER MONITORING ======
SetTimer, CheckController, 10

CheckController:
    ; RAPID FIRE - Right Trigger (Joy1)
    if (RapidFireEnabled && GetKeyState("Joy1", "P")) {
        static LastShot := 0
        if (A_TickCount - LastShot >= RapidFireRate) {
            ; Rapid fire technique
            Send, {Joy1 up}
            Sleep, 5
            Send, {Joy1 down}
            LastShot := A_TickCount
        }
    }
    
    ; SUPER JUMP - A Button (Joy3)
    if (SuperJumpEnabled && GetKeyState("Joy3", "P")) {
        static JumpReady := true
        if (JumpReady) {
            ; Triple jump for super jump effect
            Loop, 3 {
                Send, {Joy3 down}
                Sleep, 15
                Send, {Joy3 up}
                Sleep, 25
            }
            JumpReady := false
            SetTimer, ResetJump, 1000
        }
    }
return

ResetJump:
    JumpReady := true
    SetTimer, ResetJump, Off
return

; ====== SIMPLE GUI ======
CreateGUI() {
    Gui, Add, Text, x10 y10 w200 h25 Center, MW3 Controller Mods
    Gui, Add, Text, x10 y40 w200 h2 0x10
    
    Gui, Add, Text, x10 y50 w200 h20 vRapidFireStatus, Rapid Fire: OFF
    Gui, Add, Text, x10 y70 w200 h20 vSuperJumpStatus, Super Jump: OFF  
    Gui, Add, Text, x10 y90 w200 h20 vAntiRecoilStatus, Anti-Recoil: OFF
    
    Gui, Add, Text, x10 y120 w200 h2 0x10
    Gui, Add, Text, x10 y130 w200 h60, Controls:`nF1 = Toggle Rapid Fire`nF2 = Toggle Super Jump`nF3 = Toggle Anti-Recoil`nInsert = Show/Hide
    
    Gui, Add, Text, x10 y200 w200 h40, Instructions:`n1. Start MW3/MWZ`n2. Press F1 for rapid fire`n3. Hold RT to rapid fire
    
    ; Apply protection
    Gui, +LastFound +AlwaysOnTop -Caption +ToolWindow
    hWnd := WinExist()
    DllCall("user32.dll\\SetWindowDisplayAffinity", "ptr", hWnd, "uint", 0x00000011)
    WinSet, ExStyle, +0x00200000, ahk_id %hWnd%
    
    Gui, Show, w220 h250, MW3 Mods
}

UpdateGUI() {
    GuiControl,, RapidFireStatus, Rapid Fire: %RapidFireEnabled% ? "ON" : "OFF"
    GuiControl,, SuperJumpStatus, Super Jump: %SuperJumpEnabled% ? "ON" : "OFF"
    GuiControl,, AntiRecoilStatus, Anti-Recoil: %AntiRecoilEnabled% ? "ON" : "OFF"
}

ToggleGUI() {
    static Visible := true
    if (Visible) {
        Gui, Hide
        Visible := false
    } else {
        Gui, Show
        Visible := true
    }
}

; ====== ADVANCED COMBOS ======
; Dropshot: RT + Right Stick Click
Joy1 & Joy10::
    Send, {Ctrl down}  ; Crouch
    Sleep, 200
    Send, {Ctrl up}
return

; Jump Shot: RT + A (while holding RT)
Joy1 & Joy3::
    if (GetKeyState("Joy1", "P")) {
        Send, {Space down}
        Sleep, 100
        Send, {Space up}
    }
return

; ====== STARTUP ======
CreateGUI()
TrayTip, MW3 Controller Mods, Loaded successfully!`nPress F1 for rapid fire`nPress Insert to toggle GUI, 3

; ====== EXIT ======
GuiClose:
ExitApp

; ====== CONTROLLER TEST ======
F12::
    ; Test if controller is working
    if (GetKeyState("Joy1", "P"))
        TrayTip, Controller Test, Right Trigger detected!, 1
    else if (GetKeyState("Joy3", "P"))
        TrayTip, Controller Test, A Button detected!, 1
    else
        TrayTip, Controller Test, No controller input detected, 2
return
