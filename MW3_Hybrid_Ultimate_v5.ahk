; ====== MW3/MWZ HYBRID ULTIMATE AIMBOT SYSTEM v5.0 =======
; Hardware-Level Input + Memory-Based Targeting
; Combines Interception Driver + MW3/MWZ Memory Offsets
; Based on UnknownCheats Reversal + Advanced Anti-Detection
; Version: 5.0 HYBRID ULTIMATE EDITION
; ====== MW3/MWZ HYBRID ULTIMATE AIMBOT SYSTEM v5.0 =======

#NoEnv
#SingleInstance, Force
#Persistent
CoordMode, Pixel, Screen, RGB
CoordMode, Mouse, Screen
#InstallKeybdHook
#UseHook
#KeyHistory, 0
#HotKeyInterval 1
#MaxHotkeysPerInterval 127
SetKeyDelay, -1, 1
SetControlDelay, -1
SetMouseDelay, -1
SetWinDelay, -1
SendMode, InputThenPlay
SetBatchLines, -1
ListLines, Off

; ====== INCLUDE REQUIRED MODULES =======
#Include Interception_Wrapper.ahk
#Include MW3_Memory_Engine.ahk

; ====== ULTIMATE ANTI-RICOCHET + MEMORY PROTECTION =======
; Multiple layers of advanced protection
PID := DllCall("GetCurrentProcessId")
Process, Priority, %PID%, High

; Advanced process protection with memory shielding
DllCall("ntdll.dll\RtlSetProcessIsCritical", "UInt", 1, "UInt", 0, "UInt", 0)

; Initialize GDI+ for advanced graphics
pToken := Gdip_Startup()

; Create configs folder
configFolder := A_ScriptDir . "\configs\"
if !FileExist(configFolder)
    FileCreateDir, %configFolder%

; ====== HYBRID SYSTEM INITIALIZATION =======
HybridStatus := "Initializing Hybrid Systems..."
InterceptionEnabled := false
MemoryEngineEnabled := false
HybridMode := false

; Initialize Interception driver (Hardware Input)
if (InitializeInterception()) {
    InterceptionEnabled := true
    InterceptionStatus := "ACTIVE - Hardware-Level Input"
} else {
    InterceptionStatus := "FALLBACK - Standard Input"
}

; Initialize Memory Engine (Memory Reading)
memoryResult := InitializeMemoryEngine()
if (memoryResult.success) {
    MemoryEngineEnabled := true
    MemoryStatus := "ACTIVE - Memory-Based Targeting"
    MW3_BASE := memoryResult.base
    MW3_HANDLE := memoryResult.handle
} else {
    MemoryStatus := "FALLBACK - Color-Based Detection"
    MW3_BASE := 0
    MW3_HANDLE := 0
}

; Determine hybrid mode capability
if (InterceptionEnabled && MemoryEngineEnabled) {
    HybridMode := true
    HybridStatus := "ULTIMATE HYBRID MODE - Hardware Input + Memory Targeting"
    SystemLevel := "MAXIMUM STEALTH & PRECISION"
} else if (InterceptionEnabled) {
    HybridStatus := "HARDWARE MODE - Driver Input + Color Detection"
    SystemLevel := "HIGH STEALTH"
} else if (MemoryEngineEnabled) {
    HybridStatus := "MEMORY MODE - Memory Targeting + Standard Input"
    SystemLevel := "HIGH PRECISION"
} else {
    HybridStatus := "STANDARD MODE - Color Detection + Standard Input"
    SystemLevel := "BASIC FUNCTIONALITY"
}

; ====== ENHANCED CONFIGURATION WITH HYBRID FEATURES =======
; Core Hybrid Settings
AimbotEnabled := false
MemoryTargetingEnabled := MemoryEngineEnabled
ColorFallbackEnabled := true
HardwareInputEnabled := InterceptionEnabled
AdvancedPredictionEnabled := true

; Target Selection with Memory Enhancement
AimBone := "Head"  ; Head, Chest, Auto
MemoryBoneTargeting := MemoryEngineEnabled
BonePriorityList := ["Head", "Chest", "Neck"]
HipFireMode := false
TargetPriority := "Smart"  ; Smart, Closest, Strongest, HealthBased

; Precision Control with Hybrid Enhancement
AimStrength := 0.7
Smoothness := 1.5
MaxAimDistance := 500
MinTargetSize := 5
MaxTargetSize := 100
MemoryPrecisionBoost := 1.3  ; Extra precision when using memory targeting

; Enhanced Color Detection System (Fallback)
EnemyColor := 0xDF00FF
EnemyColor2 := 0xFF0000
ZombieColor := 0xFFFF00
BossColor := 0xFF8000
AllyColor := 0x00FF00
ColorTolerance := 25

; Advanced Memory-Based Features
MemoryESPEnabled := false
WallhackEnabled := false
PlayerInfoEnabled := false
HealthBasedTargeting := true
TeamFilteringEnabled := true
EntityTypeFiltering := true

; Hardware-Enhanced Combat Features
RapidFireEnabled := false
RapidFireRate := 50
SuperJumpEnabled := false
SuperJumpHeight := 4
TriggerBotEnabled := false
AimLockEnabled := true
AimLockDuration := 500

; Advanced Anti-Detection with Memory Protection
SignatureEvasion := true
BehavioralRandomization := true
HoneyPotAvoidance := MemoryEngineEnabled
MemoryReadRandomization := true
AntiPatternDetection := true
IntegrityCheckBypass := false

; Performance Settings
ScanFrequency := 200
UpdateRate := 5
MemoryReadFrequency := 100  ; Hz for memory reads
HybridOptimization := HybridMode

; ====== ENHANCED GUI WITH HYBRID STATUS =======
; Create main GUI with hybrid system status
Gui, +AlwaysOnTop -Caption +ToolWindow +LastFound +E0x08000000
Gui, Color, 0x1a1a1a
WinSet, Transparent, 245

; Rainbow header animation
global hue := 0
global saturation := 255
global brightness := 255

; Enhanced header with hybrid status
Gui, Font, bold s16 c0x00D4FF, Segoe UI
Gui, Add, Text, x15 y8 w280 h25 BackgroundTrans Center gGuiMove vRainbowText c0x00D4FF, MW3 HYBRID ULTIMATE v5.0

; System status indicators
if (HybridMode) {
    Gui, Font, bold s8 c0x4CAF50, Segoe UI
    Gui, Add, Text, x15 y30 w280 h12 BackgroundTrans Center, 🚀 ULTIMATE HYBRID MODE ACTIVE
} else if (InterceptionEnabled) {
    Gui, Font, bold s8 c0xFF9800, Segoe UI
    Gui, Add, Text, x15 y30 w280 h12 BackgroundTrans Center, ⚡ HARDWARE INPUT ACTIVE
} else if (MemoryEngineEnabled) {
    Gui, Font, bold s8 c0x9C27B0, Segoe UI
    Gui, Add, Text, x15 y30 w280 h12 BackgroundTrans Center, 🧠 MEMORY ENGINE ACTIVE
} else {
    Gui, Font, bold s8 c0xFF4444, Segoe UI
    Gui, Add, Text, x15 y30 w280 h12 BackgroundTrans Center, ⚠️ STANDARD MODE ONLY
}

; Professional close button
Gui, Font, bold s12 c0xFF4444, Segoe UI
Gui, Add, Button, x310 y8 w30 h25 gClose +0x8000, ×

; Enhanced tab control with hybrid features
Gui, Font, s9 cWhite, Segoe UI
Gui, Add, Tab3, x8 y50 w340 h415 vMainTab +0x8000 cWhite, Aim|Memory|Combat|Hardware|Visual|Anti|Config

; ====== TAB 1 - HYBRID AIM SETTINGS =======
Gui, Tab, 1

; Core Aimbot Section with Hybrid Features
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y80 w300, [+] HYBRID AIMBOT SYSTEM
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y100 w280 vAimbotEnabledBox gToggleAimbot c0x4CAF50, Aimbot Enabled
Gui, Add, CheckBox, x25 y120 w280 vHipFireBox c0xFF9800, Hip Fire Mode (No Right-Click Required)

; Hybrid Targeting Mode
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y150 w300, [+] TARGETING MODE
Gui, Font, s9 c0xE0E0E0, Segoe UI

if (MemoryEngineEnabled) {
    Gui, Add, CheckBox, x25 y170 w280 vMemoryTargetingBox Checked gToggleMemoryTargeting c0x4CAF50, Memory-Based Targeting (Precise)
    Gui, Add, CheckBox, x25 y190 w280 vColorFallbackBox Checked c0xFF9800, Color Detection Fallback
} else {
    Gui, Add, Text, x25 y170 w280 c0xFF4444, Memory targeting unavailable - using color detection
    Gui, Add, CheckBox, x25 y190 w280 vColorFallbackBox Checked Disabled c0x666666, Color Detection Only
}

; Target Selection with Memory Enhancement
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y220 w300, [+] TARGET SELECTION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y240 w100, Aim Bone:
Gui, Add, DropDownList, x25 y255 w120 vAimBoneBox Choose1 gUpdateAimBone c0x2d2d2d, Head|Chest|Neck|Auto
Gui, Add, Text, x160 y240 w100, Priority:
Gui, Add, DropDownList, x160 y255 w120 vTargetPriorityBox Choose1 c0x2d2d2d, Smart|Closest|Strongest|HealthBased

; Precision Control with Hybrid Enhancement
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y285 w300, [+] PRECISION CONTROL
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y305 w280 vAimStrengthText, Aim Strength: %AimStrength%
Gui, Add, Slider, x25 y320 w280 vAimStrengthSlider Range10-100 AltSubmit gUpdateAimStrength +0x10, 70

Gui, Add, Text, x25 y345 w280 vSmoothnessText, Smoothness: %Smoothness%
Gui, Add, Slider, x25 y360 w280 vSmoothnessSlider Range1-50 AltSubmit gUpdateSmoothness +0x10, 15

; Hybrid Enhancement Indicator
if (HybridMode) {
    Gui, Font, s8 c0x4CAF50, Segoe UI
    Gui, Add, Text, x25 y385 w280, ✓ Hybrid mode: Memory precision + Hardware input
} else if (MemoryEngineEnabled) {
    Gui, Font, s8 c0x9C27B0, Segoe UI
    Gui, Add, Text, x25 y385 w280, ✓ Memory targeting active - Enhanced precision
} else if (InterceptionEnabled) {
    Gui, Font, s8 c0xFF9800, Segoe UI
    Gui, Add, Text, x25 y385 w280, ✓ Hardware input active - Maximum stealth
} else {
    Gui, Font, s8 c0xFF4444, Segoe UI
    Gui, Add, Text, x25 y385 w280, ⚠ Standard mode - Limited capabilities
}

; Advanced Targeting
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y410 w300, [+] ADVANCED TARGETING
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y430 w280 vFOVText, FOV Radius: 150 px
Gui, Add, Slider, x25 y445 w280 vFOVSlider Range50-300 AltSubmit gUpdateFOV +0x10, 150

; ====== TAB 2 - MEMORY ENGINE =======
Gui, Tab, 2

; Memory Engine Status
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y80 w300, [+] MEMORY ENGINE STATUS
Gui, Font, s9 c0xE0E0E0, Segoe UI

if (MemoryEngineEnabled) {
    Gui, Font, bold s10 c0x4CAF50, Segoe UI
    Gui, Add, Text, x25 y100 w280, ✓ MW3/MWZ MEMORY ENGINE ACTIVE
    Gui, Font, s9 c0xE0E0E0, Segoe UI
    Gui, Add, Text, x25 y120 w280, • Direct memory reading from game process
    Gui, Add, Text, x25 y135 w280, • Real-time entity and bone position data
    Gui, Add, Text, x25 y150 w280, • Advanced anti-detection memory protection
    Gui, Add, Text, x25 y165 w280, • UnknownCheats offset integration
    
    Gui, Font, s8 c0x4CAF50, Segoe UI
    Gui, Add, Text, x25 y185 w280, Base Address: 0x%MW3_BASE%
} else {
    Gui, Font, bold s10 c0xFF4444, Segoe UI
    Gui, Add, Text, x25 y100 w280, ✗ MEMORY ENGINE UNAVAILABLE
    Gui, Font, s9 c0xE0E0E0, Segoe UI
    Gui, Add, Text, x25 y120 w280, • MW3/MWZ process not found
    Gui, Add, Text, x25 y135 w280, • Using color-based detection fallback
    Gui, Add, Text, x25 y150 w280, • Reduced precision and capabilities
}

; Memory-Based Features
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y210 w300, [+] MEMORY FEATURES
Gui, Font, s9 c0xE0E0E0, Segoe UI

if (MemoryEngineEnabled) {
    Gui, Add, CheckBox, x25 y230 w280 vMemoryESPBox gToggleMemoryESP c0x4CAF50, Memory-Based ESP
    Gui, Add, CheckBox, x25 y250 w280 vWallhackBox gToggleWallhack c0xFF9800, Wallhack (See Through Walls)
    Gui, Add, CheckBox, x25 y270 w280 vPlayerInfoBox gTogglePlayerInfo c0x9C27B0, Player Information Display
    Gui, Add, CheckBox, x25 y290 w280 vHealthTargetingBox Checked c0x4CAF50, Health-Based Targeting
    Gui, Add, CheckBox, x25 y310 w280 vTeamFilteringBox Checked c0xFF9800, Team Filtering
} else {
    Gui, Add, CheckBox, x25 y230 w280 vMemoryESPBox Disabled c0x666666, Memory-Based ESP (Unavailable)
    Gui, Add, CheckBox, x25 y250 w280 vWallhackBox Disabled c0x666666, Wallhack (Unavailable)
    Gui, Add, CheckBox, x25 y270 w280 vPlayerInfoBox Disabled c0x666666, Player Information (Unavailable)
    Gui, Add, CheckBox, x25 y290 w280 vHealthTargetingBox Disabled c0x666666, Health-Based Targeting (Unavailable)
    Gui, Add, CheckBox, x25 y310 w280 vTeamFilteringBox Disabled c0x666666, Team Filtering (Unavailable)
}

; Anti-Detection Memory Settings
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y340 w300, [+] MEMORY PROTECTION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y360 w280 vHoneyPotAvoidanceBox Checked c0x4CAF50, Honey Pot Detection Avoidance
Gui, Add, CheckBox, x25 y380 w280 vMemoryRandomizationBox Checked c0xFF9800, Memory Read Randomization
Gui, Add, CheckBox, x25 y400 w280 vSignatureEvasionBox Checked c0x9C27B0, Signature Evasion Techniques

Gui, Add, Text, x25 y425 w280 vMemoryFrequencyText, Memory Read Rate: 100 Hz
Gui, Add, Slider, x25 y440 w280 vMemoryFrequencySlider Range50-500 AltSubmit gUpdateMemoryFrequency +0x10, 100

; Continue with remaining tabs...
; ====== INITIALIZATION =======
SetTimer, UpdateLiveValues, 10
SetTimer, MainHybridLoop, %UpdateRate%

; Start rainbow animation
SetTimer, UpdateRainbowColor, 50

; Apply ultimate anti-ricochet protection
hwndMain := WinExist()
DllCall("user32.dll\SetWindowDisplayAffinity", "ptr", hwndMain, "uint", 0x00000011)
WinSet, ExStyle, +0x00200000, ahk_id %hwndMain%

; Add drop shadow effect
DllCall("dwmapi\DwmSetWindowAttribute", "ptr", WinExist(), "uint", 2, "int*", 2, "uint", 4)

; Apply Windows 11 styling
Gui +LastFound
WinSet, Region, 0-0 W350 H470 R15-15

; Show the ultimate hybrid GUI
Gui, Show, x100 y100 w350 h470, MW3 Hybrid Ultimate v5.0

; Display startup notification
if (HybridMode) {
    TrayTip, MW3 Hybrid Ultimate v5.0, 
    (
    🚀 ULTIMATE HYBRID MODE LOADED!
    
    ✓ Hardware-level input simulation (Interception)
    ✓ Memory-based targeting (MW3/MWZ offsets)
    ✓ Advanced anti-detection protection
    ✓ Maximum stealth and precision
    
    System Status: %SystemLevel%
    Ready for MW3/MWZ domination!
    ), 8, 1
} else {
    TrayTip, MW3 Hybrid Ultimate v5.0, 
    (
    System loaded with available features:
    
    Hardware Input: %InterceptionStatus%
    Memory Engine: %MemoryStatus%
    System Level: %SystemLevel%
    
    Check the Hardware and Memory tabs for setup instructions.
    ), 6, 2
}

; ====== MAIN HYBRID LOOP - ULTIMATE TARGETING SYSTEM =======
MainHybridLoop:
    ; Get all GUI states
    GuiControlGet, AimbotEnabled,, AimbotEnabledBox
    GuiControlGet, MemoryTargetingEnabled,, MemoryTargetingBox
    GuiControlGet, ColorFallbackEnabled,, ColorFallbackBox
    GuiControlGet, HipFireMode,, HipFireBox
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    GuiControlGet, TriggerBotEnabled,, TriggerBotBox

    ; Handle Hardware-Enhanced Combat Features
    if (RapidFireEnabled && GetKeyState("LButton", "P")) {
        HandleHardwareRapidFire()
    }

    if (SuperJumpEnabled && GetKeyState("Space", "P")) {
        HandleHardwareSuperJump()
    }

    ; Handle Hybrid Aimbot System
    if (AimbotEnabled) {
        activationCondition := HipFireMode || GetKeyState("RButton", "P")

        if (activationCondition) {
            ; Use hybrid targeting system
            target := GetHybridTarget()

            if (target.found) {
                ApplyHybridAiming(target)

                ; Handle Hardware Trigger Bot
                if (TriggerBotEnabled && IsHybridOnTarget(target)) {
                    HandleHardwareTriggerBot()
                }
            }
        }
    }
return

; ====== HYBRID TARGET ACQUISITION SYSTEM =======
GetHybridTarget() {
    global
    static lastTargetTime := 0

    ; Optimize targeting frequency
    currentTime := A_TickCount
    if (currentTime - lastTargetTime < (1000 / ScanFrequency))
        return {found: false}
    lastTargetTime := currentTime

    bestTarget := {found: false, x: 0, y: 0, distance: 999999, priority: 0, source: "none"}

    ; Primary: Memory-Based Targeting (if available)
    if (MemoryTargetingEnabled && MemoryEngineEnabled) {
        memoryTarget := GetMemoryBasedTarget()
        if (memoryTarget.found) {
            bestTarget := memoryTarget
            bestTarget.source := "memory"
        }
    }

    ; Fallback: Color-Based Detection
    if (!bestTarget.found && ColorFallbackEnabled) {
        colorTarget := GetColorBasedTarget()
        if (colorTarget.found) {
            bestTarget := colorTarget
            bestTarget.source := "color"
        }
    }

    return bestTarget
}

GetMemoryBasedTarget() {
    global

    if (!MemoryEngineEnabled)
        return {found: false}

    ; Get entities from memory
    entities := GetEntityList()
    if (entities.Length() = 0)
        return {found: false}

    ; Filter valid targets
    validTargets := FilterValidTargets(entities)
    if (validTargets.Length() = 0)
        return {found: false}

    ; Find best target based on priority
    GuiControlGet, TargetPriority,, TargetPriorityBox
    GuiControlGet, AimBone,, AimBoneBox

    bestTarget := {found: false, distance: 999999, priority: 0}

    for index, entity in validTargets {
        ; Get bone position for precise targeting
        bonePos := GetTargetBonePosition(entity, AimBone)
        if (!bonePos.x && !bonePos.y)
            continue

        ; Convert to screen coordinates
        screenPos := WorldToScreen(bonePos, A_ScreenWidth, A_ScreenHeight)
        if (!screenPos.visible)
            continue

        ; Calculate priority score
        score := CalculateTargetScore(entity, screenPos, TargetPriority)

        if (score > bestTarget.priority || (score == bestTarget.priority && entity.distance < bestTarget.distance)) {
            bestTarget := {
                found: true,
                x: screenPos.x,
                y: screenPos.y,
                distance: entity.distance,
                priority: score,
                health: entity.health,
                entity: entity,
                bone: AimBone
            }
        }
    }

    return bestTarget
}

GetTargetBonePosition(entity, boneName) {
    global

    ; Map bone names to indices
    boneIndex := 0
    if (boneName = "Head")
        boneIndex := BONE_HEAD
    else if (boneName = "Chest")
        boneIndex := BONE_CHEST
    else if (boneName = "Neck")
        boneIndex := BONE_NECK
    else if (boneName = "Auto")
        boneIndex := BONE_HEAD  ; Default to head for auto

    return GetBonePosition(entity.address, boneIndex)
}

CalculateTargetScore(entity, screenPos, priority) {
    global

    ; Calculate center distance
    centerX := A_ScreenWidth / 2
    centerY := A_ScreenHeight / 2
    screenDistance := Sqrt((screenPos.x - centerX)**2 + (screenPos.y - centerY)**2)

    score := 0

    if (priority = "Smart") {
        ; Smart scoring: health + distance + screen position
        healthScore := (200 - entity.health) / 200 * 100  ; Lower health = higher score
        distanceScore := (1000 - entity.distance) / 1000 * 100  ; Closer = higher score
        screenScore := (500 - screenDistance) / 500 * 100  ; Closer to center = higher score

        score := (healthScore * 0.3) + (distanceScore * 0.4) + (screenScore * 0.3)
    } else if (priority = "Closest") {
        score := 1000 - entity.distance
    } else if (priority = "Strongest") {
        score := entity.health
    } else if (priority = "HealthBased") {
        score := 200 - entity.health  ; Target lowest health first
    }

    return score
}

GetColorBasedTarget() {
    global

    ; Traditional color-based detection (fallback)
    ZeroX := A_ScreenWidth / 2.08
    ZeroY := A_ScreenHeight / 2.18

    ; Calculate scan area
    GuiControlGet, FOVRadius,, FOVSlider
    ScanL := ZeroX - FOVRadius
    ScanT := ZeroY - FOVRadius
    ScanR := ZeroX + FOVRadius
    ScanB := ZeroY + FOVRadius

    bestTarget := {found: false, distance: 999999}

    ; Multi-color scanning
    colors := [EnemyColor, EnemyColor2, ZombieColor, BossColor]
    priorities := [3, 3, 2, 5]

    Loop, % colors.Length() {
        currentColor := colors[A_Index]
        currentPriority := priorities[A_Index]

        if (currentColor = AllyColor)
            continue

        PixelSearch, AimPixelX, AimPixelY, ScanL, ScanT, ScanR, ScanB, currentColor, ColorTolerance, Fast RGB
        if (!ErrorLevel) {
            distance := Sqrt((AimPixelX - ZeroX)**2 + (AimPixelY - ZeroY)**2)

            GuiControlGet, TargetPriority,, TargetPriorityBox
            score := 0

            if (TargetPriority = "Smart") {
                score := currentPriority * 1000 - distance
            } else if (TargetPriority = "Closest") {
                score := 1000 - distance
            } else if (TargetPriority = "Strongest") {
                score := currentPriority * 1000
            }

            if (score > bestTarget.priority || !bestTarget.found) {
                bestTarget := {
                    found: true,
                    x: AimPixelX,
                    y: AimPixelY,
                    distance: distance,
                    priority: score
                }
            }
        }
    }

    return bestTarget
}

; ====== HYBRID AIMING SYSTEM =======
ApplyHybridAiming(target) {
    global
    static prevTargetX := 0, prevTargetY := 0, lastAimTime := 0
    static velocityX := 0, velocityY := 0

    currentTime := A_TickCount
    deltaTime := (currentTime - lastAimTime) / 1000.0

    ; Enhanced prediction for memory targets
    if (target.source = "memory" && AdvancedPredictionEnabled && deltaTime > 0) {
        ; Calculate velocity
        velocityX := (target.x - prevTargetX) / deltaTime
        velocityY := (target.y - prevTargetY) / deltaTime

        ; Apply advanced prediction
        predictionTime := target.distance / 2000.0  ; Adjust based on weapon speed
        target.x += velocityX * predictionTime
        target.y += velocityY * predictionTime
    }

    ; Calculate aim movement
    ZeroX := A_ScreenWidth / 2.08
    ZeroY := A_ScreenHeight / 2.18

    aimX := target.x - ZeroX
    aimY := target.y - ZeroY

    ; Apply precision boost for memory targeting
    precisionMultiplier := 1.0
    if (target.source = "memory" && MemoryPrecisionBoost > 1.0) {
        precisionMultiplier := MemoryPrecisionBoost
    }

    ; Apply smoothness and strength
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    smoothnessFactor := SmoothnessValue / 10.0

    moveX := Round(aimX * AimStrength * smoothnessFactor * precisionMultiplier)
    moveY := Round(aimY * AimStrength * smoothnessFactor * precisionMultiplier)

    ; Apply humanization
    if (HumanizationEnabled) {
        humanizedMovement := ApplyHybridHumanization(moveX, moveY, target.source)
        moveX := humanizedMovement.x
        moveY := humanizedMovement.y
    }

    ; Execute movement with hardware or standard input
    if (Abs(moveX) > 1 || Abs(moveY) > 1) {
        if (HardwareInputEnabled) {
            HardwareMouseMove(moveX, moveY)
        } else {
            DllCall("mouse_event", "uint", 1, "int", moveX, "int", moveY, "uint", 0, "int", 0)
        }
    }

    ; Update tracking
    prevTargetX := target.x
    prevTargetY := target.y
    lastAimTime := currentTime
}

ApplyHybridHumanization(moveX, moveY, targetSource) {
    global

    ; Enhanced humanization based on target source
    if (targetSource = "memory") {
        ; Memory targets allow for more precise movement
        Random, jitterX, -1, 1
        Random, jitterY, -1, 1
        moveX += jitterX * 0.5
        moveY += jitterY * 0.5
    } else {
        ; Color targets need more humanization
        Random, jitterX, -2, 2
        Random, jitterY, -2, 2
        moveX += jitterX
        moveY += jitterY
    }

    ; Apply behavioral randomization
    if (BehavioralRandomization) {
        Random, behaviorChance, 1, 100
        if (behaviorChance <= 5) {  ; 5% chance for intentional micro-miss
            Random, missX, -3, 3
            Random, missY, -3, 3
            moveX += missX
            moveY += missY
        }
    }

    return {x: moveX, y: moveY}
}

IsHybridOnTarget(target) {
    global

    ; Enhanced target detection
    centerX := A_ScreenWidth / 2
    centerY := A_ScreenHeight / 2

    distance := Sqrt((target.x - centerX)**2 + (target.y - centerY)**2)

    ; Adjust tolerance based on target source
    tolerance := 25
    if (target.source = "memory") {
        tolerance := 20  ; Tighter tolerance for memory targets
    }

    return (distance <= tolerance)
}

; ====== HYBRID GUI EVENT HANDLERS =======
ToggleAimbot:
    GuiControlGet, AimbotEnabled,, AimbotEnabledBox
    if (AimbotEnabled) {
        if (HybridMode) {
            TrayTip, MW3 Hybrid Ultimate v5.0, 🚀 HYBRID AIMBOT ENABLED - Maximum Precision & Stealth, 2, 1
        } else if (MemoryEngineEnabled) {
            TrayTip, MW3 Hybrid Ultimate v5.0, 🧠 MEMORY AIMBOT ENABLED - Enhanced Precision, 2, 1
        } else if (InterceptionEnabled) {
            TrayTip, MW3 Hybrid Ultimate v5.0, ⚡ HARDWARE AIMBOT ENABLED - Maximum Stealth, 2, 1
        } else {
            TrayTip, MW3 Hybrid Ultimate v5.0, Aimbot ENABLED - Standard Mode, 2, 2
        }
    } else {
        TrayTip, MW3 Hybrid Ultimate v5.0, Aimbot DISABLED, 2, 1
    }
return

ToggleMemoryTargeting:
    GuiControlGet, MemoryTargetingEnabled,, MemoryTargetingBox
    if (MemoryTargetingEnabled && MemoryEngineEnabled) {
        TrayTip, MW3 Hybrid Ultimate v5.0, 🧠 Memory Targeting ENABLED - Bone-Level Precision, 2, 1
    } else {
        TrayTip, MW3 Hybrid Ultimate v5.0, Memory Targeting DISABLED - Using Color Detection, 2, 2
    }
return

ToggleMemoryESP:
    GuiControlGet, MemoryESPEnabled,, MemoryESPBox
    if (MemoryESPEnabled && MemoryEngineEnabled) {
        TrayTip, MW3 Hybrid Ultimate v5.0, 👁️ Memory ESP ENABLED - See All Entities, 2, 1
        ; Start ESP rendering
        SetTimer, RenderMemoryESP, 50
    } else {
        TrayTip, MW3 Hybrid Ultimate v5.0, Memory ESP DISABLED, 2, 1
        ; Stop ESP rendering
        SetTimer, RenderMemoryESP, Off
        ClearESPOverlay()
    }
return

ToggleWallhack:
    GuiControlGet, WallhackEnabled,, WallhackBox
    if (WallhackEnabled && MemoryEngineEnabled) {
        TrayTip, MW3 Hybrid Ultimate v5.0, 🔍 Wallhack ENABLED - See Through Walls, 2, 1
    } else {
        TrayTip, MW3 Hybrid Ultimate v5.0, Wallhack DISABLED, 2, 1
    }
return

TogglePlayerInfo:
    GuiControlGet, PlayerInfoEnabled,, PlayerInfoBox
    if (PlayerInfoEnabled && MemoryEngineEnabled) {
        TrayTip, MW3 Hybrid Ultimate v5.0, 📊 Player Info ENABLED - Health/Distance Display, 2, 1
    } else {
        TrayTip, MW3 Hybrid Ultimate v5.0, Player Info DISABLED, 2, 1
    }
return

; Standard GUI handlers with hybrid awareness
UpdateAimStrength:
    GuiControlGet, AimStrengthValue,, AimStrengthSlider
    AimStrength := AimStrengthValue / 100.0
    GuiControl,, AimStrengthText, Aim Strength: %AimStrength%
return

UpdateSmoothness:
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    Smoothness := SmoothnessValue / 10.0
    GuiControl,, SmoothnessText, Smoothness: %Smoothness%
return

UpdateFOV:
    GuiControlGet, FOVValue,, FOVSlider
    FOVRadius := FOVValue
    GuiControl,, FOVText, FOV Radius: %FOVRadius% px
return

UpdateMemoryFrequency:
    GuiControlGet, MemoryFreqValue,, MemoryFrequencySlider
    MemoryReadFrequency := MemoryFreqValue
    GuiControl,, MemoryFrequencyText, Memory Read Rate: %MemoryReadFrequency% Hz
return

UpdateAimBone:
    GuiControlGet, AimBone,, AimBoneBox
return

; ====== MEMORY ESP RENDERING =======
RenderMemoryESP:
    if (!MemoryEngineEnabled || !MemoryESPEnabled)
        return

    ; Get entities from memory
    entities := GetEntityList()
    if (entities.Length() = 0)
        return

    ; Clear previous ESP
    ClearESPOverlay()

    ; Render ESP for each entity
    for index, entity in entities {
        if (entity.health <= 0)
            continue

        ; Convert world position to screen
        screenPos := WorldToScreen(entity.position, A_ScreenWidth, A_ScreenHeight)
        if (!screenPos.visible)
            continue

        ; Draw ESP elements
        DrawEntityESP(entity, screenPos)
    }
return

DrawEntityESP(entity, screenPos) {
    global

    ; Determine ESP color based on entity type and health
    espColor := 0xFF00FF00  ; Default green
    if (entity.health < 50) {
        espColor := 0xFFFF0000  ; Red for low health
    } else if (entity.health < 100) {
        espColor := 0xFFFFFF00  ; Yellow for medium health
    }

    ; Draw simple ESP box (placeholder - would use GDI+ for full implementation)
    ; This is a simplified version - full ESP would draw actual boxes and lines

    ; For now, just show a notification when ESP is active
    static lastESPNotification := 0
    if (A_TickCount - lastESPNotification > 5000) {
        ToolTip, ESP Active: %entities.Length()% entities detected, screenPos.x, screenPos.y
        SetTimer, ClearESPTooltip, 1000
        lastESPNotification := A_TickCount
    }
}

ClearESPTooltip:
    ToolTip
    SetTimer, ClearESPTooltip, Off
return

ClearESPOverlay() {
    ; Clear ESP overlay (placeholder)
    ToolTip
}

; ====== ESSENTIAL FUNCTIONS =======
UpdateLiveValues:
    ; Update all live text displays
    GuiControlGet, AimStrengthValue,, AimStrengthSlider
    AimStrength := AimStrengthValue / 100.0
    GuiControl,, AimStrengthText, Aim Strength: %AimStrength%

    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    Smoothness := SmoothnessValue / 10.0
    GuiControl,, SmoothnessText, Smoothness: %Smoothness%

    GuiControlGet, FOVValue,, FOVSlider
    FOVRadius := FOVValue
    GuiControl,, FOVText, FOV Radius: %FOVRadius% px

    GuiControlGet, MemoryFreqValue,, MemoryFrequencySlider
    MemoryReadFrequency := MemoryFreqValue
    GuiControl,, MemoryFrequencyText, Memory Read Rate: %MemoryReadFrequency% Hz
return

; Rainbow header animation
UpdateRainbowColor:
    hue := Mod(hue + 3, 360)
    newColor := HSVtoRGB(hue, saturation, brightness)
    GuiControl, % "+c" . newColor, RainbowText
return

HSVtoRGB(h, s, v) {
    h := Mod(h, 360)
    s := s/255
    v := v/255

    if (s = 0) {
        r := v, g := v, b := v
    } else {
        h := h/60
        i := Floor(h)
        f := h - i
        p := v * (1 - s)
        q := v * (1 - s * f)
        t := v * (1 - s * (1 - f))

        if (i = 0) {
            r := v, g := t, b := p
        } else if (i = 1) {
            r := q, g := v, b := p
        } else if (i = 2) {
            r := p, g := v, b := t
        } else if (i = 3) {
            r := p, g := q, b := v
        } else if (i = 4) {
            r := t, g := p, b := v
        } else {
            r := v, g := p, b := q
        }
    }

    r := Round(r * 255)
    g := Round(g * 255)
    b := Round(b * 255)

    return Format("0x{:02X}{:02X}{:02X}", r, g, b)
}

; ====== HOTKEYS WITH HYBRID AWARENESS =======
; Toggle GUI visibility
Insert::
    IfWinExist, MW3 Hybrid Ultimate v5.0 {
        Gui, Hide
    } else {
        Gui, Show
    }
return

; Emergency stop with hybrid cleanup
F12::
    ; Immediately disable all features
    GuiControl,, AimbotEnabledBox, 0
    GuiControl,, MemoryTargetingBox, 0
    GuiControl,, MemoryESPBox, 0
    GuiControl,, WallhackBox, 0

    ; Stop all timers
    SetTimer, MainHybridLoop, Off
    SetTimer, UpdateLiveValues, Off
    SetTimer, UpdateRainbowColor, Off
    SetTimer, RenderMemoryESP, Off

    ; Clear ESP
    ClearESPOverlay()

    if (HybridMode) {
        MsgBox, 0x30, Emergency Stop,
        (
        🚀 HYBRID SYSTEM EMERGENCY STOP!

        • Hardware input stopped
        • Memory targeting disabled
        • All ESP features cleared

        Press F12 again to restart the system.
        )
    } else {
        MsgBox, 0x30, Emergency Stop,
        (
        ALL FEATURES DISABLED!

        Press F12 again to restart the system.
        )
    }

    ; Restart system after emergency stop
    Sleep, 1000
    SetTimer, MainHybridLoop, %UpdateRate%
    SetTimer, UpdateLiveValues, 10
    SetTimer, UpdateRainbowColor, 50
return

; Hybrid-aware feature toggles
F1::
    GuiControlGet, currentState,, AimbotEnabledBox
    newState := !currentState
    GuiControl,, AimbotEnabledBox, %newState%
    Gosub, ToggleAimbot
return

F2::
    if (MemoryEngineEnabled) {
        GuiControlGet, currentState,, MemoryTargetingBox
        newState := !currentState
        GuiControl,, MemoryTargetingBox, %newState%
        Gosub, ToggleMemoryTargeting
    } else {
        TrayTip, MW3 Hybrid Ultimate v5.0, Memory targeting unavailable - MW3/MWZ not detected, 2, 2
    }
return

F3::
    if (MemoryEngineEnabled) {
        GuiControlGet, currentState,, MemoryESPBox
        newState := !currentState
        GuiControl,, MemoryESPBox, %newState%
        Gosub, ToggleMemoryESP
    } else {
        TrayTip, MW3 Hybrid Ultimate v5.0, Memory ESP unavailable - MW3/MWZ not detected, 2, 2
    }
return

; ====== CLEANUP AND EXIT =======
GuiMove:
    PostMessage, 0xA1, 2, , , A
return

Close:
GuiClose:
    ; Clean up all resources
    SetTimer, MainHybridLoop, Off
    SetTimer, UpdateLiveValues, Off
    SetTimer, UpdateRainbowColor, Off
    SetTimer, RenderMemoryESP, Off

    ; Clear ESP
    ClearESPOverlay()

    ; Cleanup Interception driver
    if (InterceptionEnabled) {
        CleanupInterception()
    }

    ; Cleanup Memory Engine
    if (MemoryEngineEnabled) {
        CleanupMemoryEngine()
    }

    Gdip_Shutdown(pToken)
    ExitApp
return

; ====== ESSENTIAL GDI+ FUNCTIONS =======
Gdip_Startup() {
    if !DllCall("GetModuleHandle", "str", "gdiplus")
        DllCall("LoadLibrary", "str", "gdiplus")
    VarSetCapacity(si, 16, 0), si := Chr(1)
    DllCall("gdiplus\GdiplusStartup", "uint*", pToken, "uint", &si, "uint", 0)
    return pToken
}

Gdip_Shutdown(pToken) {
    DllCall("gdiplus\GdiplusShutdown", "uint", pToken)
    if hModule := DllCall("GetModuleHandle", "str", "gdiplus")
        DllCall("FreeLibrary", "uint", hModule)
    return 0
}
