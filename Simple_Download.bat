@echo off
title MW3 AI Ultimate - Simple Interception Download
color 0A

echo.
echo ========================================
echo    MW3 AI ULTIMATE - INTERCEPTION
echo ========================================
echo.

echo SIMPLE DOWNLOAD METHOD:
echo.
echo 1. Open your web browser
echo 2. Go to: https://github.com/oblitum/Interception/releases
echo 3. Download "Interception.zip" (NOT source code)
echo 4. Extract it to this folder
echo 5. Find and copy these files here:
echo    - install-interception.exe
echo    - uninstall-interception.exe
echo    - interception.dll
echo.

echo ========================================
echo CURRENT STATUS:
echo ========================================
echo.

if exist "install-interception.exe" (
    echo [✓] install-interception.exe - FOUND
    set "installer_found=1"
) else (
    echo [✗] install-interception.exe - MISSING
    set "installer_found=0"
)

if exist "uninstall-interception.exe" (
    echo [✓] uninstall-interception.exe - FOUND
) else (
    echo [✗] uninstall-interception.exe - MISSING
)

if exist "interception.dll" (
    echo [✓] interception.dll - FOUND
    set "dll_found=1"
) else (
    echo [✗] interception.dll - MISSING
    set "dll_found=0"
)

echo.

if "%installer_found%"=="1" if "%dll_found%"=="1" (
    echo ========================================
    echo [✓] READY TO INSTALL DRIVER!
    echo ========================================
    echo.
    echo You have the required files!
    echo.
    echo INSTALL DRIVER NOW?
    echo.
    echo WARNING: This requires Administrator privileges
    echo          and you'll need to restart your computer
    echo.
    choice /c YN /m "Install Interception driver now (Y/N)"
    
    if errorlevel 2 goto :skip_install
    if errorlevel 1 goto :install_driver
    
    :install_driver
    echo.
    echo Installing Interception driver...
    echo.
    echo If UAC prompt appears, click YES
    echo.
    powershell -Command "Start-Process 'install-interception.exe' -Verb RunAs -Wait"
    
    if %errorlevel% == 0 (
        echo.
        echo [✓] Driver installation completed!
        echo.
        echo ========================================
        echo    RESTART REQUIRED!
        echo ========================================
        echo.
        echo Please restart your computer now.
        echo After restart, you can run the MW3 AI Ultimate System.
        echo.
    ) else (
        echo.
        echo [✗] Driver installation may have failed.
        echo Try right-clicking install-interception.exe
        echo and selecting "Run as administrator"
        echo.
    )
    
    :skip_install
    echo.
    echo Next steps after you have the files:
    echo 1. Right-click install-interception.exe
    echo 2. Select "Run as administrator"
    echo 3. Restart your computer
    echo 4. Run MW3 AI Ultimate System
    echo.
    
) else (
    echo ========================================
    echo [!] FILES MISSING
    echo ========================================
    echo.
    echo Please download the files first:
    echo.
    echo DIRECT LINK:
    echo https://github.com/oblitum/Interception/releases/download/v1.0.1/Interception.zip
    echo.
    echo OR try this alternative:
    echo 1. Press Windows key + R
    echo 2. Type: https://github.com/oblitum/Interception/releases
    echo 3. Press Enter
    echo 4. Download Interception.zip
    echo.
    
    choice /c YN /m "Open download page in browser now (Y/N)"
    
    if errorlevel 2 goto :end
    if errorlevel 1 (
        echo Opening browser...
        start https://github.com/oblitum/Interception/releases
    )
)

:end
echo.
echo ========================================
echo Press any key to exit...
pause >nul
