#include <windows.h>

#define VER_DEBUG                    0
#define VER_PRERELEASE               0
#define VER_FILEFLAGSMASK            VS_FFI_FILEFLAGSMASK
#define VER_FILEOS                   VOS_NT_WINDOWS32
#define VER_FILEFLAGS                (VER_PRERELEASE|VER_DEBUG)

#define VER_FILETYPE                 VFT_DLL
#define VER_FILESUBTYPE              VFT2_UNKNOWN

#define VER_COMPANYNAME_STR          "Francisco Lopes"
#define VER_PRODUCTNAME_STR          "Interception"
#define VER_LEGALCOPYRIGHT_YEARS     "2008-2017"
#define VER_LEGALCOPYRIGHT_STR       "Copyright (C) " VER_LEGALCOPYRIGHT_YEARS " Francisco Lopes da Silva"
#define VER_LEGALTRADEMARKS_STR      "Copyright (C) " VER_LEGALCOPYRIGHT_YEARS " Francisco Lopes da Silva"

#define VER_PRODUCTVERSION           1,0,1,0
#define VER_PRODUCTVERSION_STR       "1.0.1"
#define VER_PRODUCTVERSION_W         (0x010001)
#define VER_PRODUCTVERSION_DW        (0x010001)
#define VER_FILEDESCRIPTION_STR      "Interception API"
#define VER_INTERNALNAME_STR         "interception.dll"
#define VER_ORIGINALFILENAME_STR     "interception.dll"

#include "common.ver"
