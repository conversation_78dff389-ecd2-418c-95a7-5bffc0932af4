#!/usr/bin/env python3
"""
MW3/MWZ Memory Scanner
Based on Black Ops 6 reversal techniques - adapted for MW3/MWZ

This tool scans for common game data patterns like health, ammo, coordinates
"""

import psutil
import struct
import time
from typing import Optional, List, Tuple
import ctypes
from ctypes import wintypes

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_VM_READ = 0x0010

class MemoryScanner:
    def __init__(self, process_name: str = "cod.exe"):
        self.process_name = process_name
        self.process = None
        self.handle = None
        
    def find_process(self) -> bool:
        """Find the MW3/MWZ process"""
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'].lower() == self.process_name.lower():
                self.process = proc
                # Open process handle for memory reading
                self.handle = ctypes.windll.kernel32.OpenProcess(
                    PROCESS_VM_READ, False, proc.info['pid']
                )
                print(f"Found process: {proc.info['name']} (PID: {proc.info['pid']})")
                return True
        return False
    
    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """Read memory from the process"""
        if not self.handle:
            return None
            
        buffer = ctypes.create_string_buffer(size)
        bytes_read = ctypes.c_size_t()
        
        success = ctypes.windll.kernel32.ReadProcessMemory(
            self.handle, address, buffer, size, ctypes.byref(bytes_read)
        )
        
        if success:
            return buffer.raw[:bytes_read.value]
        return None
    
    def scan_for_pattern(self, pattern: bytes, start_addr: int = 0x10000000, 
                        end_addr: int = 0x7FFFFFFF) -> List[int]:
        """Scan memory for a specific byte pattern"""
        found_addresses = []
        chunk_size = 0x1000  # 4KB chunks
        
        current_addr = start_addr
        while current_addr < end_addr:
            try:
                data = self.read_memory(current_addr, chunk_size)
                if data:
                    offset = 0
                    while True:
                        pos = data.find(pattern, offset)
                        if pos == -1:
                            break
                        found_addresses.append(current_addr + pos)
                        offset = pos + 1
                        
                current_addr += chunk_size
                
            except Exception:
                current_addr += chunk_size
                continue
                
        return found_addresses
    
    def find_health_addresses(self) -> List[int]:
        """
        Find potential health addresses
        Based on BO6: 993FC48 + 1E8 //Main Health
        Look for common health values (100, 150, 200)
        """
        print("Scanning for health values...")
        addresses = []
        
        # Scan for common health values as 4-byte integers
        for health_val in [100, 150, 200, 250]:
            pattern = struct.pack('<I', health_val)  # Little-endian 4-byte int
            found = self.scan_for_pattern(pattern)
            addresses.extend(found)
            print(f"Found {len(found)} potential addresses for health value {health_val}")
            
        return addresses
    
    def find_ammo_addresses(self) -> List[int]:
        """
        Find potential ammo addresses
        Based on BO6: B9742E8 + 18F8 //Main Ammo Clip
        Look for common ammo values (30, 60, 90, etc.)
        """
        print("Scanning for ammo values...")
        addresses = []
        
        # Common ammo counts for different weapon types
        ammo_values = [30, 60, 90, 120, 150, 200, 250, 300]
        
        for ammo_val in ammo_values:
            pattern = struct.pack('<I', ammo_val)
            found = self.scan_for_pattern(pattern)
            addresses.extend(found)
            print(f"Found {len(found)} potential addresses for ammo value {ammo_val}")
            
        return addresses
    
    def find_coordinate_addresses(self) -> List[int]:
        """
        Find potential coordinate addresses
        Based on BO6: B9742E8 + 60 //Y CORD, +59 //X CORD, +58 //Z CORD
        Look for float values that change when moving
        """
        print("Scanning for coordinate patterns...")
        addresses = []
        
        # Look for sequences of 3 floats (X, Y, Z coordinates)
        # This is a simplified approach - in practice you'd monitor changing values
        chunk_size = 0x1000
        current_addr = 0x10000000
        
        while current_addr < 0x7FFFFFFF:
            try:
                data = self.read_memory(current_addr, chunk_size)
                if data and len(data) >= 12:  # Need at least 12 bytes for 3 floats
                    for i in range(0, len(data) - 11, 4):
                        try:
                            # Read 3 consecutive floats
                            x, y, z = struct.unpack('<fff', data[i:i+12])
                            
                            # Check if they look like reasonable coordinates
                            if (abs(x) < 10000 and abs(y) < 10000 and abs(z) < 10000 and
                                x != 0.0 and y != 0.0 and z != 0.0):
                                addresses.append(current_addr + i)
                                
                        except struct.error:
                            continue
                            
                current_addr += chunk_size
                
            except Exception:
                current_addr += chunk_size
                continue
                
        return addresses[:50]  # Limit results
    
    def monitor_address(self, address: int, data_type: str = "int", duration: int = 10):
        """Monitor a specific memory address for changes"""
        print(f"Monitoring address 0x{address:X} for {duration} seconds...")
        
        start_time = time.time()
        last_value = None
        
        while time.time() - start_time < duration:
            try:
                if data_type == "int":
                    data = self.read_memory(address, 4)
                    if data:
                        value = struct.unpack('<I', data)[0]
                elif data_type == "float":
                    data = self.read_memory(address, 4)
                    if data:
                        value = struct.unpack('<f', data)[0]
                else:
                    continue
                    
                if value != last_value:
                    print(f"Address 0x{address:X}: {last_value} -> {value}")
                    last_value = value
                    
            except Exception as e:
                print(f"Error reading address: {e}")
                
            time.sleep(0.1)
    
    def close(self):
        """Clean up resources"""
        if self.handle:
            ctypes.windll.kernel32.CloseHandle(self.handle)

def main():
    scanner = MemoryScanner("cod.exe")  # Adjust process name as needed
    
    if not scanner.find_process():
        print("MW3/MWZ process not found! Make sure the game is running.")
        return
    
    print("=== MW3/MWZ Memory Scanner ===")
    print("1. Scan for health addresses")
    print("2. Scan for ammo addresses") 
    print("3. Scan for coordinate addresses")
    print("4. Monitor specific address")
    
    choice = input("Enter choice (1-4): ")
    
    try:
        if choice == "1":
            addresses = scanner.find_health_addresses()
            print(f"\nFound {len(addresses)} potential health addresses:")
            for addr in addresses[:10]:  # Show first 10
                print(f"0x{addr:X}")
                
        elif choice == "2":
            addresses = scanner.find_ammo_addresses()
            print(f"\nFound {len(addresses)} potential ammo addresses:")
            for addr in addresses[:10]:
                print(f"0x{addr:X}")
                
        elif choice == "3":
            addresses = scanner.find_coordinate_addresses()
            print(f"\nFound {len(addresses)} potential coordinate addresses:")
            for addr in addresses[:10]:
                print(f"0x{addr:X}")
                
        elif choice == "4":
            addr_str = input("Enter address (hex, e.g., 0x12345678): ")
            address = int(addr_str, 16)
            data_type = input("Data type (int/float): ").lower()
            scanner.monitor_address(address, data_type)
            
    except KeyboardInterrupt:
        print("\nStopped by user")
    finally:
        scanner.close()

if __name__ == "__main__":
    main()
