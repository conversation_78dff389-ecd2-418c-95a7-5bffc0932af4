; ====== MW3 PROCESS SCANNER =======
; Find the exact process name for Microsoft Store MW3
; Compatible with AutoHotkey v1.1.37.02
; ====== MW3 PROCESS SCANNER =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== CREATE GUI =======
Gui, Destroy
Gui, Color, 0x2d2d30
Gui, Font, s12 Bold cLime, Arial
Gui, Add, Text, x10 y10 w480 Center, 🔍 MW3 PROCESS SCANNER

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y40 w460, Scanning for ALL running processes to find MW3...

; Process list area
Gui, Add, Text, x20 y70 w460 h300 vProcessList, Scanning all processes...

; Scan button
Gui, Add, Button, x20 y380 w460 h30 gScanAllProcesses, 🔍 SCAN ALL PROCESSES

; Instructions
Gui, Add, Text, x20 y420 w460, Instructions:
<PERSON><PERSON>, Add, Text, x20 y440 w460, 1. Make sure MW3 is running (like in your screenshot)
Gui, Add, Text, x20 y460 w460, 2. Click scan button to see ALL processes
Gui, Add, Text, x20 y480 w460, 3. Look for any MW3/COD/Modern Warfare related process

Gui, Show, w500 h510, MW3 Process Scanner

; Auto-scan on startup
Gosub, ScanAllProcesses

; ====== SCAN ALL PROCESSES =======
ScanAllProcesses:
    GuiControl,, ProcessList, 🔍 Scanning all running processes...
    
    allProcesses := ""
    processCount := 0
    mw3Processes := ""
    mw3Count := 0
    
    ; Get all running processes using Process command
    Process, List, OutputVar
    
    ; Parse the process list
    StringSplit, ProcessArray, OutputVar, `n
    
    Loop, %ProcessArray0% {
        if (A_Index = 1)
            continue  ; Skip header
            
        currentLine := ProcessArray%A_Index%
        if (currentLine = "")
            continue
            
        ; Extract process name and PID
        StringSplit, ProcessInfo, currentLine, %A_Tab%
        if (ProcessInfo0 >= 2) {
            processName := ProcessInfo1
            processPID := ProcessInfo2
            
            processCount++
            
            ; Check if this might be MW3 related
            if (InStr(processName, "MW") || InStr(processName, "COD") || InStr(processName, "Modern") || InStr(processName, "Call") || InStr(processName, "Duty") || InStr(processName, "Game") || InStr(processName, "38985") || InStr(processName, "MWIII")) {
                mw3Processes .= "🎯 POSSIBLE MW3: " . processName . " (PID: " . processPID . ")`n"
                mw3Count++
            }
            
            allProcesses .= processName . " (PID: " . processPID . ")`n"
        }
    }
    
    ; Build the display text
    displayText := "=== POSSIBLE MW3 PROCESSES ===`n"
    if (mw3Count > 0) {
        displayText .= mw3Processes
        displayText .= "`n=== FOUND " . mw3Count . " POSSIBLE MW3 PROCESSES ===`n"
    } else {
        displayText .= "❌ No obvious MW3 processes found`n"
    }
    
    displayText .= "`n=== ALL RUNNING PROCESSES (" . processCount . " total) ===`n"
    displayText .= allProcesses
    
    GuiControl,, ProcessList, %displayText%
    
    if (mw3Count > 0) {
        TrayTip, Process Scanner, ✅ Found %mw3Count% possible MW3 processes!, 3, 1
    } else {
        TrayTip, Process Scanner, ❌ No obvious MW3 processes found - check the full list, 5, 2
    }
return

GuiClose:
    ExitApp
