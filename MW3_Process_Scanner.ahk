; ====== MW3 PROCESS SCANNER =======
; Find the exact process name for Microsoft Store MW3
; Compatible with AutoHotkey v1.1.37.02
; ====== MW3 PROCESS SCANNER =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== CREATE GUI =======
Gui, Destroy
Gui, Color, 0x2d2d30
Gui, Font, s12 Bold cLime, Arial
Gui, Add, Text, x10 y10 w480 Center, 🔍 MW3 PROCESS SCANNER

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y40 w460, Scanning for ALL running processes to find MW3...

; Process list area
Gui, Add, Text, x20 y70 w460 h300 vProcessList, Scanning all processes...

; Scan button
Gui, Add, Button, x20 y380 w460 h30 gScanAllProcesses, 🔍 SCAN ALL PROCESSES

; Instructions
Gui, Add, Text, x20 y420 w460, Instructions:
<PERSON><PERSON>, Add, Text, x20 y440 w460, 1. Make sure MW3 is running (like in your screenshot)
Gui, Add, Text, x20 y460 w460, 2. Click scan button to see ALL processes
Gui, Add, Text, x20 y480 w460, 3. Look for any MW3/COD/Modern Warfare related process

Gui, Show, w500 h510, MW3 Process Scanner

; Auto-scan on startup
Gosub, ScanAllProcesses

; ====== SCAN ALL PROCESSES =======
ScanAllProcesses:
    GuiControl,, ProcessList, 🔍 Scanning for MW3 processes...

    mw3Processes := ""
    mw3Count := 0

    ; List of possible MW3 process names to check
    possibleNames := "MWIIIGame.exe|38985CA0.MWIIIGame.exe|cod.exe|ModernWarfare3.exe|MW3.exe|CallOfDuty.exe|mw3mp.exe|iw9.exe|modernwarfareiii.exe|gamelaunchhelper.exe|Game.exe|Microsoft.Game.exe|CallOfDutyHQ.exe|CODHQ.exe|launcher.exe"

    StringSplit, nameArray, possibleNames, |

    ; Check each possible process name
    Loop, %nameArray0% {
        processName := nameArray%A_Index%
        Process, Exist, %processName%
        if (ErrorLevel) {
            mw3Processes .= "✅ FOUND: " . processName . " (PID: " . ErrorLevel . ")`n"
            mw3Count++
        }
    }

    ; Also try to use WMI for broader search (with error handling)
    try {
        for process in ComObjGet("winmgmts:").ExecQuery("SELECT Name FROM Win32_Process") {
            processName := process.Name
            if (InStr(processName, "MW") || InStr(processName, "COD") || InStr(processName, "Modern") || InStr(processName, "Call") || InStr(processName, "Duty") || InStr(processName, "38985") || InStr(processName, "MWIII") || InStr(processName, "Game")) {
                ; Check if we haven't already found this one
                if (!InStr(mw3Processes, processName)) {
                    Process, Exist, %processName%
                    if (ErrorLevel) {
                        mw3Processes .= "🎯 DETECTED: " . processName . " (PID: " . ErrorLevel . ")`n"
                        mw3Count++
                    }
                }
            }
        }
    } catch e {
        mw3Processes .= "⚠️ WMI scan failed - using basic detection only`n"
    }

    ; Build the display text
    displayText := "=== MW3 PROCESS SCAN RESULTS ===`n`n"
    if (mw3Count > 0) {
        displayText .= mw3Processes
        displayText .= "`n✅ FOUND " . mw3Count . " MW3-RELATED PROCESSES!`n`n"
        displayText .= "📋 COPY THE EXACT PROCESS NAME ABOVE`n"
        displayText .= "📋 This is what we need for the validation script!`n"
    } else {
        displayText .= "❌ No MW3 processes found`n`n"
        displayText .= "🔍 Make sure MW3 is fully loaded (past launcher)`n"
        displayText .= "🔍 Try running this scanner as administrator`n"
        displayText .= "🔍 Check Task Manager manually for any MW3 process`n"
    }

    GuiControl,, ProcessList, %displayText%

    if (mw3Count > 0) {
        TrayTip, Process Scanner, ✅ Found %mw3Count% MW3 processes!, 3, 1
    } else {
        TrayTip, Process Scanner, ❌ No MW3 processes found, 5, 2
    }
return

GuiClose:
    ExitApp
