    uintptr_t get_bone()
    {
        const uint64_t mb = sdk::module_base;
        uint64_t rax = mb, rbx = mb, rcx = mb, rdx = mb, rdi = mb, rsi = mb, r8 = mb, r9 = mb, r10 = mb, r11 = mb, r12 = mb, r13 = mb, r14 = mb, r15 = mb;
        rdx = driver::read<uintptr_t>(sdk::module_base + 0xD40AD68);
        if (!rdx)
            return rdx;
        r11 = sdk::peb;                 //mov r11, gs:[rax]
        rax = r11;              //mov rax, r11
        rax = _rotr64(rax, 0x15);               //ror rax, 0x15
        rax &= 0xF;
        switch (rax) {
        case 0:
        {
            r9 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);             //mov r9, [0x0000000007115DE8]
            r15 = sdk::module_base + 0x629DAB46;            //lea r15, [0x00000000603E872B]
            r13 = sdk::module_base + 0x9895;                //lea r13, [0xFFFFFFFFFDA1746E]
            rax = 0xAC145E023332D189;               //mov rax, 0xAC145E023332D189
            rdx ^= rax;             //xor rdx, rax
            rax = r15;              //mov rax, r15
            rax = ~rax;             //not rax
            rax *= r11;             //imul rax, r11
            rdx += rax;             //add rdx, rax
            rax = 0xFDEBD2F07B05670D;               //mov rax, 0xFDEBD2F07B05670D
            rdx *= rax;             //imul rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x3;            //shr rax, 0x03
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x6;            //shr rax, 0x06
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0xC;            //shr rax, 0x0C
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x18;           //shr rax, 0x18
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x30;           //shr rax, 0x30
            rdx ^= rax;             //xor rdx, rax
            rax = 0xF0805972B46E082;                //mov rax, 0xF0805972B46E082
            rdx -= rax;             //sub rdx, rax
            rax = r11;              //mov rax, r11
            rax ^= r13;             //xor rax, r13
            rdx += rax;             //add rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r9;              //xor rax, r9
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = rdx;              //mov rax, rdx
            rax >>= 0x4;            //shr rax, 0x04
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x8;            //shr rax, 0x08
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x10;           //shr rax, 0x10
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x20;           //shr rax, 0x20
            rdx ^= rax;             //xor rdx, rax
            return rdx;
        }
        case 1:
        {
            r9 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);             //mov r9, [0x000000000711576A]
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r9;              //xor rax, r9
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = 0x3ECBF33498144A56;               //mov rax, 0x3ECBF33498144A56
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0xA;            //shr rax, 0x0A
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x14;           //shr rax, 0x14
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x28;           //shr rax, 0x28
            rdx ^= rax;             //xor rdx, rax
            rax = 0x87F19886B363B05B;               //mov rax, 0x87F19886B363B05B
            rdx *= rax;             //imul rdx, rax
            rdx -= r11;             //sub rdx, r11
            rax = 0x6303659E1F345AFF;               //mov rax, 0x6303659E1F345AFF
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x15;           //shr rax, 0x15
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x2A;           //shr rax, 0x2A
            rdx ^= rax;             //xor rdx, rax
            rdx += r11;             //add rdx, r11
            return rdx;
        }
        case 2:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x00000000071152EB]
            r13 = sdk::module_base + 0x6F7AC17A;            //lea r13, [0x000000006D1B9256]
            rdx += r11;             //add rdx, r11
            rax = rdx;              //mov rax, rdx
            rax >>= 0x13;           //shr rax, 0x13
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x26;           //shr rax, 0x26
            rdx ^= rax;             //xor rdx, rax
            rax = sdk::module_base + 0x62BA;                //lea rax, [0xFFFFFFFFFDA130A1]
            rax -= r11;             //sub rax, r11
            rdx += rax;             //add rdx, rax
            rax = 0x6367F6E201B667AF;               //mov rax, 0x6367F6E201B667AF
            rdx *= rax;             //imul rdx, rax
            rax = 0x7EA109C91958478C;               //mov rax, 0x7EA109C91958478C
            rdx -= rax;             //sub rdx, rax
            rdx ^= r11;             //xor rdx, r11
            rdx ^= r13;             //xor rdx, r13
            rax = 0x79658B29969CD86A;               //mov rax, 0x79658B29969CD86A
            rdx -= rax;             //sub rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r10;             //xor rax, r10
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            return rdx;
        }
        case 3:
        {
            r9 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);             //mov r9, [0x0000000007114E3A]
            r14 = sdk::module_base + 0xF8CE;                //lea r14, [0xFFFFFFFFFDA1C4F9]
            rax = rdx;              //mov rax, rdx
            rax >>= 0x20;           //shr rax, 0x20
            rdx ^= rax;             //xor rdx, rax
            rax = 0xEA0A19EF431520D;                //mov rax, 0xEA0A19EF431520D
            rdx ^= rax;             //xor rdx, rax
            rax = 0xFFFFFFFF93B5ED93;               //mov rax, 0xFFFFFFFF93B5ED93
            rax -= r11;             //sub rax, r11
            rax -= sdk::module_base;                //sub rax, [rsp+0xA8] -- didn't find trace -> use base
            rdx += rax;             //add rdx, rax
            rax = r11;              //mov rax, r11
            rax *= r14;             //imul rax, r14
            rdx -= rax;             //sub rdx, rax
            rax = 0x39F863E9187B3F65;               //mov rax, 0x39F863E9187B3F65
            rdx *= rax;             //imul rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x1F;           //shr rax, 0x1F
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x3E;           //shr rax, 0x3E
            rdx ^= rax;             //xor rdx, rax
            rax = 0x44AFB2020B72DD38;               //mov rax, 0x44AFB2020B72DD38
            rdx += rax;             //add rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r9;              //xor rax, r9
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            return rdx;
        }
        case 4:
        {
            r9 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);             //mov r9, [0x000000000711491B]
            r15 = sdk::module_base + 0xD76E;                //lea r15, [0xFFFFFFFFFDA19E7A]
            rax = r15;              //mov rax, r15
            rax = ~rax;             //not rax
            rax ^= r11;             //xor rax, r11
            rdx -= rax;             //sub rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x24;           //shr rax, 0x24
            rdx ^= rax;             //xor rdx, rax
            rax = 0x2690031C441C94ED;               //mov rax, 0x2690031C441C94ED
            rdx *= rax;             //imul rdx, rax
            rdx ^= r11;             //xor rdx, r11
            rax = 0xA3A6498F1C56BC17;               //mov rax, 0xA3A6498F1C56BC17
            rdx ^= rax;             //xor rdx, rax
            rdx -= r11;             //sub rdx, r11
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r9;              //xor rax, r9
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            return rdx;
        }
        case 5:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x00000000071144C7]
            rax = sdk::module_base;                 //lea rax, [0xFFFFFFFFFDA0C0FC]
            rdx += rax;             //add rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x19;           //shr rax, 0x19
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x32;           //shr rax, 0x32
            rdx ^= rax;             //xor rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r10;             //xor rax, r10
            rax = _byteswap_uint64(rax);            //bswap rax
            rcx = driver::read<uintptr_t>(rax + 0x11);              //mov rcx, [rax+0x11]
            rax = r11;              //mov rax, r11
            uintptr_t RSP_0x50;
            RSP_0x50 = sdk::module_base + 0x598F00A5;               //lea rax, [0x00000000572FC316] : RSP+0x50
            rax *= RSP_0x50;                //imul rax, [rsp+0x50]
            rax -= sdk::module_base;                //sub rax, [rsp+0xA8] -- didn't find trace -> use base
            rdx += rax;             //add rdx, rax
            rax = 0xC6D870371839E04D;               //mov rax, 0xC6D870371839E04D
            rdx *= rax;             //imul rdx, rax
            rax = 0x2435BC22D4E2922B;               //mov rax, 0x2435BC22D4E2922B
            rdx -= rax;             //sub rdx, rax
            rdx *= rcx;             //imul rdx, rcx
            rax = 0xBBD9DF3CECEEFE74;               //mov rax, 0xBBD9DF3CECEEFE74
            rdx ^= rax;             //xor rdx, rax
            rax = 0x23B4F504FA125955;               //mov rax, 0x23B4F504FA125955
            rdx *= rax;             //imul rdx, rax
            return rdx;
        }
        case 6:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x0000000007114086]
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rax = r11;              //mov rax, r11
            rax -= sdk::module_base;                //sub rax, [rsp+0xA8] -- didn't find trace -> use base
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rax += 0xFFFFFFFFA7D1474C;              //add rax, 0xFFFFFFFFA7D1474C
            rdx += rax;             //add rdx, rax
            rcx ^= r10;             //xor rcx, r10
            rcx = _byteswap_uint64(rcx);            //bswap rcx
            rdx *= driver::read<uintptr_t>(rcx + 0x11);             //imul rdx, [rcx+0x11]
            rax = 0xFFFFFFFFC0CD4EE3;               //mov rax, 0xFFFFFFFFC0CD4EE3
            rax -= r11;             //sub rax, r11
            rax -= sdk::module_base;                //sub rax, [rsp+0xA8] -- didn't find trace -> use base
            rdx += rax;             //add rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x23;           //shr rax, 0x23
            rax ^= rdx;             //xor rax, rdx
            rdx = 0x5A8397EF69EB3410;               //mov rdx, 0x5A8397EF69EB3410
            rax += r11;             //add rax, r11
            rax += rdx;             //add rax, rdx
            rdx = sdk::module_base;                 //lea rdx, [0xFFFFFFFFFDA0BB95]
            rdx += rax;             //add rdx, rax
            rax = 0x94B908816CF2DBE1;               //mov rax, 0x94B908816CF2DBE1
            rdx *= rax;             //imul rdx, rax
            return rdx;
        }
        case 7:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x0000000007113C74]
            r15 = sdk::module_base + 0x6B60;                //lea r15, [0xFFFFFFFFFDA125C5]
            rax = sdk::module_base;                 //lea rax, [0xFFFFFFFFFDA0B777]
            rax += 0xC77B;          //add rax, 0xC77B
            rax += r11;             //add rax, r11
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x9;            //shr rax, 0x09
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x12;           //shr rax, 0x12
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x24;           //shr rax, 0x24
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x13;           //shr rax, 0x13
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x26;           //shr rax, 0x26
            rdx ^= rax;             //xor rdx, rax
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rax = r15;              //mov rax, r15
            rax = ~rax;             //not rax
            rax *= r11;             //imul rax, r11
            rcx = _byteswap_uint64(rcx);            //bswap rcx
            rdx += rax;             //add rdx, rax
            rax = 0x3BAB7EE1C2FB5485;               //mov rax, 0x3BAB7EE1C2FB5485
            rdx *= driver::read<uintptr_t>(rcx + 0x11);             //imul rdx, [rcx+0x11]
            rdx += rax;             //add rdx, rax
            rax = 0xD64310FF7669DED5;               //mov rax, 0xD64310FF7669DED5
            rdx *= rax;             //imul rdx, rax
            rax = 0xC9A0080E2B52320A;               //mov rax, 0xC9A0080E2B52320A
            rdx ^= rax;             //xor rdx, rax
            return rdx;
        }
        case 8:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x000000000711369A]
            rax = r11;              //mov rax, r11
            rax -= sdk::module_base;                //sub rax, [rsp+0xA8] -- didn't find trace -> use base
            rax += 0xFFFFFFFF954B94E9;              //add rax, 0xFFFFFFFF954B94E9
            rdx += rax;             //add rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r10;             //xor rax, r10
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = rdx;              //mov rax, rdx
            rax >>= 0xE;            //shr rax, 0x0E
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x1C;           //shr rax, 0x1C
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x38;           //shr rax, 0x38
            rdx ^= rax;             //xor rdx, rax
            rax = 0x28853EAC80AAB90;                //mov rax, 0x28853EAC80AAB90
            rdx -= rax;             //sub rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x21;           //shr rax, 0x21
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0xB;            //shr rax, 0x0B
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x16;           //shr rax, 0x16
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x2C;           //shr rax, 0x2C
            rdx ^= rax;             //xor rdx, rax
            rax = 0x9ED615C5A516F48D;               //mov rax, 0x9ED615C5A516F48D
            rdx *= rax;             //imul rdx, rax
            rax = 0x4A5451CFD1051B0F;               //mov rax, 0x4A5451CFD1051B0F
            rdx *= rax;             //imul rdx, rax
            return rdx;
        }
        case 9:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x0000000007113187]
            r13 = sdk::module_base + 0x9F7F;                //lea r13, [0xFFFFFFFFFDA14EF7]
            rcx = sdk::module_base + 0x590B7B0F;            //lea rcx, [0x0000000056AC2A0F]
            rax = rdx;              //mov rax, rdx
            rax >>= 0x25;           //shr rax, 0x25
            rdx ^= rax;             //xor rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r10;             //xor rax, r10
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = 0xE41AAE0B4978C7A7;               //mov rax, 0xE41AAE0B4978C7A7
            rdx *= rax;             //imul rdx, rax
            rax = 0x4884ED1EDA36D9B2;               //mov rax, 0x4884ED1EDA36D9B2
            rdx -= rax;             //sub rdx, rax
            rax = r11;              //mov rax, r11
            rax ^= r13;             //xor rax, r13
            rdx ^= rax;             //xor rdx, rax
            rax = 0xA5F46429036B04E5;               //mov rax, 0xA5F46429036B04E5
            rdx *= rax;             //imul rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x27;           //shr rax, 0x27
            rdx ^= rax;             //xor rdx, rax
            rdx -= r11;             //sub rdx, r11
            rdx += rcx;             //add rdx, rcx
            return rdx;
        }
        case 10:
        {
            r9 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);             //mov r9, [0x0000000007112CEB]
            rdx -= sdk::module_base;                //sub rdx, [rsp+0xA8] -- didn't find trace -> use base
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r9;              //xor rax, r9
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = r11;              //mov rax, r11
            rax -= sdk::module_base;                //sub rax, [rsp+0xA8] -- didn't find trace -> use base
            rax += 0xFFFFFFFFFFFF88EC;              //add rax, 0xFFFFFFFFFFFF88EC
            rdx += rax;             //add rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x12;           //shr rax, 0x12
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x24;           //shr rax, 0x24
            rdx ^= rax;             //xor rdx, rax
            rax = 0x4DBC160E13E56349;               //mov rax, 0x4DBC160E13E56349
            rdx *= rax;             //imul rdx, rax
            rax = sdk::module_base;                 //lea rax, [0xFFFFFFFFFDA0A9C4]
            rdx ^= rax;             //xor rdx, rax
            rax = sdk::module_base;                 //lea rax, [0xFFFFFFFFFDA0A896]
            rdx += rax;             //add rdx, rax
            return rdx;
        }
        case 11:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x0000000007112845]
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r10;             //xor rax, r10
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = sdk::module_base + 0x73A4FAE9;            //lea rax, [0x0000000071459E6D]
            rax = ~rax;             //not rax
            rdx -= r11;             //sub rdx, r11
            rdx += rax;             //add rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x16;           //shr rax, 0x16
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x2C;           //shr rax, 0x2C
            rdx ^= rax;             //xor rdx, rax
            rax = 0x861DF3431C84C629;               //mov rax, 0x861DF3431C84C629
            rdx *= rax;             //imul rdx, rax
            rax = 0x714B44E8CE73C4F0;               //mov rax, 0x714B44E8CE73C4F0
            rdx -= rax;             //sub rdx, rax
            rax = sdk::module_base;                 //lea rax, [0xFFFFFFFFFDA0A2E8]
            rcx = rax * 0xFFFFFFFFFFFFFFFE;                 //imul rcx, rax, 0xFFFFFFFFFFFFFFFE
            rax = 0x6F9175143B9ED737;               //mov rax, 0x6F9175143B9ED737
            rdx += rax;             //add rdx, rax
            rdx += rcx;             //add rdx, rcx
            return rdx;
        }
        case 12:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x0000000007112489]
            r15 = sdk::module_base + 0xD3DA;                //lea r15, [0xFFFFFFFFFDA17654]
            rcx = r11;              //mov rcx, r11
            rcx = ~rcx;             //not rcx
            rax = sdk::module_base + 0x1F86111B;            //lea rax, [0x000000001D26B055]
            rax = ~rax;             //not rax
            rcx *= rax;             //imul rcx, rax
            rax = r15;              //mov rax, r15
            rax -= r11;             //sub rax, r11
            rax += rdx;             //add rax, rdx
            rdx = rcx;              //mov rdx, rcx
            rdx ^= rax;             //xor rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r10;             //xor rax, r10
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rcx = sdk::module_base;                 //lea rcx, [0xFFFFFFFFFDA0A0ED]
            rax = r11;              //mov rax, r11
            rax -= rcx;             //sub rax, rcx
            rcx = rax + 0xffffffffca7be9d9;                 //lea rcx, [rax-0x35841627]
            rcx += rdx;             //add rcx, rdx
            rax = rcx;              //mov rax, rcx
            rax >>= 0x18;           //shr rax, 0x18
            rcx ^= rax;             //xor rcx, rax
            rax = 0xFBA7ABC8BBB4629D;               //mov rax, 0xFBA7ABC8BBB4629D
            rdx = rcx;              //mov rdx, rcx
            rdx >>= 0x30;           //shr rdx, 0x30
            rdx ^= rcx;             //xor rdx, rcx
            rdx *= rax;             //imul rdx, rax
            rax = 0x1FE6307AA1F54B4D;               //mov rax, 0x1FE6307AA1F54B4D
            rdx *= rax;             //imul rdx, rax
            rax = 0x57A7A919AF723E1B;               //mov rax, 0x57A7A919AF723E1B
            rdx -= rax;             //sub rdx, rax
            return rdx;
        }
        case 13:
        {
            r9 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);             //mov r9, [0x000000000711208C]
            rax = rdx;              //mov rax, rdx
            rax >>= 0x1A;           //shr rax, 0x1A
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x34;           //shr rax, 0x34
            rdx ^= rax;             //xor rdx, rax
            rax = 0x525F068BC2643DF7;               //mov rax, 0x525F068BC2643DF7
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0xD;            //shr rax, 0x0D
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x1A;           //shr rax, 0x1A
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x34;           //shr rax, 0x34
            rdx ^= rax;             //xor rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r9;              //xor rax, r9
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = 0x75DFF140FA1FB5BC;               //mov rax, 0x75DFF140FA1FB5BC
            rdx += rax;             //add rdx, rax
            rax = sdk::module_base;                 //lea rax, [0xFFFFFFFFFDA09AC6]
            rdx += rax;             //add rdx, rax
            rax = 0xE5945E699002C625;               //mov rax, 0xE5945E699002C625
            rdx *= rax;             //imul rdx, rax
            rax = sdk::module_base;                 //lea rax, [0xFFFFFFFFFDA09AAE]
            rdx ^= rax;             //xor rdx, rax
            return rdx;
        }
        case 14:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x0000000007111C27]
            r15 = sdk::module_base + 0x73A4A654;            //lea r15, [0x000000007145406C]
            rax = 0x75736E13202430E1;               //mov rax, 0x75736E13202430E1
            rdx *= rax;             //imul rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x26;           //shr rax, 0x26
            rdx ^= rax;             //xor rdx, rax
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = _byteswap_uint64(rcx);            //bswap rcx
            rdx *= driver::read<uintptr_t>(rcx + 0x11);             //imul rdx, [rcx+0x11]
            rax = rdx;              //mov rax, rdx
            rax >>= 0x9;            //shr rax, 0x09
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x12;           //shr rax, 0x12
            rdx ^= rax;             //xor rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x24;           //shr rax, 0x24
            rdx ^= rax;             //xor rdx, rax
            rdx += r11;             //add rdx, r11
            rax = r11;              //mov rax, r11
            rax = ~rax;             //not rax
            rax *= r15;             //imul rax, r15
            rdx ^= rax;             //xor rdx, rax
            rax = 0xABD8E138F25E5687;               //mov rax, 0xABD8E138F25E5687
            rdx ^= rax;             //xor rdx, rax
            return rdx;
        }
        case 15:
        {
            r10 = driver::read<uintptr_t>(sdk::module_base + 0x97081FC);            //mov r10, [0x0000000007111879]
            rsi = 0xB656FAE057EB613B;               //mov rsi, 0xB656FAE057EB613B
            rdx *= rsi;             //imul rdx, rsi
            rax = rdx;              //mov rax, rdx
            rax >>= 0x21;           //shr rax, 0x21
            rdx ^= rax;             //xor rdx, rax
            rax = 0;                //and rax, 0xFFFFFFFFC0000000
            rax = _rotl64(rax, 0x10);               //rol rax, 0x10
            rax ^= r10;             //xor rax, r10
            rax = _byteswap_uint64(rax);            //bswap rax
            rdx *= driver::read<uintptr_t>(rax + 0x11);             //imul rdx, [rax+0x11]
            rax = 0x5CA0A4447C245D90;               //mov rax, 0x5CA0A4447C245D90
            rdx -= rax;             //sub rdx, rax
            rdx -= r11;             //sub rdx, r11
            rax = 0xF071D0312866EB9D;               //mov rax, 0xF071D0312866EB9D
            rdx *= rax;             //imul rdx, rax
            rax = 0xFFFFFFFFFFFFF34A;               //mov rax, 0xFFFFFFFFFFFFF34A
            rax -= r11;             //sub rax, r11
            rax -= sdk::module_base;                //sub rax, [rsp+0xA8] -- didn't find trace -> use base
            rdx += rax;             //add rdx, rax
            rax = rdx;              //mov rax, rdx
            rax >>= 0x21;           //shr rax, 0x21
            rdx ^= rax;             //xor rdx, rax
            return rdx;
        }
        }
    }
    uint32_t get_bone_index(uint32_t index)
    {
        const uint64_t mb = sdk::module_base;
        uint64_t rax = mb, rbx = mb, rcx = mb, rdx = mb, rdi = mb, rsi = mb, r8 = mb, r9 = mb, r10 = mb, r11 = mb, r12 = mb, r13 = mb, r14 = mb, r15 = mb;
        rdi = index;
        rcx = rdi * 0x13C8;
        rax = 0xD73F3E9D2DBEC8E7;               //mov rax, 0xD73F3E9D2DBEC8E7
        rax = _umul128(rax, rcx, (uintptr_t*)&rdx);             //mul rcx
        r11 = 0xCCCCCCCCCCCCCCCD;               //mov r11, 0xCCCCCCCCCCCCCCCD
        rdi = sdk::module_base;                 //lea rdi, [0xFFFFFFFFFDD521FB]
        rdx >>= 0xD;            //shr rdx, 0x0D
        r10 = 0xE98285CCFA0AE387;               //mov r10, 0xE98285CCFA0AE387
        rax = rdx * 0x260F;             //imul rax, rdx, 0x260F
        rcx -= rax;             //sub rcx, rax
        rax = 0xC388D5333BAA90CD;               //mov rax, 0xC388D5333BAA90CD
        r8 = rcx * 0x260F;              //imul r8, rcx, 0x260F
        rax = _umul128(rax, r8, (uintptr_t*)&rdx);              //mul r8
        rax = r8;               //mov rax, r8
        rax -= rdx;             //sub rax, rdx
        rax >>= 0x1;            //shr rax, 0x01
        rax += rdx;             //add rax, rdx
        rax >>= 0xE;            //shr rax, 0x0E
        rax = rax * 0x4892;             //imul rax, rax, 0x4892
        r8 -= rax;              //sub r8, rax
        rax = r11;              //mov rax, r11
        rax = _umul128(rax, r8, (uintptr_t*)&rdx);              //mul r8
        rax = 0xBAA551EE51D6FD2D;               //mov rax, 0xBAA551EE51D6FD2D
        rdx >>= 0x3;            //shr rdx, 0x03
        rcx = rdx + rdx * 4;            //lea rcx, [rdx+rdx*4]
        rax = _umul128(rax, r8, (uintptr_t*)&rdx);              //mul r8
        rdx >>= 0xB;            //shr rdx, 0x0B
        rax = rdx + rcx * 2;            //lea rax, [rdx+rcx*2]
        rcx = rax * 0x15F2;             //imul rcx, rax, 0x15F2
        rax = r8 * 0x15F4;              //imul rax, r8, 0x15F4
        rax -= rcx;             //sub rax, rcx
        rax = driver::read<uint16_t>(rax + rdi * 1 + 0x977B850);                //movzx eax, word ptr [rax+rdi*1+0x977B850]
        r8 = rax * 0x13C8;              //imul r8, rax, 0x13C8
        rax = r10;              //mov rax, r10
        rax = _umul128(rax, r8, (uintptr_t*)&rdx);              //mul r8
        rax = r10;              //mov rax, r10
        rdx >>= 0xD;            //shr rdx, 0x0D
        rcx = rdx * 0x2315;             //imul rcx, rdx, 0x2315
        r8 -= rcx;              //sub r8, rcx
        r9 = r8 * 0x351B;               //imul r9, r8, 0x351B
        rax = _umul128(rax, r9, (uintptr_t*)&rdx);              //mul r9
        rax = r11;              //mov rax, r11
        rdx >>= 0xD;            //shr rdx, 0x0D
        rcx = rdx * 0x2315;             //imul rcx, rdx, 0x2315
        r9 -= rcx;              //sub r9, rcx
        rax = _umul128(rax, r9, (uintptr_t*)&rdx);              //mul r9
        rax = 0x18AB083902BDAB95;               //mov rax, 0x18AB083902BDAB95
        rdx >>= 0x4;            //shr rdx, 0x04
        rcx = rdx + rdx * 4;            //lea rcx, [rdx+rdx*4]
        rax = _umul128(rax, r9, (uintptr_t*)&rdx);              //mul r9
        rax = r9;               //mov rax, r9
        rax -= rdx;             //sub rax, rdx
        rax >>= 0x1;            //shr rax, 0x01
        rax += rdx;             //add rax, rdx
        rax >>= 0x8;            //shr rax, 0x08
        rax = rax + rcx * 4;            //lea rax, [rax+rcx*4]
        rcx = rax * 0x3A6;              //imul rcx, rax, 0x3A6
        rax = r9 * 0x3A8;               //imul rax, r9, 0x3A8
        rax -= rcx;             //sub rax, rcx
        r14 = driver::read<uint16_t>(rax + rdi * 1 + 0x97893D0);                //movsx r14d, word ptr [rax+rdi*1+0x97893D0]
        return r14;
    }