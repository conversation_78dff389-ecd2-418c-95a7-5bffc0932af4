; ====== MW3 XINPUT ALTERNATIVE SYSTEM =======
; Xbox Controller Enhancement without Interception Driver
; Uses XInput library for direct controller access
; Safer and easier setup than Interception
; ====== MW3 XINPUT ALTERNATIVE SYSTEM =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input
SetWorkingDir %A_ScriptDir%

; ====== XINPUT LIBRARY SETUP =======
; XInput is built into Windows - no additional drivers needed
global XINPUT_AVAILABLE := false
global CONTROLLER_CONNECTED := false

; Try to load XInput
try {
    DllCall("LoadLibrary", "Str", "xinput1_4.dll")
    XINPUT_AVAILABLE := true
    TrayTip, XInput System, XInput library loaded successfully!, 2, 1
} catch {
    try {
        DllCall("LoadLibrary", "Str", "xinput1_3.dll")
        XINPUT_AVAILABLE := true
        TrayTip, XInput System, XInput 1.3 loaded successfully!, 2, 1
    } catch {
        TrayTip, XInput Error, XInput not available - Controller features limited, 5, 2
    }
}

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global MW3_WINDOW_TITLE := "Call of Duty: Modern Warfare III"
global MW3_WINDOW_HANDLE := 0
global VALIDATION_PASSED := false

; Feature toggles
global RAPID_FIRE_ENABLED := false
global SUPER_JUMP_ENABLED := false
global RECOIL_COMPENSATION_ENABLED := false
global AIMBOT_ENABLED := false

; XInput-based settings
global RAPID_FIRE_SPEED := 50        ; 10-500ms
global SUPER_JUMP_STRENGTH := 3      ; 1-10x
global RECOIL_COMPENSATION := 0.5    ; 0.1-2.0
global AIMBOT_STRENGTH := 0.3        ; 0.1-1.0
global AIMBOT_SMOOTHNESS := 0.5      ; 0.1-1.0

; Controller state
global CURRENT_RT_TRIGGER := 0
global CURRENT_A_BUTTON := false
global CURRENT_RIGHT_STICK_X := 0
global CURRENT_RIGHT_STICK_Y := 0
global TRIGGER_THRESHOLD := 30

; ====== XINPUT FUNCTIONS =======
GetControllerState(controllerIndex := 0) {
    global
    
    if (!XINPUT_AVAILABLE) {
        return false
    }
    
    VarSetCapacity(state, 16, 0)
    result := DllCall("xinput1_4\XInputGetState", "UInt", controllerIndex, "Ptr", &state, "UInt")
    
    if (result != 0) {
        CONTROLLER_CONNECTED := false
        return false
    }
    
    CONTROLLER_CONNECTED := true
    
    ; Extract controller data
    CURRENT_RT_TRIGGER := NumGet(state, 7, "UChar")  ; Right trigger
    CURRENT_A_BUTTON := (NumGet(state, 4, "UShort") & 0x1000) ? true : false  ; A button
    CURRENT_RIGHT_STICK_X := NumGet(state, 8, "Short")  ; Right stick X
    CURRENT_RIGHT_STICK_Y := NumGet(state, 10, "Short") ; Right stick Y
    
    return true
}

; ====== CREATE GUI =======
Gui, Destroy
Gui, Color, 0x1a1a1a
Gui, Font, s14 Bold cLime, Arial
Gui, Add, Text, x10 y10 w580 Center, MW3 XINPUT ALTERNATIVE SYSTEM

Gui, Font, s10 cWhite, Arial
Gui, Add, Text, x20 y40 w560, Status: XInput-based controller enhancement ready

; Status display
Gui, Add, Text, x20 y70 w560 h40 vStatusDisplay, XInput library loaded - No additional drivers needed - Account safe alternative

; Feature controls
Gui, Font, s11 Bold cYellow, Arial
Gui, Add, Text, x20 y120 w560, XINPUT CONTROLLER FEATURES:

Gui, Font, s9 cWhite, Arial
Gui, Add, Checkbox, x30 y150 w200 vRapidFireCheck gToggleRapidFire, XInput Rapid Fire
Gui, Add, Checkbox, x250 y150 w200 vSuperJumpCheck gToggleSuperJump, XInput Super Jump
Gui, Add, Checkbox, x30 y180 w200 vRecoilCheck gToggleRecoil, XInput Recoil Control
Gui, Add, Checkbox, x250 y180 w200 vAimbotCheck gToggleAimbot, XInput Aimbot

; XInput-based sliders
Gui, Font, s10 Bold cCyan, Arial
Gui, Add, Text, x20 y220 w560, XINPUT CONTROL SETTINGS:

Gui, Font, s9 cWhite, Arial
; Rapid Fire Speed
Gui, Add, Text, x30 y250, XInput Rapid Fire:
Gui, Add, Slider, x150 y248 w200 h20 Range10-500 TickInterval50 vRapidFireSlider gUpdateRapidFire, %RAPID_FIRE_SPEED%
Gui, Add, Text, x360 y250 w80 vRapidFireValue, %RAPID_FIRE_SPEED%ms

; Super Jump Strength
Gui, Add, Text, x30 y280, XInput Super Jump:
Gui, Add, Slider, x150 y278 w200 h20 Range1-10 TickInterval1 vSuperJumpSlider gUpdateSuperJump, %SUPER_JUMP_STRENGTH%
Gui, Add, Text, x360 y280 w80 vSuperJumpValue, %SUPER_JUMP_STRENGTH%x

; Recoil Compensation
Gui, Add, Text, x30 y310, XInput Recoil Control:
Gui, Add, Slider, x150 y308 w200 h20 Range1-20 TickInterval2 vRecoilSlider gUpdateRecoil, % Round(RECOIL_COMPENSATION * 10)
Gui, Add, Text, x360 y310 w80 vRecoilValue, %RECOIL_COMPENSATION%

; Aimbot Strength
Gui, Add, Text, x30 y340, XInput Aimbot Strength:
Gui, Add, Slider, x150 y338 w200 h20 Range1-10 TickInterval1 vAimbotStrengthSlider gUpdateAimbotStrength, % Round(AIMBOT_STRENGTH * 10)
Gui, Add, Text, x360 y340 w80 vAimbotStrengthValue, %AIMBOT_STRENGTH%

; Control buttons
Gui, Add, Button, x20 y380 w270 h40 gValidateSystem, VALIDATE XINPUT SETUP
Gui, Add, Button, x310 y380 w270 h40 gToggleSystem, START XINPUT SYSTEM

; Controller status
Gui, Font, s8 cCyan, Arial
Gui, Add, Text, x20 y440 w560, XINPUT STATUS:
Gui, Add, Text, x20 y460 w560 vControllerStatus, XInput library ready - Waiting for controller detection

; Xbox Controller info
Gui, Add, Text, x20 y490 w560, XINPUT XBOX CONTROLLER:
Gui, Add, Text, x20 y510 w560, RT Trigger: XInput Rapid Fire - A Button: XInput Super Jump
Gui, Add, Text, x20 y530 w560, Right Stick: XInput Aimbot - All via XInput library (built into Windows)

; Safety notice
Gui, Font, s8 cYellow, Arial
Gui, Add, Text, x20 y560 w560, XINPUT SAFETY: Uses Windows built-in XInput - No additional drivers needed
Gui, Add, Text, x20 y580 w560, ACCOUNT SAFE: Standard Windows API - No kernel-level modifications

Gui, Show, w600 h620, MW3 XInput Alternative System

; Start XInput monitoring
SetTimer, MonitorXInput, 50

; ====== VALIDATE XINPUT SETUP =======
ValidateSystem:
    GuiControl,, StatusDisplay, Validating XInput setup and MW3 detection...
    
    ; Check XInput availability
    if (!XINPUT_AVAILABLE) {
        GuiControl,, StatusDisplay, XINPUT ERROR: XInput library not available on this system
        GuiControl,, ControllerStatus, XInput validation FAILED - XInput not available
        return
    }
    
    ; Check controller
    GetControllerState(0)
    
    ; Check MW3 window
    windowTitles := "Call of Duty: Modern Warfare III|Modern Warfare III|MW3|Call of Duty|COD"
    StringSplit, titleArray, windowTitles, |
    
    MW3_WINDOW_HANDLE := 0
    foundTitle := ""
    
    Loop, %titleArray0% {
        testTitle := titleArray%A_Index%
        WinGet, testHandle, ID, %testTitle%
        if (testHandle) {
            MW3_WINDOW_HANDLE := testHandle
            foundTitle := testTitle
            break
        }
    }
    
    if (MW3_WINDOW_HANDLE && CONTROLLER_CONNECTED && XINPUT_AVAILABLE) {
        VALIDATION_PASSED := true
        GuiControl,, StatusDisplay, XINPUT READY: Controller connected - MW3: %foundTitle% - XInput active
        GuiControl,, ControllerStatus, XInput validation PASSED - All systems ready for activation
        TrayTip, XInput Validation, All XInput systems validated successfully!, 3, 1
    } else {
        VALIDATION_PASSED := false
        statusMsg := "XINPUT VALIDATION FAILED: "
        if (!XINPUT_AVAILABLE) {
            statusMsg .= "XInput not available - "
        }
        if (!CONTROLLER_CONNECTED) {
            statusMsg .= "Xbox controller not detected - "
        }
        if (!MW3_WINDOW_HANDLE) {
            statusMsg .= "MW3 window not found"
        }
        GuiControl,, StatusDisplay, %statusMsg%
        GuiControl,, ControllerStatus, XInput validation FAILED - Check requirements
        TrayTip, XInput Validation Failed, Check XInput controller and MW3, 5, 2
    }
return

; ====== TOGGLE XINPUT SYSTEM =======
ToggleSystem:
    if (!VALIDATION_PASSED) {
        TrayTip, System Error, Please validate XInput setup first!, 3, 2
        return
    }
    
    SYSTEM_ENABLED := !SYSTEM_ENABLED
    
    if (SYSTEM_ENABLED) {
        GuiControl,, StatusDisplay, XINPUT SYSTEM ACTIVE - All features operating via XInput - Account safe operation
        GuiControl,, ControllerStatus, XInput system ACTIVE - Processing controller inputs via Windows API
        GuiControl, Text, ToggleSystem, STOP XINPUT SYSTEM
        TrayTip, XInput System Active, MW3 XInput system activated!, 3, 1
        
        ; Start XInput processing
        SetTimer, ProcessXInputFeatures, 10
    } else {
        GuiControl,, StatusDisplay, XINPUT SYSTEM STOPPED - All features disabled - Click Start to reactivate
        GuiControl,, ControllerStatus, XInput system STOPPED - XInput monitoring idle
        GuiControl, Text, ToggleSystem, START XINPUT SYSTEM
        TrayTip, XInput System Stopped, MW3 XInput system deactivated, 2, 1
        
        ; Stop XInput processing
        SetTimer, ProcessXInputFeatures, Off
    }
return

; ====== XINPUT MONITORING =======
MonitorXInput:
    if (!XINPUT_AVAILABLE) {
        return
    }
    
    ; Update controller state
    GetControllerState(0)
    
    ; Update GUI with controller info
    if (CONTROLLER_CONNECTED) {
        triggerPressure := Round((CURRENT_RT_TRIGGER / 255) * 100)
        controllerInfo := "XInput controller connected - RT: " . triggerPressure . "% - A: " . (CURRENT_A_BUTTON ? "Pressed" : "Released")
        GuiControl,, ControllerStatus, %controllerInfo%
    } else {
        GuiControl,, ControllerStatus, XInput controller not detected - Connect Xbox controller
    }
return

; ====== XINPUT FEATURE PROCESSING =======
ProcessXInputFeatures:
    ; Static variables for timing (AutoHotkey v1.1.37.02 compatible)
    static lastRapidFire
    static lastSuperJump

    ; Initialize static variables if not set
    if (lastRapidFire = "") {
        lastRapidFire := 0
    }
    if (lastSuperJump = "") {
        lastSuperJump := 0
    }

    if (!SYSTEM_ENABLED || !VALIDATION_PASSED || !CONTROLLER_CONNECTED) {
        return
    }

    ; Only process when MW3 is active
    WinGet, activeWindow, ID, A
    if (activeWindow != MW3_WINDOW_HANDLE) {
        return
    }

    ; XInput rapid fire
    if (RAPID_FIRE_ENABLED && CURRENT_RT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Simulate rapid clicking based on RT trigger pressure
        currentTime := A_TickCount
        if (currentTime - lastRapidFire >= RAPID_FIRE_SPEED) {
            Click
            lastRapidFire := currentTime
        }
    }

    ; XInput super jump
    if (SUPER_JUMP_ENABLED && CURRENT_A_BUTTON) {
        ; Enhanced jump simulation
        currentTime := A_TickCount
        if (currentTime - lastSuperJump >= 200) {  ; Prevent spam
            Loop, %SUPER_JUMP_STRENGTH% {
                Send, {Space}
                Sleep, 5
            }
            lastSuperJump := currentTime
        }
    }
    
    ; XInput aimbot (simplified)
    if (AIMBOT_ENABLED) {
        ; Basic aimbot using right stick input
        if (Abs(CURRENT_RIGHT_STICK_X) > 1000 || Abs(CURRENT_RIGHT_STICK_Y) > 1000) {
            ; Apply aimbot assistance
            aimX := (CURRENT_RIGHT_STICK_X / 32767) * AIMBOT_STRENGTH * 2
            aimY := (CURRENT_RIGHT_STICK_Y / 32767) * AIMBOT_STRENGTH * 2
            
            DllCall("mouse_event", "UInt", 0x0001, "Int", Round(aimX), "Int", Round(aimY), "UInt", 0, "UPtr", 0)
        }
    }
    
    ; XInput recoil compensation
    if (RECOIL_COMPENSATION_ENABLED && CURRENT_RT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Apply recoil compensation
        recoilY := RECOIL_COMPENSATION * 2
        DllCall("mouse_event", "UInt", 0x0001, "Int", 0, "Int", Round(recoilY), "UInt", 0, "UPtr", 0)
    }
return

; ====== SLIDER UPDATE FUNCTIONS =======
UpdateRapidFire:
    Gui, Submit, NoHide
    RAPID_FIRE_SPEED := RapidFireSlider
    GuiControl,, RapidFireValue, %RAPID_FIRE_SPEED%ms
return

UpdateSuperJump:
    Gui, Submit, NoHide
    SUPER_JUMP_STRENGTH := SuperJumpSlider
    GuiControl,, SuperJumpValue, %SUPER_JUMP_STRENGTH%x
return

UpdateRecoil:
    Gui, Submit, NoHide
    RECOIL_COMPENSATION := RecoilSlider / 10.0
    GuiControl,, RecoilValue, %RECOIL_COMPENSATION%
return

UpdateAimbotStrength:
    Gui, Submit, NoHide
    AIMBOT_STRENGTH := AimbotStrengthSlider / 10.0
    GuiControl,, AimbotStrengthValue, %AIMBOT_STRENGTH%
return

; ====== FEATURE TOGGLES =======
ToggleRapidFire:
    Gui, Submit, NoHide
    RAPID_FIRE_ENABLED := RapidFireCheck
    TrayTip, XInput Rapid Fire, % (RAPID_FIRE_ENABLED ? "XInput rapid fire enabled" : "XInput rapid fire disabled"), 1, 1
return

ToggleSuperJump:
    Gui, Submit, NoHide
    SUPER_JUMP_ENABLED := SuperJumpCheck
    TrayTip, XInput Super Jump, % (SUPER_JUMP_ENABLED ? "XInput super jump enabled" : "XInput super jump disabled"), 1, 1
return

ToggleRecoil:
    Gui, Submit, NoHide
    RECOIL_COMPENSATION_ENABLED := RecoilCheck
    TrayTip, XInput Recoil Control, % (RECOIL_COMPENSATION_ENABLED ? "XInput recoil control enabled" : "XInput recoil control disabled"), 1, 1
return

ToggleAimbot:
    Gui, Submit, NoHide
    AIMBOT_ENABLED := AimbotCheck
    TrayTip, XInput Aimbot, % (AIMBOT_ENABLED ? "XInput aimbot enabled" : "XInput aimbot disabled"), 1, 1
return

GuiClose:
    ExitApp
