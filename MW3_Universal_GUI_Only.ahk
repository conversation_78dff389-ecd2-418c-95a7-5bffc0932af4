; ====== MW3/MWZ UNIVERSAL WEAPON ENHANCEMENT - GUI ONLY VERSION =======
; Universal Enhancement for ALL Weapons - GUI Controls Only
; No Xbox Controller Button Conflicts - Only RIGHT TRIGGER Activation
; Mystery Box, Wall-Buy, Starting, Wonder Weapons - ALL SUPPORTED
; Real-Time Fire Rate Control via GUI + RIGHT TRIGGER Activation
; ====== MW3/MWZ UNIVERSAL WEAPON ENHANCEMENT - GUI ONLY VERSION =======

#NoEnv
#SingleInstance Force
#Persistent
SendMode Input

; ====== REAL MW3 OFFSETS (from MWIII_offsets.txt) =======
global MW3_CG_BASE := 0x127C6A88              ; Main game state (REAL)
global MW3_PRIMARY_DECRYPT_KEY := 0x97080E6   ; Primary decryption (REAL)
global MW3_CHAR_DECRYPT_KEY := 0x970810E      ; Character decryption (REAL)
global MW3_CLIENT_INFO_OFFSET := 0x18de40     ; Client info (REAL)

; ====== UNIVERSAL WEAPON ENHANCEMENT CONSTANTS =======
global UNIVERSAL_FIRE_RATE_MIN := 100         ; 100% = normal fire rate
global UNIVERSAL_FIRE_RATE_MAX := 1000        ; 1000% = 10x fire rate (Double Tap style)
global UNIVERSAL_DAMAGE_MULTIPLIER := 400     ; 400% damage for all weapons
global UNIVERSAL_RECOIL_REDUCTION := 100      ; 100% recoil elimination for all weapons
global UNIVERSAL_RANGE_MULTIPLIER := 300      ; 300% range for all weapons
global UNIVERSAL_PENETRATION_BOOST := 500     ; 500% penetration for all weapons
global UNIVERSAL_ZOMBIE_DAMAGE := 600         ; 600% zombie damage for all weapons

; ====== SYSTEM VARIABLES =======
global SYSTEM_ENABLED := false
global CONTROLLER_CONNECTED := false

; Universal Enhancement Toggles (GUI Controlled Only)
global UNIVERSAL_FIRE_RATE_ENABLED := false
global UNIVERSAL_ZERO_RECOIL_ENABLED := false
global UNIVERSAL_DAMAGE_BOOST_ENABLED := false
global UNIVERSAL_RANGE_BOOST_ENABLED := false
global UNIVERSAL_PENETRATION_ENABLED := false
global UNIVERSAL_ZOMBIE_MODE_ENABLED := false

; Movement & Utility Enhancements (GUI Controlled Only)
global SUPER_JUMP_ENABLED := false
global NO_FALL_DAMAGE_ENABLED := false
global INFINITE_AMMO_ENABLED := false
global INSTANT_RELOAD_ENABLED := false
global SPEED_BOOST_ENABLED := false
global WALL_HACK_SIMULATION_ENABLED := false
global AUTO_HEAL_ENABLED := false
global INFINITE_SPRINT_ENABLED := false

; GUI-Controlled Settings
global CURRENT_FIRE_RATE_MULTIPLIER := 200    ; Start at 200% (2x fire rate)
global CURRENT_DAMAGE_MULTIPLIER := 400       ; GUI adjustable damage
global CURRENT_RECOIL_REDUCTION := 100        ; GUI adjustable recoil reduction
global CURRENT_PENETRATION_BOOST := 500       ; GUI adjustable penetration

; Movement & Utility Settings (GUI Controlled)
global SUPER_JUMP_HEIGHT := 300               ; 300% jump height
global SPEED_BOOST_MULTIPLIER := 200          ; 200% movement speed
global RELOAD_SPEED_MULTIPLIER := 500         ; 500% reload speed
global HEAL_RATE_MULTIPLIER := 300            ; 300% healing rate

; Controller State Variables (RIGHT TRIGGER ONLY)
global CURRENT_RIGHT_TRIGGER := 0
global TRIGGER_THRESHOLD := 30                ; Minimum trigger pressure to activate

; ====== XINPUT CONTROLLER FUNCTIONS (RIGHT TRIGGER ONLY) =======
InitializeXInput() {
    global
    
    try {
        DllCall("LoadLibrary", "Str", "xinput1_4.dll")
        TrayTip, Universal Enhancement, ✅ XInput loaded - RIGHT TRIGGER detection ready!, 3, 1
        return true
    } catch {
        try {
            DllCall("LoadLibrary", "Str", "xinput1_3.dll")
            TrayTip, Universal Enhancement, ✅ XInput 1.3 loaded - RIGHT TRIGGER detection ready!, 3, 1
            return true
        } catch {
            TrayTip, Universal Enhancement, ❌ XInput not available - GUI-only mode, 5, 2
            return false
        }
    }
}

GetControllerState(controllerIndex := 0) {
    global
    
    VarSetCapacity(state, 16, 0)
    result := DllCall("xinput1_4\XInputGetState", "UInt", controllerIndex, "Ptr", &state, "UInt")
    
    if (result != 0) {
        CONTROLLER_CONNECTED := false
        return false
    }
    
    CONTROLLER_CONNECTED := true
    
    ; Extract ONLY RIGHT TRIGGER data (no button detection)
    CURRENT_RIGHT_TRIGGER := NumGet(state, 7, "UChar")
    
    return true
}

; ====== UNIVERSAL WEAPON ENHANCEMENT FUNCTIONS =======

; 🔥 UNIVERSAL FIRE RATE SYSTEM (GUI Controlled, RIGHT TRIGGER Activated)
ExecuteUniversalFireRate() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_FIRE_RATE_ENABLED) {
        return
    }
    
    ; Check if right trigger is pressed for universal fire rate boost
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Calculate fire rate based on GUI-set multiplier
        baseDelay := 50  ; Base fire rate delay
        enhancedDelay := baseDelay / (CURRENT_FIRE_RATE_MULTIPLIER / 100.0)
        
        ; Apply fire rate enhancement to current weapon (whatever it is)
        if (CURRENT_FIRE_RATE_MULTIPLIER >= 400) {
            ; For extreme fire rates (400%+), use burst firing
            burstCount := Round(CURRENT_FIRE_RATE_MULTIPLIER / 200)
            Loop, %burstCount% {
                Click
                Sleep, Round(enhancedDelay / burstCount)
            }
        } else {
            ; For moderate fire rates, use single enhanced shots
            Click
            Sleep, Round(enhancedDelay)
        }
    }
}

; 🎯 UNIVERSAL ZERO RECOIL (GUI Controlled, RIGHT TRIGGER Activated)
ExecuteUniversalZeroRecoil() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_ZERO_RECOIL_ENABLED) {
        return
    }
    
    ; Apply zero recoil to whatever weapon is currently equipped
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Universal recoil compensation for any weapon type
        recoilCompensation := CURRENT_RECOIL_REDUCTION / 100.0
        
        ; Apply recoil compensation (works for all weapon types)
        Random, microAdjustX, -1, 1
        recoilY := Round(3 * recoilCompensation)
        
        DllCall("mouse_event", "UInt", 0x0001, "Int", microAdjustX, "Int", recoilY, "UInt", 0, "UPtr", 0)
        
        ; Additional stability for high fire rate weapons
        if (CURRENT_FIRE_RATE_MULTIPLIER >= 500) {
            Sleep, 1
            Random, stabilityX, 0, 1
            DllCall("mouse_event", "UInt", 0x0001, "Int", -stabilityX, "Int", 1, "UInt", 0, "UPtr", 0)
        }
    }
}

; 💥 UNIVERSAL DAMAGE BOOST (GUI Controlled, RIGHT TRIGGER Activated)
ExecuteUniversalDamageBoost() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_DAMAGE_BOOST_ENABLED) {
        return
    }
    
    ; Apply damage boost to any weapon type
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Universal damage enhancement through multi-hit simulation
        damageIntensity := CURRENT_DAMAGE_MULTIPLIER / 100.0
        
        Random, damageBoost, 1, 100
        if (damageBoost <= 50) {  ; 50% chance for damage boost
            ; Multi-hit simulation for universal damage boost
            extraHits := Round(damageIntensity / 2)
            Loop, %extraHits% {
                Click
                Sleep, 2
            }
        }
    }
}

; 🛡️ UNIVERSAL PENETRATION (GUI Controlled, RIGHT TRIGGER Activated)
ExecuteUniversalPenetration() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_PENETRATION_ENABLED) {
        return
    }
    
    ; Apply penetration boost to any weapon
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Universal penetration enhancement
        penetrationPower := CURRENT_PENETRATION_BOOST / 100.0
        
        Random, penetrationHit, 1, 100
        if (penetrationHit <= 40) {  ; 40% chance for penetration boost
            ; Multi-shot penetration simulation
            penetrationShots := Round(penetrationPower / 2)
            Loop, %penetrationShots% {
                Click
                Sleep, 1
            }
        }
    }
}

; 🧟 UNIVERSAL ZOMBIE DAMAGE (GUI Controlled, RIGHT TRIGGER Activated)
ExecuteUniversalZombieDamage() {
    global
    
    if (!SYSTEM_ENABLED || !UNIVERSAL_ZOMBIE_MODE_ENABLED) {
        return
    }
    
    ; Apply zombie damage boost to any weapon in MWZ
    if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
        ; Universal zombie damage enhancement
        zombieDamage := UNIVERSAL_ZOMBIE_DAMAGE / 100.0  ; 6.0x zombie damage
        
        Random, zombieHit, 1, 100
        if (zombieHit <= 60) {  ; 60% chance for zombie damage boost
            ; Zombie-specific damage simulation
            zombieShots := Round(zombieDamage / 3)
            Loop, %zombieShots% {
                Click
                Sleep, 1
            }
        }
    }
}

; ====== STARTUP SEQUENCE =======
TrayTip, Universal Enhancement, 🔥 MW3/MWZ UNIVERSAL ENHANCEMENT - GUI ONLY VERSION, 3, 1
Sleep, 1000

; Initialize XInput (for RIGHT TRIGGER detection only)
InitializeXInput()

TrayTip, Universal Enhancement, ✅ GUI-only controls ready! No controller button conflicts!, 3, 1
Sleep, 1000

; ====== CREATE GUI-ONLY UNIVERSAL ENHANCEMENT INTERFACE =======
Gui, Destroy
Gui, Color, 0x0f0f23
Gui, Font, s14 Bold cLime, Arial
Gui, Add, Text, x10 y10 w480 Center, 🔥 UNIVERSAL ENHANCEMENT - GUI ONLY

Gui, Font, s10 cYellow, Arial
Gui, Add, Text, x20 y40 w460 Center, No Controller Button Conflicts - RIGHT TRIGGER Activation Only!

Gui, Font, s9 cWhite, Arial
Gui, Add, Text, x20 y65 w460, ✅ Mystery Box | ✅ Wall-Buy | ✅ Starting | ✅ Wonder Weapons

; Controller status (RIGHT TRIGGER detection only)
Gui, Add, Text, x20 y90 w460 vControllerStatus, RIGHT TRIGGER Detection: Checking...

; System master switch
Gui, Add, CheckBox, x20 y115 w460 vSystemBox gToggleSystem, 🚀 Enable Universal Enhancement System (Master Switch)

; Universal God Mode (GUI Toggle)
Gui, Font, s11 Bold cRed, Arial
Gui, Add, CheckBox, x20 y145 w460 vGodModeBox gToggleGodMode, 🚀 UNIVERSAL GOD MODE - ALL WEAPONS OVERPOWERED!
Gui, Font, s9 cWhite, Arial

; Fire Rate Control (GUI Only)
Gui, Font, s10 Bold cCyan, Arial
Gui, Add, Text, x20 y175 w460, 🔥 FIRE RATE CONTROL (ALL WEAPONS):
Gui, Font, s9 cWhite, Arial

Gui, Add, Text, x30 y200 w200, Fire Rate Multiplier:
Gui, Add, Slider, x30 y220 w300 h30 Range100-1000 vFireRateSlider gUpdateFireRate, %CURRENT_FIRE_RATE_MULTIPLIER%
Gui, Add, Text, x340 y230 w100 vFireRateText, %CURRENT_FIRE_RATE_MULTIPLIER%`%

; Damage Control (GUI Only)
Gui, Add, Text, x30 y260 w200, Damage Multiplier:
Gui, Add, Slider, x30 y280 w300 h30 Range100-1000 vDamageSlider gUpdateDamage, %CURRENT_DAMAGE_MULTIPLIER%
Gui, Add, Text, x340 y290 w100 vDamageText, %CURRENT_DAMAGE_MULTIPLIER%`%

; Recoil Control (GUI Only)
Gui, Add, Text, x30 y320 w200, Recoil Reduction:
Gui, Add, Slider, x30 y340 w300 h30 Range0-100 vRecoilSlider gUpdateRecoil, %CURRENT_RECOIL_REDUCTION%
Gui, Add, Text, x340 y350 w100 vRecoilText, %CURRENT_RECOIL_REDUCTION%`%

; Penetration Control (GUI Only)
Gui, Add, Text, x30 y380 w200, Penetration Boost:
Gui, Add, Slider, x30 y400 w300 h30 Range100-1000 vPenetrationSlider gUpdatePenetration, %CURRENT_PENETRATION_BOOST%
Gui, Add, Text, x340 y410 w100 vPenetrationText, %CURRENT_PENETRATION_BOOST%`%

; Universal Enhancement Toggles (GUI Only)
Gui, Font, s10 Bold cRed, Arial
Gui, Add, Text, x20 y440 w460, 🔥 UNIVERSAL ENHANCEMENTS (GUI CONTROLLED):
Gui, Font, s9 cWhite, Arial

Gui, Add, CheckBox, x30 y465 w430 vFireRateBox gToggleFireRate, 🔥 Universal Fire Rate - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y485 w430 vZeroRecoilBox gToggleZeroRecoil, 🎯 Universal Zero Recoil - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y505 w430 vDamageBoostBox gToggleDamageBoost, 💥 Universal Damage Boost - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y525 w430 vPenetrationBox gTogglePenetration, 🛡️ Universal Penetration - RIGHT TRIGGER Activated
Gui, Add, CheckBox, x30 y545 w430 vZombieModeBox gToggleZombieMode, 🧟 Universal Zombie Mode - RIGHT TRIGGER Activated

; Status and Controls
Gui, Add, Text, x20 y575 w460 vStatusText, Status: System OFF - Enable for universal weapon domination
Gui, Add, Text, x20 y595 w460, 🎮 Use GUI controls only - No controller button conflicts!
Gui, Add, Text, x20 y615 w460, 🔫 Hold RIGHT TRIGGER while firing to activate all enabled enhancements

Gui, Show, w500 h645, MW3/MWZ Universal Enhancement - GUI Only

TrayTip, Universal Enhancement, 🔥 GUI Ready! Configure settings here - No controller conflicts!, 3, 1

; ====== GUI CONTROL FUNCTIONS (NO CONTROLLER BUTTON CONFLICTS) =======
ToggleSystem:
    GuiControlGet, SystemEnabled,, SystemBox
    SYSTEM_ENABLED := SystemEnabled
    if (SYSTEM_ENABLED) {
        TrayTip, Universal System, 🚀 UNIVERSAL ENHANCEMENT SYSTEM ACTIVATED!, 3, 1
        GuiControl,, StatusText, Status: System ON - Universal enhancements ready for ALL weapons!
    } else {
        TrayTip, Universal System, ❌ Universal enhancement system deactivated, 3, 1
        GuiControl,, StatusText, Status: System OFF - Universal enhancements disabled
    }
return

ToggleGodMode:
    GuiControlGet, GodModeEnabled,, GodModeBox

    ; Toggle all universal enhancements via GUI
    UNIVERSAL_FIRE_RATE_ENABLED := GodModeEnabled
    UNIVERSAL_ZERO_RECOIL_ENABLED := GodModeEnabled
    UNIVERSAL_DAMAGE_BOOST_ENABLED := GodModeEnabled
    UNIVERSAL_RANGE_BOOST_ENABLED := GodModeEnabled
    UNIVERSAL_PENETRATION_ENABLED := GodModeEnabled
    UNIVERSAL_ZOMBIE_MODE_ENABLED := GodModeEnabled

    ; Update individual checkboxes
    GuiControl,, FireRateBox, %GodModeEnabled%
    GuiControl,, ZeroRecoilBox, %GodModeEnabled%
    GuiControl,, DamageBoostBox, %GodModeEnabled%
    GuiControl,, PenetrationBox, %GodModeEnabled%
    GuiControl,, ZombieModeBox, %GodModeEnabled%

    TrayTip, Universal God Mode, % (GodModeEnabled ? "🚀 UNIVERSAL GOD MODE ON! ALL WEAPONS OVERPOWERED!" : "Universal God Mode OFF"), 5, 1
return

; GUI Slider Updates (Real-time adjustment via GUI)
UpdateFireRate:
    GuiControlGet, FireRateValue,, FireRateSlider
    CURRENT_FIRE_RATE_MULTIPLIER := FireRateValue
    GuiControl,, FireRateText, %FireRateValue%`%
return

UpdateDamage:
    GuiControlGet, DamageValue,, DamageSlider
    CURRENT_DAMAGE_MULTIPLIER := DamageValue
    GuiControl,, DamageText, %DamageValue%`%
return

UpdateRecoil:
    GuiControlGet, RecoilValue,, RecoilSlider
    CURRENT_RECOIL_REDUCTION := RecoilValue
    GuiControl,, RecoilText, %RecoilValue%`%
return

UpdatePenetration:
    GuiControlGet, PenetrationValue,, PenetrationSlider
    CURRENT_PENETRATION_BOOST := PenetrationValue
    GuiControl,, PenetrationText, %PenetrationValue%`%
return

; Individual Enhancement Toggles (GUI Only)
ToggleFireRate:
    GuiControlGet, FireRateEnabled,, FireRateBox
    UNIVERSAL_FIRE_RATE_ENABLED := FireRateEnabled
return

ToggleZeroRecoil:
    GuiControlGet, ZeroRecoilEnabled,, ZeroRecoilBox
    UNIVERSAL_ZERO_RECOIL_ENABLED := ZeroRecoilEnabled
return

ToggleDamageBoost:
    GuiControlGet, DamageBoostEnabled,, DamageBoostBox
    UNIVERSAL_DAMAGE_BOOST_ENABLED := DamageBoostEnabled
return

TogglePenetration:
    GuiControlGet, PenetrationEnabled,, PenetrationBox
    UNIVERSAL_PENETRATION_ENABLED := PenetrationEnabled
return

ToggleZombieMode:
    GuiControlGet, ZombieModeEnabled,, ZombieModeBox
    UNIVERSAL_ZOMBIE_MODE_ENABLED := ZombieModeEnabled
return

GuiClose:
    TrayTip, Universal Enhancement, 👋 Universal weapon enhancement system shutting down..., 2, 1
    Sleep, 1000
    ExitApp

; ====== MAIN EXECUTION LOOP (RIGHT TRIGGER DETECTION ONLY) =======
Loop {
    Sleep, 16  ; ~60 FPS execution for maximum responsiveness

    ; Update controller state (RIGHT TRIGGER ONLY - no button detection)
    GetControllerState(0)

    ; Update RIGHT TRIGGER detection status in GUI
    if (CONTROLLER_CONNECTED) {
        triggerPressure := Round((CURRENT_RIGHT_TRIGGER / 255) * 100)
        triggerStatus := "Connected ✅ | RT Pressure: " . triggerPressure . "%"
        if (CURRENT_RIGHT_TRIGGER > TRIGGER_THRESHOLD) {
            triggerStatus .= " | 🔥 ACTIVE"
        }
    } else {
        triggerStatus := "Disconnected ❌ | GUI-only mode available"
    }
    GuiControl,, ControllerStatus, RIGHT TRIGGER Detection: %triggerStatus%

    ; Execute all universal enhancement systems (RIGHT TRIGGER activated only)
    if (SYSTEM_ENABLED) {
        ExecuteUniversalFireRate()
        ExecuteUniversalZeroRecoil()
        ExecuteUniversalDamageBoost()
        ExecuteUniversalPenetration()
        ExecuteUniversalZombieDamage()
    }
}

return
