@echo off
title MW3 AI Ultimate - Copy Interception Files
color 0A

echo.
echo ========================================
echo   COPYING INTERCEPTION FILES
echo ========================================
echo.

echo Copying required files from Interception folder...
echo.

REM Copy the installer
if exist "Interception/command line installer/install-interception.exe" (
    copy "Interception/command line installer/install-interception.exe" "install-interception.exe" >nul
    echo [✓] Copied install-interception.exe
) else (
    echo [✗] install-interception.exe not found at: Interception/command line installer/
    dir "Interception" /s | findstr "install-interception.exe"
)

REM Copy the DLL (64-bit version)
if exist "Interception/library/x64/interception.dll" (
    copy "Interception/library/x64/interception.dll" "interception.dll" >nul
    echo [✓] Copied interception.dll (x64)
) else (
    echo [✗] interception.dll not found at: Interception/library/x64/
    dir "Interception" /s | findstr "interception.dll"
)

REM Check if there's an uninstaller (might not exist in all versions)
if exist "Interception/command line installer/uninstall-interception.exe" (
    copy "Interception/command line installer/uninstall-interception.exe" "uninstall-interception.exe" >nul
    echo [✓] Copied uninstall-interception.exe
) else (
    echo [!] uninstall-interception.exe not found (this is normal for some versions)
)

echo.
echo ========================================
echo   VERIFICATION
echo ========================================
echo.

if exist "install-interception.exe" (
    echo [✓] install-interception.exe - READY
    set "installer_ready=1"
) else (
    echo [✗] install-interception.exe - MISSING
    set "installer_ready=0"
)

if exist "interception.dll" (
    echo [✓] interception.dll - READY
    set "dll_ready=1"
) else (
    echo [✗] interception.dll - MISSING
    set "dll_ready=0"
)

if exist "uninstall-interception.exe" (
    echo [✓] uninstall-interception.exe - READY
) else (
    echo [!] uninstall-interception.exe - Not available
)

echo.

if "%installer_ready%"=="1" if "%dll_ready%"=="1" (
    echo ========================================
    echo   SUCCESS! READY TO INSTALL DRIVER
    echo ========================================
    echo.
    echo All required files are now in place!
    echo.
    echo NEXT STEPS:
    echo 1. Install the Interception driver
    echo 2. Restart your computer
    echo 3. Run the MW3 AI Ultimate System
    echo.
    echo ========================================
    echo.
    choice /c YN /m "Install Interception driver now (Y/N)"
    
    if errorlevel 2 goto :skip_install
    if errorlevel 1 goto :install_driver
    
    :install_driver
    echo.
    echo Installing Interception driver...
    echo.
    echo [!] You will see a UAC prompt - click YES to continue
    echo.
    
    REM Try to run the installer with admin privileges
    powershell -Command "Start-Process 'install-interception.exe' -Verb RunAs -Wait"
    
    echo.
    if %errorlevel% == 0 (
        echo [✓] Driver installation completed successfully!
        echo.
        echo ========================================
        echo     RESTART REQUIRED!
        echo ========================================
        echo.
        echo IMPORTANT: You MUST restart your computer now
        echo           for the driver to work properly.
        echo.
        echo After restart:
        echo 1. Run Complete_MW3_AI_Ultimate_v7.ahk as Administrator
        echo 2. The system will detect Interception automatically
        echo 3. Enjoy maximum stealth hardware input!
        echo.
        choice /c YN /m "Restart computer now (Y/N)"
        
        if errorlevel 1 (
            echo Restarting computer in 10 seconds...
            echo Press Ctrl+C to cancel
            timeout /t 10
            shutdown /r /t 0
        )
        
    ) else (
        echo [!] Driver installation may have failed or was cancelled.
        echo.
        echo Try manually:
        echo 1. Right-click install-interception.exe
        echo 2. Select "Run as administrator"
        echo 3. Follow the prompts
        echo.
    )
    
    :skip_install
    echo.
    echo Manual installation instructions:
    echo 1. Right-click install-interception.exe
    echo 2. Select "Run as administrator"  
    echo 3. Restart your computer
    echo 4. Run MW3 AI Ultimate System
    echo.
    
) else (
    echo ========================================
    echo   ERROR: MISSING FILES
    echo ========================================
    echo.
    echo Some required files could not be copied.
    echo Please check that the Interception folder is complete.
    echo.
)

echo ========================================
echo Press any key to exit...
pause >nul
