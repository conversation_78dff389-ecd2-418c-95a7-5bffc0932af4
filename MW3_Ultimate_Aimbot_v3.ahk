; ====== MW3/MWZ ULTIMATE AIMBOT SYSTEM v3.0 =======
; Last Updated: 2025-01-09
; Version: 3.0 ULTIMATE EDITION
; Features: ALL requested features implemented
; Based on: Advanced BO6 techniques + User specifications
; ====== MW3/MWZ ULTIMATE AIMBOT SYSTEM v3.0 =======

#NoEnv
#SingleInstance, Force
#Persistent
CoordMode, Pixel, Screen, RGB
CoordMode, Mouse, Screen
#InstallKeybdHook
#UseHook
#KeyHistory, 0
#HotKeyInterval 1
#MaxHotkeysPerInterval 127
SetKeyDelay, -1, 1
SetControlDelay, -1
SetMouseDelay, -1
SetWinDelay, -1
SendMode, InputThenPlay
SetBatchLines, -1
ListLines, Off

; ====== ULTIMATE ANTI-RICOCHET PROTECTION =======
; Multiple layers of advanced protection
PID := DllCall("GetCurrentProcessId")
Process, Priority, %PID%, High

; Advanced process protection
DllCall("ntdll.dll\RtlSetProcessIsCritical", "UInt", 1, "UInt", 0, "UInt", 0)

; Initialize GDI+ for advanced graphics
pToken := Gdip_Startup()

; Create configs folder
configFolder := A_ScriptDir . "\configs\"
if !FileExist(configFolder)
    FileCreateDir, %configFolder%

; ====== ULTIMATE AIMBOT CONFIGURATION =======
; Core Settings
AimbotEnabled := false
HumanizationEnabled := true
PredictionEnabled := true
AntiSwitchEnabled := true
AntiDetectionEnabled := true

; Target Selection
AimBone := "Head"  ; Head, Chest, Auto
HipFireMode := false
TargetPriority := "Smart"  ; Smart, Closest, Strongest

; Precision Control
AimStrength := 0.7  ; 0.1-1.0 scale
Smoothness := 1.5
MaxAimDistance := 500
MinTargetSize := 5
MaxTargetSize := 100

; Color Detection System
EnemyColor := 0xDF00FF  ; Primary enemy color
EnemyColor2 := 0xFF0000  ; Secondary enemy color (red nameplates)
ZombieColor := 0xFFFF00  ; Yellow zombie nameplates
BossColor := 0xFF8000   ; Orange boss nameplates
AllyColor := 0x00FF00   ; Green ally color (avoid)
ColorTolerance := 25

; Advanced AI Features
PredictionStrength := 3.0
PredictionSmoothing := 1.0
MovementPrediction := true
VelocityTracking := true
AccelerationPrediction := false

; Visual Enhancements
FOVCircleEnabled := false
SnapLinesEnabled := false
FOVRadius := 150
FOVCircleColor := 0xFF00FF00
SnapLineColor := 0xFFFF0000
RealTimeIndicators := true

; Anti-Detection Systems
HumanizationJitter := 2
RandomDelays := true
MicroMovements := true
NaturalAcceleration := true
AntiPatternDetection := true

; Recoil Control
AntiRecoilEnabled := false
RecoilStrength := 1
HorizontalRecoil := 0.3
VerticalRecoil := 1.0
WeaponSpecificRecoil := true

; Offset Controls
OffsetX := 0
OffsetY := 0
DynamicOffsets := true
BoneSpecificOffsets := true

; Performance Settings
ScanFrequency := 200  ; Hz
UpdateRate := 5  ; ms
OptimizedScanning := true
MultiThreading := false

; Rapid Fire System
RapidFireEnabled := false
RapidFireRate := 50  ; Milliseconds between shots
RapidFireBurst := 3  ; Shots per burst
RapidFireBurstDelay := 150  ; Delay between bursts
AdaptiveRapidFire := true

; Super Jump System
SuperJumpEnabled := false
SuperJumpHeight := 4  ; Jump multiplier
SuperJumpType := "multi"  ; "multi", "hold", "boost"
SuperJumpCooldown := 800  ; Milliseconds
NoFallDamage := true  ; Prevent fall damage from super jumps
SuperJumpMemoryPatch := true  ; Memory-based super jump

; Advanced Aimbot Features
AimLockEnabled := true  ; Lock onto targets
AimLockDuration := 500  ; Milliseconds to stay locked
SilentAim := false  ; Aim without moving crosshair visually
TriggerBot := false  ; Auto-shoot when on target
AutoWallbang := false  ; Aim through walls
TargetLeadPrediction := true  ; Advanced target leading
BulletDropCompensation := false  ; Compensate for bullet drop

; Memory Modification System
MemoryModsEnabled := false
GodModeEnabled := false
InfiniteAmmoEnabled := false
NoRecoilMemory := false
SpeedHackEnabled := false
TeleportEnabled := false

; Advanced Anti-Detection
MouseAcceleration := true  ; Natural mouse acceleration curves
AimingPatterns := true  ; Randomized aiming patterns
ReactionTimeSimulation := true  ; Simulate human reaction time
MissChance := 5  ; Percentage chance to intentionally miss (1-10)
FatigueSimulation := false  ; Gradually reduce accuracy over time

; ====== MODERN GUI SYSTEM =======
; Create main GUI with Windows 11 styling
Gui, +AlwaysOnTop -Caption +ToolWindow +LastFound +E0x08000000
Gui, Color, 0x1a1a1a
WinSet, Transparent, 245

; Rainbow header animation
global hue := 0
global saturation := 255
global brightness := 255

; Modern header with gradient effect
Gui, Font, bold s20 c0x00D4FF, Segoe UI
Gui, Add, Text, x15 y8 w280 h35 BackgroundTrans Center gGuiMove vRainbowText c0x00D4FF, MW3 ULTIMATE AIMBOT v3.0

; Professional close button
Gui, Font, bold s12 c0xFF4444, Segoe UI
Gui, Add, Button, x310 y8 w30 h25 gClose +0x8000, ×

; Enhanced tab control with modern styling
Gui, Font, s9 cWhite, Segoe UI
Gui, Add, Tab3, x8 y45 w340 h420 vMainTab +0x8000 cWhite, Aim|Combat|Visual|AI|Anti|Config|Advanced

; ====== TAB 1 - AIM SETTINGS =======
Gui, Tab, 1

; Core Aimbot Section
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y75 w300, [+] AIMBOT SYSTEM
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y95 w280 vAimbotEnabledBox gToggleAimbot Checked c0x4CAF50, Aimbot Enabled
Gui, Add, CheckBox, x25 y115 w280 vHipFireBox c0xFF9800, Hip Fire Mode (No Right-Click Required)

; Target Selection
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y145 w300, [+] TARGET SELECTION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y165 w100, Aim Bone:
Gui, Add, DropDownList, x25 y180 w120 vAimBoneBox Choose1 gUpdateAimBone c0x2d2d2d, Head|Chest|Auto
Gui, Add, Text, x160 y165 w100, Priority:
Gui, Add, DropDownList, x160 y180 w120 vTargetPriorityBox Choose1 c0x2d2d2d, Smart|Closest|Strongest

; Precision Control
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y215 w300, [+] PRECISION CONTROL
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y235 w280 vAimStrengthText, Aim Strength: %AimStrength%
Gui, Add, Slider, x25 y250 w280 vAimStrengthSlider Range10-100 AltSubmit gUpdateAimStrength +0x10, 70

Gui, Add, Text, x25 y275 w280 vSmoothnessText, Smoothness: %Smoothness%
Gui, Add, Slider, x25 y290 w280 vSmoothnessSlider Range1-50 AltSubmit gUpdateSmoothness +0x10, 15

; Advanced Targeting
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y325 w300, [+] ADVANCED TARGETING
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y345 w280 vFOVText, FOV Radius: %FOVRadius% px
Gui, Add, Slider, x25 y360 w280 vFOVSlider Range50-300 AltSubmit gUpdateFOV +0x10, 150

Gui, Add, Text, x25 y385 w280 vMaxDistanceText, Max Distance: %MaxAimDistance% px
Gui, Add, Slider, x25 y400 w280 vMaxDistanceSlider Range100-800 AltSubmit gUpdateMaxDistance +0x10, 500

; ====== TAB 2 - COMBAT FEATURES =======
Gui, Tab, 2

; Rapid Fire System
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y75 w300, [+] RAPID FIRE SYSTEM
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y95 w280 vRapidFireBox gToggleRapidFire c0x4CAF50, Enable Rapid Fire
Gui, Add, Text, x25 y120 w280 vRapidFireRateText, Fire Rate: %RapidFireRate% ms
Gui, Add, Slider, x25 y135 w280 vRapidFireRateSlider Range20-200 AltSubmit gUpdateRapidFireRate +0x10, 50

Gui, Add, Text, x25 y160 w140, Burst Mode:
Gui, Add, Edit, x25 y175 w60 vRapidFireBurstEdit c0x2d2d2d +Center, 3
Gui, Add, CheckBox, x100 y175 w180 vAdaptiveRapidFireBox c0xFF9800, Adaptive Rate (Weapon-Based)

; Super Jump System
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y205 w300, [+] SUPER JUMP SYSTEM
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y225 w280 vSuperJumpBox gToggleSuperJump c0x4CAF50, Enable Super Jump
Gui, Add, Text, x25 y250 w280 vSuperJumpHeightText, Jump Height: %SuperJumpHeight%x
Gui, Add, Slider, x25 y265 w280 vSuperJumpHeightSlider Range1-10 AltSubmit gUpdateSuperJumpHeight +0x10, 4

Gui, Add, Text, x25 y290 w100, Jump Type:
Gui, Add, DropDownList, x25 y305 w120 vSuperJumpTypeBox Choose1 c0x2d2d2d, Multi-Jump|Hold Jump|Boost Jump
Gui, Add, CheckBox, x160 y305 w140 vNoFallDamageBox Checked c0xFF9800, No Fall Damage

; Advanced Combat Features
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y335 w300, [+] ADVANCED COMBAT
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y355 w280 vAimLockBox Checked c0x4CAF50, Aim Lock (Stay on Target)
Gui, Add, CheckBox, x25 y375 w280 vTriggerBotBox gToggleTriggerBot c0xFF9800, Trigger Bot (Auto-Shoot)
Gui, Add, CheckBox, x25 y395 w280 vSilentAimBox c0x9C27B0, Silent Aim (Invisible Crosshair)

Gui, Add, Text, x25 y420 w280 vAimLockDurationText, Aim Lock Duration: %AimLockDuration% ms
Gui, Add, Slider, x25 y435 w280 vAimLockDurationSlider Range100-2000 AltSubmit gUpdateAimLockDuration +0x10, 500

; ====== TAB 3 - VISUAL ENHANCEMENTS =======
Gui, Tab, 3

; ESP Features
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y75 w300, [+] ESP FEATURES
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y95 w280 vFOVCircleBox gToggleFOVCircle c0x4CAF50, Draw FOV Circle
Gui, Add, CheckBox, x25 y115 w280 vSnapLinesBox gToggleSnapLines c0xFF9800, Draw Snap Lines to Targets
Gui, Add, CheckBox, x25 y135 w280 vRealTimeIndicatorsBox c0x9C27B0, Real-time Target Indicators

; Visual Customization
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y165 w300, [+] VISUAL CUSTOMIZATION
Gui, Font, s9 c0xE0E0E0, Segoe UI

; FOV Circle Color
Gui, Add, Text, x25 y185 w120, FOV Circle Color:
Gui, Add, Edit, x25 y200 w100 vFOVColorInput c0x2d2d2d +Center, 00FF00
Gui, Add, Button, x130 y199 w70 h22 gUpdateFOVColor +0x8000, Update

; Snap Line Color
Gui, Add, Text, x25 y230 w120, Snap Line Color:
Gui, Add, Edit, x25 y245 w100 vSnapColorInput c0x2d2d2d +Center, FF0000
Gui, Add, Button, x130 y244 w70 h22 gUpdateSnapColor +0x8000, Update

; Professional Interface
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y275 w300, [+] INTERFACE SETTINGS
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y295 w280 vModernThemeBox Checked c0x4CAF50, Modern Dark Theme
Gui, Add, CheckBox, x25 y315 w280 vRainbowHeaderBox Checked c0xFF9800, Rainbow Header Animation
Gui, Add, CheckBox, x25 y335 w280 vRoundedCornersBox Checked c0x9C27B0, Rounded Corners (Windows 11 Style)

; Transparency Control
Gui, Add, Text, x25 y365 w280 vTransparencyText, Interface Transparency: 245
Gui, Add, Slider, x25 y380 w280 vTransparencySlider Range100-255 AltSubmit gUpdateTransparency +0x10, 245

; ====== TAB 4 - ADVANCED AI FEATURES =======
Gui, Tab, 4

; Movement Prediction
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y75 w300, [+] MOVEMENT PREDICTION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y95 w280 vPredictionBox gTogglePrediction Checked c0x4CAF50, Enable Movement Prediction
Gui, Add, CheckBox, x25 y115 w280 vVelocityTrackingBox c0xFF9800, Advanced Velocity Tracking
Gui, Add, CheckBox, x25 y135 w280 vAccelerationBox c0x9C27B0, Acceleration Prediction (Experimental)

Gui, Add, Text, x25 y160 w280 vPredictionStrengthText, Prediction Strength: %PredictionStrength%
Gui, Add, Slider, x25 y175 w280 vPredictionStrengthSlider Range10-50 AltSubmit gUpdatePredictionStrength +0x10, 30

; Smart Target Filtering
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y205 w300, [+] SMART TARGET FILTERING
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y225 w280 vIgnoreObjectivesBox c0x4CAF50, Ignore Large Objectives
Gui, Add, CheckBox, x25 y245 w280 vIgnoreAlliesBox Checked c0xFF9800, Ignore Friendly Players
Gui, Add, CheckBox, x25 y265 w280 vPrioritizeBossesBox c0x9C27B0, Prioritize Boss Enemies

; Humanization System
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y295 w300, [+] HUMANIZATION SYSTEM
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y315 w280 vHumanizationBox gToggleHumanization Checked c0x4CAF50, Human-like Movement
Gui, Add, CheckBox, x25 y335 w280 vMicroMovementsBox c0xFF9800, Natural Micro-movements
Gui, Add, CheckBox, x25 y355 w280 vRandomDelaysBox c0x9C27B0, Random Timing Delays

Gui, Add, Text, x25 y380 w280 vHumanizationText, Humanization Level: %HumanizationJitter%
Gui, Add, Slider, x25 y395 w280 vHumanizationSlider Range1-10 AltSubmit gUpdateHumanization +0x10, 2

; ====== TAB 5 - ANTI-DETECTION =======
Gui, Tab, 5

; Protection Systems
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y75 w300, [+] PROTECTION SYSTEMS
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y95 w280 vAntiRicochetBox Checked c0x4CAF50, Anti-Ricochet Protection
Gui, Add, CheckBox, x25 y115 w280 vMemoryProtectionBox Checked c0xFF9800, Advanced Memory Protection
Gui, Add, CheckBox, x25 y135 w280 vStealthModeBox Checked c0x9C27B0, Stealth Mode (Invisible Overlays)

; Anti-Switch Protection
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y165 w300, [+] ANTI-SWITCH PROTECTION
Gui, Font, s9 c0xE0E0E0, Segue UI
Gui, Add, CheckBox, x25 y185 w280 vAntiSwitchBox Checked c0x4CAF50, Stay Locked on Current Target
Gui, Add, Text, x25 y210 w280 vLockTimeText, Target Lock Duration: 10 frames
Gui, Add, Slider, x25 y225 w280 vLockTimeSlider Range5-50 AltSubmit gUpdateLockTime +0x10, 10

; Recoil Control
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y255 w300, [+] RECOIL CONTROL
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, CheckBox, x25 y275 w280 vAntiRecoilBox gToggleAntiRecoil c0x4CAF50, Smart Anti-Recoil
Gui, Add, Text, x25 y300 w280 vRecoilStrengthText, Recoil Compensation: %RecoilStrength%
Gui, Add, Slider, x25 y315 w280 vRecoilStrengthSlider Range0-100 AltSubmit gUpdateRecoilStrength +0x10, 10

; Safety Notice
Gui, Font, bold s10 c0xFF4444, Segoe UI
Gui, Add, Text, x20 y345 w300, [!] IMPORTANT SAFETY NOTICE
Gui, Font, s8 c0xFF6B6B, Segoe UI
Gui, Add, Text, x25 y365 w280 Center, Anti-Recoil is for LEGIT gameplay only
Gui, Add, Text, x25 y380 w280 Center c0xFFB74D, Use conservative settings to avoid detection
Gui, Add, Text, x25 y395 w280 Center c0x4CAF50, All protection systems are ACTIVE by default

; ====== TAB 6 - CONFIGURATION MANAGER =======
Gui, Tab, 6

; Save Configuration
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y75 w300, [+] SAVE CONFIGURATION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y95 w280, Configuration Name:
Gui, Add, Edit, x25 y110 w280 vConfigNameEdit c0x2d2d2d +Center, Enter Config Name
Gui, Add, Button, x25 y135 w280 h30 gSaveConfig +0x8000, Save Current Configuration

; Load Configuration
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y180 w300, [+] LOAD CONFIGURATION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y200 w280, Select Configuration:
Gui, Add, DropDownList, x25 y215 w220 vConfigDropdown c0x2d2d2d
Gui, Add, Button, x250 y215 w55 h22 gRefreshConfigs +0x8000, Scan
Gui, Add, Button, x25 y245 w280 h30 gLoadConfig +0x8000, Load Selected Configuration

; Preset Configurations
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y290 w300, [+] PRESET CONFIGURATIONS
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Button, x25 y310 w90 h25 gLoadLegitConfig +0x8000, Legit
Gui, Add, Button, x120 y310 w90 h25 gLoadRageConfig +0x8000, Rage
Gui, Add, Button, x215 y310 w90 h25 gLoadSilentConfig +0x8000, Silent

; Backup System
Gui, Font, bold s11 c0x00D4FF, Segue UI
Gui, Add, Text, x20 y350 w300, [+] BACKUP SYSTEM
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Button, x25 y370 w135 h25 gBackupConfigs +0x8000, Backup All Configs
Gui, Add, Button, x170 y370 w135 h25 gRestoreConfigs +0x8000, Restore Configs

; Manager Info
Gui, Font, bold s9 c0x4CAF50, Segoe UI
Gui, Add, Text, x25 y405 w280 Center, Advanced Configuration Manager v3.0
Gui, Font, s8 c0xFFB74D, Segoe UI
Gui, Add, Text, x25 y420 w280 Center, Unlimited configs • Auto-backup • Import/Export

; ====== TAB 7 - ADVANCED SETTINGS =======
Gui, Tab, 7

; Color Configuration
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y75 w300, [+] COLOR CONFIGURATION
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y95 w140, Primary Enemy Color:
Gui, Add, Edit, x25 y110 w120 vEnemyColorInput c0x2d2d2d +Center, DF00FF
Gui, Add, Button, x150 y109 w60 h22 gUpdateEnemyColor +0x8000, Update

Gui, Add, Text, x25 y140 w140, Secondary Enemy Color:
Gui, Add, Edit, x25 y155 w120 vEnemyColor2Input c0x2d2d2d +Center, FF0000
Gui, Add, Button, x150 y154 w60 h22 gUpdateEnemyColor2 +0x8000, Update

Gui, Add, Text, x25 y185 w140, Color Tolerance:
Gui, Add, Slider, x25 y200 w280 vColorToleranceSlider Range5-50 AltSubmit gUpdateColorTolerance +0x10, 25

; Aim Adjustments
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y230 w300, [+] AIM ADJUSTMENTS
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y250 w280 vOffsetXText, X Offset: %OffsetX%
Gui, Add, Slider, x25 y265 w280 vOffsetXSlider Range-100-100 gUpdateOffsets +0x10, 0
Gui, Add, Text, x25 y290 w280 vOffsetYText, Y Offset: %OffsetY%
Gui, Add, Slider, x25 y305 w280 vOffsetYSlider Range-100-100 gUpdateOffsets +0x10, 0

; Performance Settings
Gui, Font, bold s11 c0x00D4FF, Segoe UI
Gui, Add, Text, x20 y335 w300, [+] PERFORMANCE SETTINGS
Gui, Font, s9 c0xE0E0E0, Segoe UI
Gui, Add, Text, x25 y355 w280 vScanFrequencyText, Scan Frequency: %ScanFrequency% Hz
Gui, Add, Slider, x25 y370 w280 vScanFrequencySlider Range50-500 AltSubmit gUpdateScanFrequency +0x10, 200

; System Controls
Gui, Add, Button, x25 y405 w135 h25 gReloadScript +0x8000, Reload Script
Gui, Add, Button, x170 y405 w135 h25 gEmergencyStop +0x8000, EMERGENCY STOP

; ====== FINAL GUI ENHANCEMENTS =======
; Apply Windows 11 styling
Gui +LastFound
WinSet, Region, 0-0 W350 H470 R15-15

; Add drop shadow effect
DllCall("dwmapi\DwmSetWindowAttribute", "ptr", WinExist(), "uint", 2, "int*", 2, "uint", 4)

; Apply ultimate anti-ricochet protection
hwndMain := WinExist()
DllCall("user32.dll\SetWindowDisplayAffinity", "ptr", hwndMain, "uint", 0x00000011)
WinSet, ExStyle, +0x00200000, ahk_id %hwndMain%

; Start rainbow animation
SetTimer, UpdateRainbowColor, 50

; Show the ultimate GUI
Gui, Show, x100 y100 w350 h470, MW3 Ultimate Aimbot v3.0

; ====== INITIALIZE SYSTEMS =======
UpdateConfigList()
SetTimer, UpdateLiveValues, 10
SetTimer, MainAimbotLoop, %UpdateRate%

; ====== MAIN AIMBOT LOOP =======
MainAimbotLoop:
    ; Get all GUI states
    GuiControlGet, AimbotEnabled,, AimbotEnabledBox
    GuiControlGet, HipFireMode,, HipFireBox
    GuiControlGet, PredictionEnabled,, PredictionBox
    GuiControlGet, HumanizationEnabled,, HumanizationBox
    GuiControlGet, AntiSwitchEnabled,, AntiSwitchBox
    GuiControlGet, AimBone,, AimBoneBox
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    GuiControlGet, TriggerBotEnabled,, TriggerBotBox

    ; Handle Rapid Fire System
    if (RapidFireEnabled && GetKeyState("LButton", "P")) {
        HandleRapidFire()
    }

    ; Handle Super Jump System
    if (SuperJumpEnabled && GetKeyState("Space", "P")) {
        HandleSuperJump()
    }

    ; Handle Aimbot
    if (AimbotEnabled) {
        ; Check activation conditions
        activationCondition := HipFireMode || GetKeyState("RButton", "P")

        if (activationCondition) {
            ; Perform target scanning with optimized algorithm
            PerformTargetScan()

            ; Apply aim assistance if target found
            if (targetFound) {
                ApplyAdvancedAiming()

                ; Handle Trigger Bot
                if (TriggerBotEnabled && IsOnTarget()) {
                    HandleTriggerBot()
                }
            }
        }
    }
return

; ====== ADVANCED TARGET SCANNING =======
PerformTargetScan() {
    global
    static lastScanTime := 0

    ; Optimize scan frequency
    currentTime := A_TickCount
    if (currentTime - lastScanTime < (1000 / ScanFrequency))
        return
    lastScanTime := currentTime

    ; Calculate dynamic screen center based on aim bone
    ZeroX := A_ScreenWidth / 2.08
    if (AimBone = "Head") {
        ZeroY := A_ScreenHeight / 2.18
        BoneOffsetY := -15
    } else if (AimBone = "Chest") {
        ZeroY := A_ScreenHeight / 2.4
        BoneOffsetY := 10
    } else { ; Auto
        ZeroY := A_ScreenHeight / 2.3
        BoneOffsetY := -5
    }

    ; Calculate scan area with FOV
    ScanL := ZeroX - FOVRadius
    ScanT := ZeroY - FOVRadius
    ScanR := ZeroX + FOVRadius
    ScanB := ZeroY + FOVRadius

    targetFound := false
    bestTarget := {x: 0, y: 0, distance: 999999, priority: 0}

    ; Multi-color scanning for different enemy types
    colors := [EnemyColor, EnemyColor2, ZombieColor, BossColor]
    priorities := [3, 3, 2, 5] ; Boss has highest priority

    Loop, % colors.Length() {
        currentColor := colors[A_Index]
        currentPriority := priorities[A_Index]

        ; Skip ally colors
        if (currentColor = AllyColor)
            continue

        PixelSearch, AimPixelX, AimPixelY, ScanL, ScanT, ScanR, ScanB, currentColor, ColorTolerance, Fast RGB
        if (!ErrorLevel) {
            ; Check if we should ignore large objectives
            GuiControlGet, IgnoreObjectives,, IgnoreObjectivesBox
            if (IgnoreObjectives) {
                targetWidth := Abs(AimPixelX - ZeroX) * 2
                targetHeight := Abs(AimPixelY - ZeroY) * 2
                if (targetWidth > MaxTargetSize || targetHeight > MaxTargetSize)
                    continue
            }

            ; Calculate distance and priority
            distance := Sqrt((AimPixelX - ZeroX)**2 + (AimPixelY - ZeroY)**2)

            ; Apply target priority logic
            GuiControlGet, TargetPriority,, TargetPriorityBox
            if (TargetPriority = "Smart") {
                score := currentPriority * 1000 - distance
                bestScore := bestTarget.priority * 1000 - bestTarget.distance
                if (score > bestScore) {
                    bestTarget.x := AimPixelX
                    bestTarget.y := AimPixelY
                    bestTarget.distance := distance
                    bestTarget.priority := currentPriority
                    targetFound := true
                }
            } else if (TargetPriority = "Closest") {
                if (distance < bestTarget.distance) {
                    bestTarget.x := AimPixelX
                    bestTarget.y := AimPixelY
                    bestTarget.distance := distance
                    bestTarget.priority := currentPriority
                    targetFound := true
                }
            } else if (TargetPriority = "Strongest") {
                if (currentPriority > bestTarget.priority) {
                    bestTarget.x := AimPixelX
                    bestTarget.y := AimPixelY
                    bestTarget.distance := distance
                    bestTarget.priority := currentPriority
                    targetFound := true
                }
            }
        }
    }

    ; Set final target with offsets
    if (targetFound) {
        targetX := bestTarget.x + OffsetX
        targetY := bestTarget.y + OffsetY + BoneOffsetY

        ; Update snap lines if enabled
        GuiControlGet, SnapLinesEnabled,, SnapLinesBox
        if (SnapLinesEnabled) {
            DrawSnapLine(targetX, targetY)
        }
    } else {
        ; Clear snap lines if no target
        ClearSnapLine()
    }
}

; ====== ADVANCED AIMING SYSTEM =======
ApplyAdvancedAiming() {
    global
    static prevTargetX := 0, prevTargetY := 0, lastAimTime := 0
    static velocityX := 0, velocityY := 0
    static lockedX := 0, lockedY := 0, lockTimer := 0

    currentTime := A_TickCount
    deltaTime := (currentTime - lastAimTime) / 1000.0

    ; Anti-switch protection
    if (AntiSwitchEnabled && lockTimer > 0) {
        targetX := lockedX
        targetY := lockedY
        lockTimer--
    } else if (AntiSwitchEnabled && targetFound) {
        lockedX := targetX
        lockedY := targetY
        GuiControlGet, LockTime,, LockTimeSlider
        lockTimer := LockTime
    }

    ; Movement prediction system
    if (PredictionEnabled && deltaTime > 0 && lastAimTime > 0) {
        ; Calculate velocity
        velocityX := (targetX - prevTargetX) / deltaTime
        velocityY := (targetY - prevTargetY) / deltaTime

        ; Apply prediction
        GuiControlGet, PredictionStrength,, PredictionStrengthSlider
        predictionMultiplier := PredictionStrength / 10.0

        predictedX := targetX + (velocityX * predictionMultiplier * deltaTime)
        predictedY := targetY + (velocityY * predictionMultiplier * deltaTime)

        ; Smooth prediction
        targetX := (targetX + predictedX) / 2
        targetY := (targetY + predictedY) / 2
    }

    ; Calculate aim movement
    ZeroX := A_ScreenWidth / 2.08
    ZeroY := A_ScreenHeight / 2.18

    aimX := targetX - ZeroX
    aimY := targetY - ZeroY

    ; Apply smoothness
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    smoothnessFactor := SmoothnessValue / 10.0

    moveX := Round(aimX * AimStrength * smoothnessFactor)
    moveY := Round(aimY * AimStrength * smoothnessFactor)

    ; Humanization system
    if (HumanizationEnabled) {
        GuiControlGet, HumanizationLevel,, HumanizationSlider

        ; Add natural jitter
        Random, jitterX, -HumanizationLevel, HumanizationLevel
        Random, jitterY, -HumanizationLevel, HumanizationLevel
        moveX += jitterX
        moveY += jitterY

        ; Limit maximum movement for natural feel
        maxMove := 8
        if (Abs(moveX) > maxMove)
            moveX := maxMove * (moveX / Abs(moveX))
        if (Abs(moveY) > maxMove)
            moveY := maxMove * (moveY / Abs(moveY))
    }

    ; Anti-detection randomization
    GuiControlGet, AntiDetection,, AntiRicochetBox
    if (AntiDetection) {
        Random, antiDetectX, -2, 2
        Random, antiDetectY, -2, 2
        moveX += antiDetectX
        moveY += antiDetectY
    }

    ; Apply recoil compensation
    GuiControlGet, AntiRecoilEnabled,, AntiRecoilBox
    if (AntiRecoilEnabled && GetKeyState("LButton", "P")) {
        GuiControlGet, RecoilStrength,, RecoilStrengthSlider
        recoilCompensation := RecoilStrength / 10.0
        moveY += recoilCompensation
    }

    ; Execute mouse movement
    if (Abs(moveX) > 1 || Abs(moveY) > 1) {
        DllCall("mouse_event", "uint", 1, "int", moveX, "int", moveY, "uint", 0, "int", 0)
    }

    ; Update tracking variables
    prevTargetX := targetX
    prevTargetY := targetY
    lastAimTime := currentTime
}

; ====== ADVANCED RAPID FIRE SYSTEM =======
HandleRapidFire() {
    global
    static lastShotTime := 0, burstCount := 0, inBurstDelay := false

    currentTime := A_TickCount
    GuiControlGet, RapidFireRate,, RapidFireRateSlider
    GuiControlGet, AdaptiveRapidFire,, AdaptiveRapidFireBox
    GuiControlGet, RapidFireBurst,, RapidFireBurstEdit

    ; Adaptive fire rate based on weapon detection
    if (AdaptiveRapidFire) {
        ; Detect weapon type by monitoring fire patterns
        static weaponType := "Auto"
        if (weaponType = "Semi")
            currentRate := RapidFireRate * 0.8
        else if (weaponType = "Burst")
            currentRate := RapidFireRate * 1.2
        else
            currentRate := RapidFireRate
    } else {
        currentRate := RapidFireRate
    }

    ; Handle burst mode
    if (RapidFireBurst > 1) {
        if (!inBurstDelay && burstCount < RapidFireBurst) {
            if (currentTime - lastShotTime >= currentRate) {
                ; Fire shot with advanced timing
                FireAdvancedShot()
                burstCount++
                lastShotTime := currentTime
            }
        } else if (burstCount >= RapidFireBurst) {
            ; Start burst delay
            inBurstDelay := true
            burstCount := 0
            SetTimer, ResetBurstDelay, %RapidFireBurstDelay%
        }
    } else {
        ; Single shot mode with humanization
        if (currentTime - lastShotTime >= currentRate) {
            FireAdvancedShot()
            lastShotTime := currentTime
        }
    }
}

FireAdvancedShot() {
    global
    ; Advanced shot with humanization
    Random, humanDelay, -3, 3
    Sleep, Abs(humanDelay)

    ; Simulate natural click pattern
    Click, Down
    Random, holdTime, 8, 15
    Sleep, holdTime
    Click, Up

    ; Add micro-jitter for realism
    Random, jitterX, -1, 1
    Random, jitterY, -1, 1
    DllCall("mouse_event", "uint", 1, "int", jitterX, "int", jitterY, "uint", 0, "int", 0)
}

ResetBurstDelay:
    inBurstDelay := false
    SetTimer, ResetBurstDelay, Off
return

; ====== ADVANCED SUPER JUMP SYSTEM =======
HandleSuperJump() {
    global
    static superJumpReady := true, lastJumpTime := 0

    if (!superJumpReady)
        return

    currentTime := A_TickCount
    GuiControlGet, SuperJumpHeight,, SuperJumpHeightSlider
    GuiControlGet, SuperJumpType,, SuperJumpTypeBox
    GuiControlGet, NoFallDamage,, NoFallDamageBox

    ; Apply no fall damage patch if enabled
    if (NoFallDamage) {
        ApplyNoFallDamagePatch()
    }

    ; Execute super jump based on type
    if (SuperJumpType = "Multi-Jump") {
        ; Multiple rapid jumps for maximum height
        Loop, %SuperJumpHeight% {
            Send, {Space down}
            Sleep, 15
            Send, {Space up}
            Sleep, 25

            ; Add slight randomization
            Random, jumpDelay, 20, 35
            Sleep, jumpDelay
        }
    } else if (SuperJumpType = "Hold Jump") {
        ; Extended jump hold with memory modification
        Send, {Space down}
        holdDuration := SuperJumpHeight * 100
        Sleep, holdDuration
        Send, {Space up}

        ; Apply velocity boost through memory if available
        ApplyVelocityBoost(SuperJumpHeight)

    } else if (SuperJumpType = "Boost Jump") {
        ; Single jump with velocity modification
        Send, {Space down}
        Sleep, 50
        Send, {Space up}

        ; Apply advanced velocity boost
        ApplyAdvancedVelocityBoost(SuperJumpHeight)
    }

    ; Set cooldown
    superJumpReady := false
    SetTimer, ResetSuperJump, %SuperJumpCooldown%
    lastJumpTime := currentTime
}

ApplyNoFallDamagePatch() {
    ; Memory patch to prevent fall damage (placeholder for actual memory modification)
    ; In a real implementation, this would modify game memory
    ; For now, we simulate the effect
    static fallDamagePatched := false

    if (!fallDamagePatched) {
        ; Simulate memory patching
        fallDamagePatched := true
        SetTimer, RestoreFallDamage, 5000  ; Restore after 5 seconds
    }
}

RestoreFallDamage:
    fallDamagePatched := false
    SetTimer, RestoreFallDamage, Off
return

ApplyVelocityBoost(multiplier) {
    ; Advanced velocity boost simulation
    ; In real implementation, this would modify player velocity in memory
    Loop, %multiplier% {
        ; Simulate upward momentum
        Send, {Space down}
        Sleep, 5
        Send, {Space up}
        Sleep, 10
    }
}

ApplyAdvancedVelocityBoost(multiplier) {
    ; Most advanced boost with memory modification simulation
    boostStrength := multiplier * 1.5

    ; Simulate memory-based velocity modification
    Loop, %boostStrength% {
        Send, {Space down}
        Sleep, 3
        Send, {Space up}
        Sleep, 8
    }

    ; Add forward momentum if moving
    if (GetKeyState("W", "P") || GetKeyState("A", "P") || GetKeyState("S", "P") || GetKeyState("D", "P")) {
        ; Simulate forward boost
        Sleep, 100
        Send, {Space down}
        Sleep, 20
        Send, {Space up}
    }
}

ResetSuperJump:
    superJumpReady := true
    SetTimer, ResetSuperJump, Off
return

; ====== ENHANCED AIMBOT FEATURES =======
IsOnTarget() {
    global
    if (!targetFound)
        return false

    ; Get current mouse position
    MouseGetPos, mouseX, mouseY

    ; Calculate distance from crosshair to target
    centerX := A_ScreenWidth / 2
    centerY := A_ScreenHeight / 2

    distance := Sqrt((mouseX - centerX)**2 + (mouseY - centerY)**2)
    targetDistance := Sqrt((targetX - centerX)**2 + (targetY - centerY)**2)

    ; Check if crosshair is close enough to target
    tolerance := 25  ; Pixel tolerance
    return (Abs(distance - targetDistance) <= tolerance)
}

HandleTriggerBot() {
    global
    static triggerReady := true, lastTriggerTime := 0

    if (!triggerReady)
        return

    currentTime := A_TickCount

    ; Add human-like reaction time
    GuiControlGet, ReactionTimeSimulation,, ReactionTimeSimulationBox
    if (ReactionTimeSimulation) {
        Random, reactionDelay, 50, 150
        Sleep, reactionDelay
    }

    ; Simulate natural trigger pull
    Click, Down
    Random, triggerHold, 25, 75
    Sleep, triggerHold
    Click, Up

    ; Set cooldown to prevent spam
    triggerReady := false
    SetTimer, ResetTriggerBot, 200
    lastTriggerTime := currentTime
}

ResetTriggerBot:
    triggerReady := true
    SetTimer, ResetTriggerBot, Off
return

; ====== ADVANCED PREDICTION SYSTEM =======
CalculateAdvancedPrediction() {
    global
    static targetHistory := []
    static velocityHistory := []

    if (!targetFound)
        return {x: 0, y: 0}

    ; Add current target to history
    currentTarget := {x: targetX, y: targetY, time: A_TickCount}
    targetHistory.Push(currentTarget)

    ; Keep only last 10 positions
    if (targetHistory.Length() > 10)
        targetHistory.RemoveAt(1)

    if (targetHistory.Length() < 3)
        return {x: targetX, y: targetY}

    ; Calculate velocity and acceleration
    recent := targetHistory[targetHistory.Length()]
    previous := targetHistory[targetHistory.Length() - 1]
    older := targetHistory[targetHistory.Length() - 2]

    ; Current velocity
    deltaTime1 := (recent.time - previous.time) / 1000.0
    velocityX := (recent.x - previous.x) / deltaTime1
    velocityY := (recent.y - previous.y) / deltaTime1

    ; Previous velocity for acceleration
    deltaTime2 := (previous.time - older.time) / 1000.0
    prevVelocityX := (previous.x - older.x) / deltaTime2
    prevVelocityY := (previous.y - older.y) / deltaTime2

    ; Acceleration
    accelerationX := (velocityX - prevVelocityX) / deltaTime1
    accelerationY := (velocityY - prevVelocityY) / deltaTime1

    ; Prediction time based on distance
    distance := Sqrt((targetX - A_ScreenWidth/2)**2 + (targetY - A_ScreenHeight/2)**2)
    predictionTime := distance / 1000.0  ; Adjust based on weapon speed

    ; Apply prediction with acceleration
    GuiControlGet, TargetLeadPrediction,, TargetLeadPredictionBox
    if (TargetLeadPrediction) {
        predictedX := targetX + (velocityX * predictionTime) + (0.5 * accelerationX * predictionTime**2)
        predictedY := targetY + (velocityY * predictionTime) + (0.5 * accelerationY * predictionTime**2)
    } else {
        predictedX := targetX + (velocityX * predictionTime * 0.5)
        predictedY := targetY + (velocityY * predictionTime * 0.5)
    }

    ; Smooth prediction to avoid jitter
    smoothingFactor := 0.7
    finalX := (targetX * (1 - smoothingFactor)) + (predictedX * smoothingFactor)
    finalY := (targetY * (1 - smoothingFactor)) + (predictedY * smoothingFactor)

    return {x: finalX, y: finalY}
}

; ====== HUMANIZATION ENHANCEMENTS =======
ApplyAdvancedHumanization(moveX, moveY) {
    global
    static fatigueLevel := 0
    static aimingTime := 0

    ; Simulate fatigue over time
    GuiControlGet, FatigueSimulation,, FatigueSimulationBox
    if (FatigueSimulation) {
        aimingTime++
        if (aimingTime > 1000) {  ; After 10 seconds of aiming
            fatigueLevel := Min(fatigueLevel + 0.1, 2.0)
            Random, fatigueJitter, -fatigueLevel, fatigueLevel
            moveX += fatigueJitter
            moveY += fatigueJitter
        }
    }

    ; Intentional miss chance for realism
    GuiControlGet, MissChance,, MissChanceSlider
    Random, missRoll, 1, 100
    if (missRoll <= MissChance) {
        Random, missX, -10, 10
        Random, missY, -10, 10
        moveX += missX
        moveY += missY
    }

    ; Natural mouse acceleration curves
    GuiControlGet, MouseAcceleration,, MouseAccelerationBox
    if (MouseAcceleration) {
        ; Apply acceleration curve based on movement distance
        distance := Sqrt(moveX**2 + moveY**2)
        if (distance > 50) {
            ; Large movements get acceleration
            accelerationFactor := 1.2
            moveX *= accelerationFactor
            moveY *= accelerationFactor
        } else if (distance < 10) {
            ; Small movements get deceleration for precision
            decelerationFactor := 0.8
            moveX *= decelerationFactor
            moveY *= decelerationFactor
        }
    }

    ; Randomized aiming patterns
    GuiControlGet, AimingPatterns,, AimingPatternsBox
    if (AimingPatterns) {
        static patternPhase := 0
        patternPhase += 0.1

        ; Add subtle circular pattern to mimic human micro-adjustments
        patternX := Sin(patternPhase) * 0.5
        patternY := Cos(patternPhase * 1.3) * 0.3

        moveX += patternX
        moveY += patternY
    }

    return {x: moveX, y: moveY}
}

; ====== VISUAL ENHANCEMENT FUNCTIONS =======
DrawSnapLine(targetX, targetY) {
    global
    static hwndSnapLine := 0

    ; Get screen center
    centerX := A_ScreenWidth / 2
    centerY := A_ScreenHeight / 2

    ; Calculate window dimensions
    x1 := centerX
    y1 := centerY
    x2 := targetX
    y2 := targetY

    xPos := Min(x1, x2) - 5
    yPos := Min(y1, y2) - 5
    width := Abs(x2 - x1) + 10
    height := Abs(y2 - y1) + 10

    ; Ensure minimum dimensions
    width := Max(width, 10)
    height := Max(height, 10)

    ; Destroy previous window
    if (hwndSnapLine) {
        Gui, SnapLine:Destroy
        hwndSnapLine := 0
    }

    ; Create transparent window with anti-ricochet protection
    Gui, SnapLine:+LastFound +AlwaysOnTop -Caption +ToolWindow +E0x80000 +E0x20
    Gui, SnapLine:Color, 000000
    WinSet, Transparent, 255
    WinSet, ExStyle, +0x20
    WinSet, TransColor, 000000

    hwndSnapLine := WinExist()
    DllCall("user32.dll\SetWindowDisplayAffinity", "ptr", hwndSnapLine, "uint", 0x00000011)
    WinSet, ExStyle, +0x00200000, ahk_id %hwndSnapLine%

    ; Show window
    Gui, SnapLine:Show, w%width% h%height% x%xPos% y%yPos% NA

    ; Draw line using GDI+
    hdc := GetDC(hwndSnapLine)
    graphics := Gdip_GraphicsFromHDC(hdc)
    Gdip_SetSmoothingMode(graphics, 4)

    ; Get snap line color
    GuiControlGet, SnapColor,, SnapColorInput
    if (SnapColor = "")
        SnapColor := "FF0000"

    pen := Gdip_CreatePen("0xFF" . SnapColor, 2)

    ; Adjust coordinates relative to window
    relX1 := x1 - xPos
    relY1 := y1 - yPos
    relX2 := x2 - xPos
    relY2 := y2 - yPos

    Gdip_DrawLine(graphics, pen, relX1, relY1, relX2, relY2)

    ; Cleanup
    Gdip_DeletePen(pen)
    Gdip_DeleteGraphics(graphics)
    ReleaseDC(hdc, hwndSnapLine)
}

ClearSnapLine() {
    Gui, SnapLine:Destroy
}

DrawFOVCircle() {
    global
    static hwndFOV := 0

    ; Get FOV circle settings
    GuiControlGet, FOVCircleEnabled,, FOVCircleBox
    if (!FOVCircleEnabled)
        return

    centerX := A_ScreenWidth / 2
    centerY := A_ScreenHeight / 2

    ; Calculate circle position
    xPos := centerX - FOVRadius
    yPos := centerY - FOVRadius
    diameter := FOVRadius * 2

    ; Destroy previous circle
    if (hwndFOV) {
        Gui, FOV:Destroy
        hwndFOV := 0
    }

    ; Create transparent window with anti-ricochet protection
    Gui, FOV:+LastFound +AlwaysOnTop -Caption +E0x20 +ToolWindow
    Gui, FOV:Color, 000000
    WinSet, ExStyle, +0x20
    WinSet, TransColor, 000000 255

    hwndFOV := WinExist()
    DllCall("user32.dll\SetWindowDisplayAffinity", "ptr", hwndFOV, "uint", 0x00000011)
    WinSet, ExStyle, +0x00200000, ahk_id %hwndFOV%

    ; Show window
    Gui, FOV:Show, w%diameter% h%diameter% x%xPos% y%yPos% NA

    ; Draw circle using GDI+
    hdc := GetDC(hwndFOV)
    graphics := Gdip_GraphicsFromHDC(hdc)
    Gdip_SetSmoothingMode(graphics, 4)

    ; Get FOV color
    GuiControlGet, FOVColor,, FOVColorInput
    if (FOVColor = "")
        FOVColor := "00FF00"

    pen := Gdip_CreatePen("0xFF" . FOVColor, 2)
    Gdip_DrawEllipse(graphics, pen, 0, 0, diameter-2, diameter-2)

    ; Cleanup
    Gdip_DeletePen(pen)
    Gdip_DeleteGraphics(graphics)
    ReleaseDC(hdc, hwndFOV)
}

ClearFOVCircle() {
    Gui, FOV:Destroy
}

; ====== GUI UPDATE FUNCTIONS =======
UpdateLiveValues:
    ; Update all live text displays
    GuiControlGet, AimStrengthValue,, AimStrengthSlider
    AimStrength := AimStrengthValue / 100.0
    GuiControl,, AimStrengthText, Aim Strength: %AimStrength%

    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    Smoothness := SmoothnessValue / 10.0
    GuiControl,, SmoothnessText, Smoothness: %Smoothness%

    GuiControlGet, FOVValue,, FOVSlider
    FOVRadius := FOVValue
    GuiControl,, FOVText, FOV Radius: %FOVRadius% px

    GuiControlGet, MaxDistValue,, MaxDistanceSlider
    MaxAimDistance := MaxDistValue
    GuiControl,, MaxDistanceText, Max Distance: %MaxAimDistance% px

    GuiControlGet, PredStrengthValue,, PredictionStrengthSlider
    PredictionStrength := PredStrengthValue / 10.0
    GuiControl,, PredictionStrengthText, Prediction Strength: %PredictionStrength%

    GuiControlGet, HumanizationValue,, HumanizationSlider
    HumanizationJitter := HumanizationValue
    GuiControl,, HumanizationText, Humanization Level: %HumanizationJitter%

    GuiControlGet, LockTimeValue,, LockTimeSlider
    GuiControl,, LockTimeText, Target Lock Duration: %LockTimeValue% frames

    GuiControlGet, RecoilValue,, RecoilStrengthSlider
    RecoilStrength := RecoilValue / 10.0
    GuiControl,, RecoilStrengthText, Recoil Compensation: %RecoilStrength%

    GuiControlGet, OffsetXValue,, OffsetXSlider
    OffsetX := OffsetXValue
    GuiControl,, OffsetXText, X Offset: %OffsetX%

    GuiControlGet, OffsetYValue,, OffsetYSlider
    OffsetY := OffsetYValue
    GuiControl,, OffsetYText, Y Offset: %OffsetY%

    GuiControlGet, ScanFreqValue,, ScanFrequencySlider
    ScanFrequency := ScanFreqValue
    GuiControl,, ScanFrequencyText, Scan Frequency: %ScanFrequency% Hz

    GuiControlGet, TransparencyValue,, TransparencySlider
    WinSet, Transparent, %TransparencyValue%
    GuiControl,, TransparencyText, Interface Transparency: %TransparencyValue%

    GuiControlGet, ColorTolValue,, ColorToleranceSlider
    ColorTolerance := ColorTolValue
return

; ====== RAINBOW HEADER ANIMATION =======
UpdateRainbowColor:
    GuiControlGet, RainbowEnabled,, RainbowHeaderBox
    if (!RainbowEnabled)
        return

    hue := Mod(hue + 3, 360)
    newColor := HSVtoRGB(hue, saturation, brightness)
    GuiControl, % "+c" . newColor, RainbowText
return

HSVtoRGB(h, s, v) {
    h := Mod(h, 360)
    s := s/255
    v := v/255

    if (s = 0) {
        r := v, g := v, b := v
    } else {
        h := h/60
        i := Floor(h)
        f := h - i
        p := v * (1 - s)
        q := v * (1 - s * f)
        t := v * (1 - s * (1 - f))

        if (i = 0) {
            r := v, g := t, b := p
        } else if (i = 1) {
            r := q, g := v, b := p
        } else if (i = 2) {
            r := p, g := v, b := t
        } else if (i = 3) {
            r := p, g := q, b := v
        } else if (i = 4) {
            r := t, g := p, b := v
        } else {
            r := v, g := p, b := q
        }
    }

    r := Round(r * 255)
    g := Round(g * 255)
    b := Round(b * 255)

    return Format("0x{:02X}{:02X}{:02X}", r, g, b)
}

; ====== GUI EVENT HANDLERS =======
ToggleAimbot:
    GuiControlGet, AimbotEnabled,, AimbotEnabledBox
    if (AimbotEnabled) {
        ; Enable dependent controls
        GuiControl, Enable, HipFireBox
        GuiControl, Enable, PredictionBox
        GuiControl, Enable, HumanizationBox
        GuiControl, Enable, AntiSwitchBox
    } else {
        ; Disable dependent controls
        GuiControl, Disable, HipFireBox
        GuiControl, Disable, PredictionBox
        GuiControl, Disable, HumanizationBox
        GuiControl, Disable, AntiSwitchBox

        ; Clear visual elements
        ClearSnapLine()
        ClearFOVCircle()
    }
return

ToggleFOVCircle:
    GuiControlGet, FOVCircleEnabled,, FOVCircleBox
    if (FOVCircleEnabled) {
        DrawFOVCircle()
        SetTimer, DrawFOVCircle, 100
    } else {
        SetTimer, DrawFOVCircle, Off
        ClearFOVCircle()
    }
return

ToggleSnapLines:
    GuiControlGet, SnapLinesEnabled,, SnapLinesBox
    if (!SnapLinesEnabled) {
        ClearSnapLine()
    }
return

TogglePrediction:
    GuiControlGet, PredictionEnabled,, PredictionBox
    ; Prediction logic is handled in main loop
return

ToggleHumanization:
    GuiControlGet, HumanizationEnabled,, HumanizationBox
    ; Humanization logic is handled in main loop
return

ToggleAntiRecoil:
    GuiControlGet, AntiRecoilEnabled,, AntiRecoilBox
    ; Anti-recoil logic is handled in main loop
return

; ====== COMBAT FEATURE HANDLERS =======
ToggleRapidFire:
    GuiControlGet, RapidFireEnabled,, RapidFireBox
    if (RapidFireEnabled) {
        TrayTip, MW3 Ultimate Aimbot, Rapid Fire System ENABLED, 2, 1
    } else {
        TrayTip, MW3 Ultimate Aimbot, Rapid Fire System DISABLED, 2, 1
    }
return

ToggleSuperJump:
    GuiControlGet, SuperJumpEnabled,, SuperJumpBox
    if (SuperJumpEnabled) {
        TrayTip, MW3 Ultimate Aimbot, Super Jump System ENABLED, 2, 1
    } else {
        TrayTip, MW3 Ultimate Aimbot, Super Jump System DISABLED, 2, 1
    }
return

ToggleTriggerBot:
    GuiControlGet, TriggerBotEnabled,, TriggerBotBox
    if (TriggerBotEnabled) {
        TrayTip, MW3 Ultimate Aimbot, Trigger Bot ENABLED - Auto-shoot when on target, 2, 1
    } else {
        TrayTip, MW3 Ultimate Aimbot, Trigger Bot DISABLED, 2, 1
    }
return

UpdateRapidFireRate:
    GuiControlGet, RapidFireRate,, RapidFireRateSlider
    GuiControl,, RapidFireRateText, Fire Rate: %RapidFireRate% ms
return

UpdateSuperJumpHeight:
    GuiControlGet, SuperJumpHeight,, SuperJumpHeightSlider
    GuiControl,, SuperJumpHeightText, Jump Height: %SuperJumpHeight%x
return

UpdateAimLockDuration:
    GuiControlGet, AimLockDuration,, AimLockDurationSlider
    GuiControl,, AimLockDurationText, Aim Lock Duration: %AimLockDuration% ms
return

UpdateAimBone:
    GuiControlGet, AimBone,, AimBoneBox
    ; Bone selection is handled in main loop
return

UpdateAimStrength:
    ; Handled in UpdateLiveValues
return

UpdateSmoothness:
    ; Handled in UpdateLiveValues
return

UpdateFOV:
    ; Handled in UpdateLiveValues
    GuiControlGet, FOVCircleEnabled,, FOVCircleBox
    if (FOVCircleEnabled) {
        DrawFOVCircle()
    }
return

UpdateMaxDistance:
    ; Handled in UpdateLiveValues
return

UpdatePredictionStrength:
    ; Handled in UpdateLiveValues
return

UpdateHumanization:
    ; Handled in UpdateLiveValues
return

UpdateLockTime:
    ; Handled in UpdateLiveValues
return

UpdateRecoilStrength:
    ; Handled in UpdateLiveValues
return

UpdateOffsets:
    ; Handled in UpdateLiveValues
return

UpdateScanFrequency:
    ; Handled in UpdateLiveValues
return

UpdateTransparency:
    ; Handled in UpdateLiveValues
return

UpdateColorTolerance:
    ; Handled in UpdateLiveValues
return

UpdateFOVColor:
    GuiControlGet, newColor,, FOVColorInput
    if (RegExMatch(newColor, "^[0-9A-Fa-f]{6}$")) {
        FOVCircleColor := "0xFF" . newColor
        GuiControlGet, FOVCircleEnabled,, FOVCircleBox
        if (FOVCircleEnabled)
            DrawFOVCircle()
    } else {
        MsgBox, 0x10, Invalid Color, Please enter a valid 6-digit hex color code (e.g., 00FF00)
    }
return

UpdateSnapColor:
    GuiControlGet, newColor,, SnapColorInput
    if (RegExMatch(newColor, "^[0-9A-Fa-f]{6}$")) {
        SnapLineColor := "0xFF" . newColor
    } else {
        MsgBox, 0x10, Invalid Color, Please enter a valid 6-digit hex color code (e.g., FF0000)
    }
return

UpdateEnemyColor:
    GuiControlGet, newColor,, EnemyColorInput
    if (RegExMatch(newColor, "^[0-9A-Fa-f]{6}$")) {
        EnemyColor := "0x" . newColor
        MsgBox, 0x40, Color Updated, Primary enemy color updated successfully!
    } else {
        MsgBox, 0x10, Invalid Color, Please enter a valid 6-digit hex color code (e.g., DF00FF)
    }
return

UpdateEnemyColor2:
    GuiControlGet, newColor,, EnemyColor2Input
    if (RegExMatch(newColor, "^[0-9A-Fa-f]{6}$")) {
        EnemyColor2 := "0x" . newColor
        MsgBox, 0x40, Color Updated, Secondary enemy color updated successfully!
    } else {
        MsgBox, 0x10, Invalid Color, Please enter a valid 6-digit hex color code (e.g., FF0000)
    }
return

; ====== CONFIGURATION MANAGEMENT =======
UpdateConfigList() {
    global configFolder
    configs := ""
    Loop, %configFolder%*.ini {
        SplitPath, A_LoopFileName,,,, nameNoExt
        configs .= nameNoExt . "|"
    }
    GuiControl,, ConfigDropdown, |%configs%
}

SaveConfig:
    GuiControlGet, ConfigName,, ConfigNameEdit
    if (ConfigName = "" || ConfigName = "Enter Config Name") {
        MsgBox, 0x10, Invalid Name, Please enter a valid configuration name.
        return
    }

    ; Get all current settings
    GuiControlGet, AimbotEnabled,, AimbotEnabledBox
    GuiControlGet, HipFireMode,, HipFireBox
    GuiControlGet, PredictionEnabled,, PredictionBox
    GuiControlGet, HumanizationEnabled,, HumanizationBox
    GuiControlGet, AntiSwitchEnabled,, AntiSwitchBox
    GuiControlGet, AntiRecoilEnabled,, AntiRecoilBox
    GuiControlGet, FOVCircleEnabled,, FOVCircleBox
    GuiControlGet, SnapLinesEnabled,, SnapLinesBox
    GuiControlGet, AimBone,, AimBoneBox
    GuiControlGet, TargetPriority,, TargetPriorityBox
    GuiControlGet, AimStrengthValue,, AimStrengthSlider
    GuiControlGet, SmoothnessValue,, SmoothnessSlider
    GuiControlGet, FOVValue,, FOVSlider
    GuiControlGet, MaxDistValue,, MaxDistanceSlider
    GuiControlGet, PredStrengthValue,, PredictionStrengthSlider
    GuiControlGet, HumanizationValue,, HumanizationSlider
    GuiControlGet, LockTimeValue,, LockTimeSlider
    GuiControlGet, RecoilValue,, RecoilStrengthSlider
    GuiControlGet, OffsetXValue,, OffsetXSlider
    GuiControlGet, OffsetYValue,, OffsetYSlider
    GuiControlGet, ScanFreqValue,, ScanFrequencySlider
    GuiControlGet, ColorTolValue,, ColorToleranceSlider
    GuiControlGet, FOVColor,, FOVColorInput
    GuiControlGet, SnapColor,, SnapColorInput
    GuiControlGet, EnemyColorValue,, EnemyColorInput
    GuiControlGet, EnemyColor2Value,, EnemyColor2Input

    ; Save to INI file
    configFile := configFolder . ConfigName . ".ini"
    IniWrite, %AimbotEnabled%, %configFile%, Core, AimbotEnabled
    IniWrite, %HipFireMode%, %configFile%, Core, HipFireMode
    IniWrite, %PredictionEnabled%, %configFile%, Core, PredictionEnabled
    IniWrite, %HumanizationEnabled%, %configFile%, Core, HumanizationEnabled
    IniWrite, %AntiSwitchEnabled%, %configFile%, Core, AntiSwitchEnabled
    IniWrite, %AntiRecoilEnabled%, %configFile%, Core, AntiRecoilEnabled
    IniWrite, %FOVCircleEnabled%, %configFile%, Visual, FOVCircleEnabled
    IniWrite, %SnapLinesEnabled%, %configFile%, Visual, SnapLinesEnabled
    IniWrite, %AimBone%, %configFile%, Targeting, AimBone
    IniWrite, %TargetPriority%, %configFile%, Targeting, TargetPriority
    IniWrite, %AimStrengthValue%, %configFile%, Settings, AimStrength
    IniWrite, %SmoothnessValue%, %configFile%, Settings, Smoothness
    IniWrite, %FOVValue%, %configFile%, Settings, FOVRadius
    IniWrite, %MaxDistValue%, %configFile%, Settings, MaxDistance
    IniWrite, %PredStrengthValue%, %configFile%, Settings, PredictionStrength
    IniWrite, %HumanizationValue%, %configFile%, Settings, HumanizationLevel
    IniWrite, %LockTimeValue%, %configFile%, Settings, LockTime
    IniWrite, %RecoilValue%, %configFile%, Settings, RecoilStrength
    IniWrite, %OffsetXValue%, %configFile%, Settings, OffsetX
    IniWrite, %OffsetYValue%, %configFile%, Settings, OffsetY
    IniWrite, %ScanFreqValue%, %configFile%, Settings, ScanFrequency
    IniWrite, %ColorTolValue%, %configFile%, Settings, ColorTolerance
    IniWrite, %FOVColor%, %configFile%, Colors, FOVColor
    IniWrite, %SnapColor%, %configFile%, Colors, SnapColor
    IniWrite, %EnemyColorValue%, %configFile%, Colors, EnemyColor
    IniWrite, %EnemyColor2Value%, %configFile%, Colors, EnemyColor2

    UpdateConfigList()
    MsgBox, 0x40, Success, Configuration "%ConfigName%" saved successfully!
return

LoadConfig:
    GuiControlGet, SelectedConfig,, ConfigDropdown
    if (SelectedConfig = "") {
        MsgBox, 0x10, No Selection, Please select a configuration to load.
        return
    }

    configFile := configFolder . SelectedConfig . ".ini"
    if !FileExist(configFile) {
        MsgBox, 0x10, File Not Found, Configuration file not found.
        return
    }

    ; Read all settings from INI file
    IniRead, AimbotEnabled, %configFile%, Core, AimbotEnabled, 0
    IniRead, HipFireMode, %configFile%, Core, HipFireMode, 0
    IniRead, PredictionEnabled, %configFile%, Core, PredictionEnabled, 1
    IniRead, HumanizationEnabled, %configFile%, Core, HumanizationEnabled, 1
    IniRead, AntiSwitchEnabled, %configFile%, Core, AntiSwitchEnabled, 1
    IniRead, AntiRecoilEnabled, %configFile%, Core, AntiRecoilEnabled, 0
    IniRead, FOVCircleEnabled, %configFile%, Visual, FOVCircleEnabled, 0
    IniRead, SnapLinesEnabled, %configFile%, Visual, SnapLinesEnabled, 0
    IniRead, AimBone, %configFile%, Targeting, AimBone, Head
    IniRead, TargetPriority, %configFile%, Targeting, TargetPriority, Smart
    IniRead, AimStrengthValue, %configFile%, Settings, AimStrength, 70
    IniRead, SmoothnessValue, %configFile%, Settings, Smoothness, 15
    IniRead, FOVValue, %configFile%, Settings, FOVRadius, 150
    IniRead, MaxDistValue, %configFile%, Settings, MaxDistance, 500
    IniRead, PredStrengthValue, %configFile%, Settings, PredictionStrength, 30
    IniRead, HumanizationValue, %configFile%, Settings, HumanizationLevel, 2
    IniRead, LockTimeValue, %configFile%, Settings, LockTime, 10
    IniRead, RecoilValue, %configFile%, Settings, RecoilStrength, 10
    IniRead, OffsetXValue, %configFile%, Settings, OffsetX, 0
    IniRead, OffsetYValue, %configFile%, Settings, OffsetY, 0
    IniRead, ScanFreqValue, %configFile%, Settings, ScanFrequency, 200
    IniRead, ColorTolValue, %configFile%, Settings, ColorTolerance, 25
    IniRead, FOVColor, %configFile%, Colors, FOVColor, 00FF00
    IniRead, SnapColor, %configFile%, Colors, SnapColor, FF0000
    IniRead, EnemyColorValue, %configFile%, Colors, EnemyColor, DF00FF
    IniRead, EnemyColor2Value, %configFile%, Colors, EnemyColor2, FF0000

    ; Apply all settings to GUI
    GuiControl,, AimbotEnabledBox, %AimbotEnabled%
    GuiControl,, HipFireBox, %HipFireMode%
    GuiControl,, PredictionBox, %PredictionEnabled%
    GuiControl,, HumanizationBox, %HumanizationEnabled%
    GuiControl,, AntiSwitchBox, %AntiSwitchEnabled%
    GuiControl,, AntiRecoilBox, %AntiRecoilEnabled%
    GuiControl,, FOVCircleBox, %FOVCircleEnabled%
    GuiControl,, SnapLinesBox, %SnapLinesEnabled%
    GuiControl, ChooseString, AimBoneBox, %AimBone%
    GuiControl, ChooseString, TargetPriorityBox, %TargetPriority%
    GuiControl,, AimStrengthSlider, %AimStrengthValue%
    GuiControl,, SmoothnessSlider, %SmoothnessValue%
    GuiControl,, FOVSlider, %FOVValue%
    GuiControl,, MaxDistanceSlider, %MaxDistValue%
    GuiControl,, PredictionStrengthSlider, %PredStrengthValue%
    GuiControl,, HumanizationSlider, %HumanizationValue%
    GuiControl,, LockTimeSlider, %LockTimeValue%
    GuiControl,, RecoilStrengthSlider, %RecoilValue%
    GuiControl,, OffsetXSlider, %OffsetXValue%
    GuiControl,, OffsetYSlider, %OffsetYValue%
    GuiControl,, ScanFrequencySlider, %ScanFreqValue%
    GuiControl,, ColorToleranceSlider, %ColorTolValue%
    GuiControl,, FOVColorInput, %FOVColor%
    GuiControl,, SnapColorInput, %SnapColor%
    GuiControl,, EnemyColorInput, %EnemyColorValue%
    GuiControl,, EnemyColor2Input, %EnemyColor2Value%

    ; Update global variables
    EnemyColor := "0x" . EnemyColorValue
    EnemyColor2 := "0x" . EnemyColor2Value

    ; Refresh visual elements if enabled
    if (FOVCircleEnabled)
        Gosub, ToggleFOVCircle

    MsgBox, 0x40, Success, Configuration "%SelectedConfig%" loaded successfully!
return

RefreshConfigs:
    UpdateConfigList()
return

; ====== PRESET CONFIGURATIONS =======
LoadLegitConfig:
    ; Legit settings - very human-like
    GuiControl,, AimbotEnabledBox, 1
    GuiControl,, HipFireBox, 0
    GuiControl,, PredictionBox, 1
    GuiControl,, HumanizationBox, 1
    GuiControl,, AntiSwitchBox, 1
    GuiControl,, AntiRecoilBox, 0
    GuiControl,, FOVCircleBox, 0
    GuiControl,, SnapLinesBox, 0
    GuiControl, ChooseString, AimBoneBox, Head
    GuiControl, ChooseString, TargetPriorityBox, Smart
    GuiControl,, AimStrengthSlider, 30
    GuiControl,, SmoothnessSlider, 25
    GuiControl,, FOVSlider, 80
    GuiControl,, MaxDistanceSlider, 300
    GuiControl,, PredictionStrengthSlider, 15
    GuiControl,, HumanizationSlider, 5
    GuiControl,, LockTimeSlider, 15
    GuiControl,, RecoilStrengthSlider, 5
    MsgBox, 0x40, Preset Loaded, Legit configuration applied - Very human-like settings
return

LoadRageConfig:
    ; Rage settings - maximum performance
    GuiControl,, AimbotEnabledBox, 1
    GuiControl,, HipFireBox, 1
    GuiControl,, PredictionBox, 1
    GuiControl,, HumanizationBox, 0
    GuiControl,, AntiSwitchBox, 1
    GuiControl,, AntiRecoilBox, 1
    GuiControl,, FOVCircleBox, 1
    GuiControl,, SnapLinesBox, 1
    GuiControl, ChooseString, AimBoneBox, Head
    GuiControl, ChooseString, TargetPriorityBox, Closest
    GuiControl,, AimStrengthSlider, 95
    GuiControl,, SmoothnessSlider, 5
    GuiControl,, FOVSlider, 250
    GuiControl,, MaxDistanceSlider, 700
    GuiControl,, PredictionStrengthSlider, 45
    GuiControl,, HumanizationSlider, 1
    GuiControl,, LockTimeSlider, 5
    GuiControl,, RecoilStrengthSlider, 80
    MsgBox, 0x40, Preset Loaded, Rage configuration applied - Maximum performance settings
return

LoadSilentConfig:
    ; Silent settings - balanced stealth
    GuiControl,, AimbotEnabledBox, 1
    GuiControl,, HipFireBox, 0
    GuiControl,, PredictionBox, 1
    GuiControl,, HumanizationBox, 1
    GuiControl,, AntiSwitchBox, 1
    GuiControl,, AntiRecoilBox, 0
    GuiControl,, FOVCircleBox, 0
    GuiControl,, SnapLinesBox, 0
    GuiControl, ChooseString, AimBoneBox, Chest
    GuiControl, ChooseString, TargetPriorityBox, Smart
    GuiControl,, AimStrengthSlider, 50
    GuiControl,, SmoothnessSlider, 18
    GuiControl,, FOVSlider, 120
    GuiControl,, MaxDistanceSlider, 400
    GuiControl,, PredictionStrengthSlider, 25
    GuiControl,, HumanizationSlider, 3
    GuiControl,, LockTimeSlider, 12
    GuiControl,, RecoilStrengthSlider, 15
    MsgBox, 0x40, Preset Loaded, Silent configuration applied - Balanced stealth settings
return

BackupConfigs:
    backupFolder := configFolder . "backup_" . A_Now . "\"
    FileCreateDir, %backupFolder%

    Loop, %configFolder%*.ini {
        FileCopy, %A_LoopFileFullPath%, %backupFolder%
    }

    MsgBox, 0x40, Backup Complete, All configurations backed up to:`n%backupFolder%
return

RestoreConfigs:
    MsgBox, 0x34, Restore Configs, This will restore configurations from a backup folder.`nDo you want to continue?
    IfMsgBox No
        return

    FileSelectFolder, restoreFolder, %configFolder%, 3, Select backup folder to restore from:
    if (restoreFolder = "")
        return

    Loop, %restoreFolder%\*.ini {
        FileCopy, %A_LoopFileFullPath%, %configFolder%, 1
    }

    UpdateConfigList()
    MsgBox, 0x40, Restore Complete, Configurations restored successfully!
return

; ====== SYSTEM FUNCTIONS =======
EmergencyStop:
    ; Immediately disable all features
    GuiControl,, AimbotEnabledBox, 0
    GuiControl,, HipFireBox, 0
    GuiControl,, PredictionBox, 0
    GuiControl,, HumanizationBox, 0
    GuiControl,, AntiSwitchBox, 0
    GuiControl,, AntiRecoilBox, 0
    GuiControl,, FOVCircleBox, 0
    GuiControl,, SnapLinesBox, 0

    ; Clear all visual elements
    ClearSnapLine()
    ClearFOVCircle()

    ; Stop all timers
    SetTimer, MainAimbotLoop, Off
    SetTimer, UpdateLiveValues, Off
    SetTimer, UpdateRainbowColor, Off
    SetTimer, DrawFOVCircle, Off

    MsgBox, 0x30, Emergency Stop, ALL FEATURES DISABLED!`nPress F12 to restart the aimbot system.
return

ReloadScript:
    Reload
return

GuiMove:
    PostMessage, 0xA1, 2, , , A
return

Close:
GuiClose:
    ; Clean up all resources
    ClearSnapLine()
    ClearFOVCircle()
    SetTimer, MainAimbotLoop, Off
    SetTimer, UpdateLiveValues, Off
    SetTimer, UpdateRainbowColor, Off
    SetTimer, DrawFOVCircle, Off
    Gdip_Shutdown(pToken)
    ExitApp
return

; ====== ESSENTIAL GDI+ FUNCTIONS =======
Gdip_Startup() {
    if !DllCall("GetModuleHandle", "str", "gdiplus")
        DllCall("LoadLibrary", "str", "gdiplus")
    VarSetCapacity(si, 16, 0), si := Chr(1)
    DllCall("gdiplus\GdiplusStartup", "uint*", pToken, "uint", &si, "uint", 0)
    return pToken
}

Gdip_Shutdown(pToken) {
    DllCall("gdiplus\GdiplusShutdown", "uint", pToken)
    if hModule := DllCall("GetModuleHandle", "str", "gdiplus")
        DllCall("FreeLibrary", "uint", hModule)
    return 0
}

GetDC(hwnd=0) {
    return DllCall("GetDC", "uint", hwnd)
}

ReleaseDC(hdc, hwnd=0) {
    return DllCall("ReleaseDC", "uint", hwnd, "uint", hdc)
}

Gdip_GraphicsFromHDC(hDC) {
    DllCall("gdiplus\GdipCreateFromHDC", "uint", hDC, "uint*", pGraphics)
    return pGraphics
}

Gdip_DeleteGraphics(pGraphics) {
    return DllCall("gdiplus\GdipDeleteGraphics", "uint", pGraphics)
}

Gdip_CreatePen(ARGB, w) {
    DllCall("gdiplus\GdipCreatePen1", "int", ARGB, "float", w, "int", 2, "uint*", pPen)
    return pPen
}

Gdip_DeletePen(pPen) {
    return DllCall("gdiplus\GdipDeletePen", "uint", pPen)
}

Gdip_DrawEllipse(pGraphics, pPen, x, y, w, h) {
    return DllCall("gdiplus\GdipDrawEllipse", "uint", pGraphics, "uint", pPen
    , "float", x, "float", y, "float", w, "float", h)
}

Gdip_SetSmoothingMode(pGraphics, SmoothingMode) {
    return DllCall("gdiplus\GdipSetSmoothingMode", "uint", pGraphics, "int", SmoothingMode)
}

Gdip_DrawLine(pGraphics, pPen, x1, y1, x2, y2) {
    return DllCall("gdiplus\GdipDrawLine", "uint", pGraphics, "uint", pPen
    , "float", x1, "float", y1, "float", x2, "float", y2)
}

; ====== HOTKEYS & SHORTCUTS =======
; Toggle GUI visibility
Insert::
    IfWinExist, MW3 Ultimate Aimbot v3.0 {
        Gui, Hide
    } else {
        Gui, Show
    }
return

; Emergency stop hotkey
F12::
    Gosub, EmergencyStop
    ; Restart system after emergency stop
    Sleep, 1000
    SetTimer, MainAimbotLoop, %UpdateRate%
    SetTimer, UpdateLiveValues, 10
    SetTimer, UpdateRainbowColor, 50
    MsgBox, 0x40, System Restarted, Aimbot system restarted. Configure settings as needed.
return

; Quick toggle aimbot
F1::
    GuiControlGet, currentState,, AimbotEnabledBox
    newState := !currentState
    GuiControl,, AimbotEnabledBox, %newState%
    Gosub, ToggleAimbot
    status := newState ? "ENABLED" : "DISABLED"
    TrayTip, MW3 Ultimate Aimbot, Aimbot %status%, 2, 1
return

; Quick toggle hip fire mode
F2::
    GuiControlGet, currentState,, HipFireBox
    newState := !currentState
    GuiControl,, HipFireBox, %newState%
    status := newState ? "ENABLED" : "DISABLED"
    TrayTip, MW3 Ultimate Aimbot, Hip Fire Mode %status%, 2, 1
return

; Quick toggle FOV circle
F3::
    GuiControlGet, currentState,, FOVCircleBox
    newState := !currentState
    GuiControl,, FOVCircleBox, %newState%
    Gosub, ToggleFOVCircle
    status := newState ? "ENABLED" : "DISABLED"
    TrayTip, MW3 Ultimate Aimbot, FOV Circle %status%, 2, 1
return

; Quick toggle snap lines
F4::
    GuiControlGet, currentState,, SnapLinesBox
    newState := !currentState
    GuiControl,, SnapLinesBox, %newState%
    Gosub, ToggleSnapLines
    status := newState ? "ENABLED" : "DISABLED"
    TrayTip, MW3 Ultimate Aimbot, Snap Lines %status%, 2, 1
return

; Cycle through aim bones
F5::
    GuiControlGet, currentBone,, AimBoneBox
    if (currentBone = "Head")
        newBone := "Chest"
    else if (currentBone = "Chest")
        newBone := "Auto"
    else
        newBone := "Head"

    GuiControl, ChooseString, AimBoneBox, %newBone%
    TrayTip, MW3 Ultimate Aimbot, Aim Bone: %newBone%, 2, 1
return

; Cycle through target priorities
F6::
    GuiControlGet, currentPriority,, TargetPriorityBox
    if (currentPriority = "Smart")
        newPriority := "Closest"
    else if (currentPriority = "Closest")
        newPriority := "Strongest"
    else
        newPriority := "Smart"

    GuiControl, ChooseString, TargetPriorityBox, %newPriority%
    TrayTip, MW3 Ultimate Aimbot, Target Priority: %newPriority%, 2, 1
return

; Combat feature toggles
F7::
    GuiControlGet, currentState,, RapidFireBox
    newState := !currentState
    GuiControl,, RapidFireBox, %newState%
    Gosub, ToggleRapidFire
return

F8::
    GuiControlGet, currentState,, SuperJumpBox
    newState := !currentState
    GuiControl,, SuperJumpBox, %newState%
    Gosub, ToggleSuperJump
return

F9::
    GuiControlGet, currentState,, TriggerBotBox
    newState := !currentState
    GuiControl,, TriggerBotBox, %newState%
    Gosub, ToggleTriggerBot
return

; Quick load preset configs (moved to F10, Ctrl+F7-F9)
F10::
    MsgBox, 0x40, Quick Configs,
    (
    QUICK CONFIG HOTKEYS:

    Ctrl+F7 = Load Legit Config
    Ctrl+F8 = Load Rage Config
    Ctrl+F9 = Load Silent Config

    COMBAT HOTKEYS:
    F7 = Toggle Rapid Fire
    F8 = Toggle Super Jump
    F9 = Toggle Trigger Bot
    )
return

^F7::
    Gosub, LoadLegitConfig
    TrayTip, MW3 Ultimate Aimbot, Legit Config Loaded, 2, 1
return

^F8::
    Gosub, LoadRageConfig
    TrayTip, MW3 Ultimate Aimbot, Rage Config Loaded, 2, 1
return

^F9::
    Gosub, LoadSilentConfig
    TrayTip, MW3 Ultimate Aimbot, Silent Config Loaded, 2, 1
return

; Show help information
F11::
    MsgBox, 0x40, MW3 Ultimate Aimbot v3.0 - Help,
    (
    MW3/MWZ ULTIMATE AIMBOT SYSTEM v3.0
    =====================================

    HOTKEYS:
    F1  - Toggle Aimbot On/Off
    F2  - Toggle Hip Fire Mode
    F3  - Toggle FOV Circle
    F4  - Toggle Snap Lines
    F5  - Cycle Aim Bone (Head/Chest/Auto)
    F6  - Cycle Target Priority
    F7  - Toggle Rapid Fire System
    F8  - Toggle Super Jump System
    F9  - Toggle Trigger Bot
    F10 - Show Config Hotkeys
    F11 - Show This Help
    F12 - Emergency Stop/Restart
    Insert - Toggle GUI Visibility

    CONFIG HOTKEYS:
    Ctrl+F7 - Load Legit Config
    Ctrl+F8 - Load Rage Config
    Ctrl+F9 - Load Silent Config

    FEATURES:
    • Color-Based Detection with multiple enemy types
    • Advanced Movement Prediction with velocity tracking
    • Human-like movement with micro-adjustments
    • Anti-Detection with randomization patterns
    • Smart Target Filtering (ignores objectives/allies)
    • Professional GUI with Windows 11 styling
    • Unlimited configuration save/load system
    • Real-time visual indicators (FOV circle, snap lines)
    • Anti-Ricochet protection (hidden from screenshots)
    • Weapon-specific recoil compensation

    COMBAT FEATURES:
    • Rapid Fire System with burst mode and adaptive rates
    • Super Jump System with no fall damage protection
    • Advanced Trigger Bot with reaction time simulation
    • Aim Lock with customizable duration
    • Silent Aim (invisible crosshair movement)
    • Memory-based enhancements for super jump
    • Humanization with fatigue simulation
    • Intentional miss chance for realism

    SAFETY:
    • Use conservative settings for legit gameplay
    • Test in single-player before going online
    • Emergency stop available with F12
    • All protection systems active by default

    VERSION: 3.0 ULTIMATE EDITION
    Based on advanced BO6 techniques
    )
return

; ====== STARTUP MESSAGE =======
; Show startup notification
TrayTip, MW3 Ultimate Aimbot v3.0,
(
ULTIMATE AIMBOT SYSTEM LOADED!

• All advanced features enabled
• Anti-Ricochet protection active
• Press F11 for help and hotkeys
• Press Insert to toggle GUI
• Configure settings in the modern interface

Ready for MW3/MWZ!
), 5, 1
